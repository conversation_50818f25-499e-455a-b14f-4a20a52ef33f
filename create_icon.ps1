# إنشاء أيقونة Atlas Medical Supplies الاحترافية
Write-Host "🎨 إنشاء أيقونة Atlas Medical Supplies الاحترافية..." -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python موجود: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Write-Host "يرجى تثبيت Python من https://python.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود Pillow
try {
    python -c "import PIL" 2>$null
    Write-Host "✅ مكتبة Pillow مثبتة" -ForegroundColor Green
} catch {
    Write-Host "📦 تثبيت مكتبة Pillow..." -ForegroundColor Yellow
    try {
        pip install Pillow
        Write-Host "✅ تم تثبيت Pillow بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "❌ فشل في تثبيت Pillow" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}

Write-Host "🎨 تشغيل سكريبت إنشاء الأيقونة..." -ForegroundColor Cyan
try {
    python create_atlas_icon.py
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في تشغيل السكريبت"
    }
} catch {
    Write-Host "❌ فشل في إنشاء الأيقونة: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "📱 إنشاء أيقونات التطبيق..." -ForegroundColor Cyan
try {
    flutter pub get
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في flutter pub get"
    }
    
    flutter pub run flutter_launcher_icons
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في flutter pub run flutter_launcher_icons"
    }
} catch {
    Write-Host "❌ فشل في إنشاء أيقونات التطبيق: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "✅ تم إنشاء أيقونة التطبيق بنجاح!" -ForegroundColor Green
Write-Host "🎉 يمكنك الآن بناء التطبيق وستظهر الأيقونة الجديدة" -ForegroundColor Cyan
Read-Host "اضغط Enter للخروج" 