# سكريبت تشغيل تطبيق Atlas Medical Supplies
Write-Host "🚀 بدء تشغيل تطبيق Atlas Medical Supplies..." -ForegroundColor Green

# التحقق من وجود Flutter
if (!(Get-Command flutter -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Flutter غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    exit 1
}

# التحقق من وجود الأجهزة المتصلة
Write-Host "📱 البحث عن الأجهزة المتصلة..." -ForegroundColor Yellow
flutter devices

# تشغيل التطبيق
Write-Host "⚡ تشغيل التطبيق..." -ForegroundColor Cyan
flutter run

Write-Host "✅ تم تشغيل التطبيق بنجاح!" -ForegroundColor Green 