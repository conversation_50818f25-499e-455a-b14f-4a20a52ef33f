import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/database/database_helper.dart';
import 'package:atlas_medical_supplies/services/auth_service.dart';
import 'package:atlas_medical_supplies/services/performance_service.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:io';

void main() {
  group('اكتشاف المشاكل', () {
    late DatabaseHelper databaseHelper;

    setUpAll(() async {
      databaseHelper = DatabaseHelper();
      await databaseHelper.database;
    });

    test('فحص قاعدة البيانات', () async {
      try {
        final db = await databaseHelper.database;

        // فحص الجداول
        final tables = await db.query(
          'sqlite_master',
          where: 'type = ?',
          whereArgs: ['table'],
        );
        print('عدد الجداول: ${tables.length}');

        // فحص حجم قاعدة البيانات
        final dbPath = join(await getDatabasesPath(), 'atlas_medical.db');
        final dbFile = File(dbPath);
        if (await dbFile.exists()) {
          final size = await dbFile.length();
          final sizeMB = size / 1024 / 1024;
          print('حجم قاعدة البيانات: ${sizeMB.toStringAsFixed(2)} MB');

          if (sizeMB > 50) {
            print('تحذير: قاعدة البيانات كبيرة جداً');
          }
        }

        expect(tables.length, greaterThan(0));
      } catch (e) {
        print('خطأ في فحص قاعدة البيانات: $e');
        fail('فشل في فحص قاعدة البيانات');
      }
    });

    test('فحص المستخدمين', () async {
      try {
        final users = await databaseHelper.getUsers();
        print('عدد المستخدمين: ${users.length}');

        if (users.isEmpty) {
          print('تحذير: لا يوجد مستخدمين');
        }

        expect(users.length, greaterThanOrEqualTo(0));
      } catch (e) {
        print('خطأ في فحص المستخدمين: $e');
        fail('فشل في فحص المستخدمين');
      }
    });

    test('فحص الأداء', () async {
      try {
        final isFirstLaunch = await PerformanceService.isFirstLaunch();
        final needsBackup = await PerformanceService.needsBackupRestore();

        print('أول تشغيل: $isFirstLaunch');
        print('يحتاج نسخة احتياطية: $needsBackup');

        expect(isFirstLaunch, isA<bool>());
        expect(needsBackup, isA<bool>());
      } catch (e) {
        print('خطأ في فحص الأداء: $e');
        fail('فشل في فحص الأداء');
      }
    });
  });
}
