import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../database/database_helper.dart';

class StorageService {
  static const String _appDataFileName = 'atlas_medical_data.json';
  static const String _backupFileName = 'atlas_backup_data.json';
  static const String _logsFileName = 'atlas_logs.txt';
  
  // طلب أذونات التخزين
  static Future<bool> requestStoragePermissions() async {
    try {
      // طلب أذونات التخزين
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
        Permission.manageExternalStorage,
      ].request();

      // التحقق من حالة الأذونات
      bool hasStoragePermission = statuses[Permission.storage]?.isGranted ?? false;
      bool hasManageStoragePermission = statuses[Permission.manageExternalStorage]?.isGranted ?? false;

      print('📱 حالة أذونات التخزين:');
      print('   - Storage: ${statuses[Permission.storage]}');
      print('   - Manage External Storage: ${statuses[Permission.manageExternalStorage]}');

      return hasStoragePermission || hasManageStoragePermission;
    } catch (e) {
      print('❌ خطأ في طلب أذونات التخزين: $e');
      return false;
    }
  }

  // التحقق من وجود أذونات التخزين
  static Future<bool> hasStoragePermissions() async {
    try {
      bool hasStoragePermission = await Permission.storage.isGranted;
      bool hasManageStoragePermission = await Permission.manageExternalStorage.isGranted;
      
      return hasStoragePermission || hasManageStoragePermission;
    } catch (e) {
      print('❌ خطأ في التحقق من أذونات التخزين: $e');
      return false;
    }
  }

  // الحصول على مجلد التخزين الخارجي
  static Future<Directory?> getExternalStorageDirectory() async {
    try {
      // محاولة الحصول على مجلد التخزين الخارجي
      Directory? externalDir = await getExternalStorageDirectory();
      
      if (externalDir != null && await externalDir.exists()) {
        return externalDir;
      }

      // إذا لم يتوفر التخزين الخارجي، استخدم مجلد المستندات
      Directory documentsDir = await getApplicationDocumentsDirectory();
      return documentsDir;
    } catch (e) {
      print('❌ خطأ في الحصول على مجلد التخزين: $e');
      return null;
    }
  }

  // حفظ جميع بيانات التطبيق
  static Future<Map<String, dynamic>> saveAllAppData() async {
    try {
      // التحقق من الأذونات
      if (!await hasStoragePermissions()) {
        bool granted = await requestStoragePermissions();
        if (!granted) {
          return {
            'success': false,
            'message': 'لم يتم منح أذونات التخزين المطلوبة',
          };
        }
      }

      // الحصول على مجلد التخزين
      Directory? storageDir = await getExternalStorageDirectory();
      if (storageDir == null) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول إلى مجلد التخزين',
        };
      }

      // إنشاء مجلد Atlas إذا لم يكن موجود
      Directory atlasDir = Directory(path.join(storageDir.path, 'AtlasMedical'));
      if (!await atlasDir.exists()) {
        await atlasDir.create(recursive: true);
      }

      // جمع البيانات من قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final appData = await _collectAllAppData(dbHelper);

      // حفظ البيانات في ملف JSON
      String filePath = path.join(atlasDir.path, _appDataFileName);
      File dataFile = File(filePath);
      await dataFile.writeAsString(json.encode(appData));

      // إنشاء نسخة احتياطية
      String backupPath = path.join(atlasDir.path, _backupFileName);
      File backupFile = File(backupPath);
      await backupFile.writeAsString(json.encode(appData));

      // تسجيل العملية
      await _logOperation('تم حفظ جميع بيانات التطبيق بنجاح', atlasDir);

      return {
        'success': true,
        'message': 'تم حفظ جميع بيانات التطبيق بنجاح',
        'file_path': filePath,
        'backup_path': backupPath,
        'data_size': appData.toString().length,
        'statistics': appData['statistics'],
      };
    } catch (e) {
      print('❌ خطأ في حفظ بيانات التطبيق: $e');
      return {
        'success': false,
        'message': 'خطأ في حفظ بيانات التطبيق: $e',
      };
    }
  }

  // جمع جميع بيانات التطبيق
  static Future<Map<String, dynamic>> _collectAllAppData(DatabaseHelper dbHelper) async {
    try {
      final customers = await dbHelper.getCustomers();
      final invoices = await dbHelper.getInvoices();
      final collections = await dbHelper.getCollections();
      final users = await dbHelper.getUsers();
      final products = await dbHelper.getProducts();
      
      // جمع أرقام هواتف جميع العملاء
      final allCustomerPhones = await dbHelper.getAllCustomerPhones();

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
        'app_name': 'InvoFast',
        'data': {
          'customers': customers,
          'invoices': invoices,
          'collections': collections,
          'users': users,
          'products': products,
          'customer_phones': allCustomerPhones,
        },
        'statistics': {
          'customers_count': customers.length,
          'invoices_count': invoices.length,
          'collections_count': collections.length,
          'users_count': users.length,
          'products_count': products.length,
          'customer_phones_count': allCustomerPhones.length,
        },
        'metadata': {
          'export_date': DateTime.now().toIso8601String(),
          'export_version': '1.0',
          'database_version': '1.0',
        },
      };
    } catch (e) {
      print('❌ خطأ في جمع بيانات التطبيق: $e');
      rethrow;
    }
  }

  // استعادة بيانات التطبيق من ملف
  static Future<Map<String, dynamic>> restoreAppDataFromFile(String filePath) async {
    try {
      // التحقق من الأذونات
      if (!await hasStoragePermissions()) {
        bool granted = await requestStoragePermissions();
        if (!granted) {
          return {
            'success': false,
            'message': 'لم يتم منح أذونات التخزين المطلوبة',
          };
        }
      }

      // قراءة الملف
      File dataFile = File(filePath);
      if (!await dataFile.exists()) {
        return {
          'success': false,
          'message': 'الملف غير موجود',
        };
      }

      String fileContent = await dataFile.readAsString();
      Map<String, dynamic> appData = json.decode(fileContent);

      // استعادة البيانات إلى قاعدة البيانات
      final dbHelper = DatabaseHelper();
      await _restoreDataToDatabase(dbHelper, appData);

      return {
        'success': true,
        'message': 'تم استعادة بيانات التطبيق بنجاح',
        'restored_data': appData['statistics'],
      };
    } catch (e) {
      print('❌ خطأ في استعادة بيانات التطبيق: $e');
      return {
        'success': false,
        'message': 'خطأ في استعادة بيانات التطبيق: $e',
      };
    }
  }

  // استعادة البيانات إلى قاعدة البيانات
  static Future<void> _restoreDataToDatabase(DatabaseHelper dbHelper, Map<String, dynamic> appData) async {
    try {
      // مسح البيانات الحالية
      await dbHelper.clearAllData();

      final data = appData['data'];

      // استعادة العملاء
      for (var customer in data['customers']) {
        await dbHelper.insertCustomer(customer);
      }

      // استعادة الفواتير
      for (var invoice in data['invoices']) {
        await dbHelper.insertInvoice(invoice);
      }

      // استعادة التحصيلات
      for (var collection in data['collections']) {
        await dbHelper.insertCollection(collection);
      }

      // استعادة المستخدمين
      for (var user in data['users']) {
        await dbHelper.insertUser(user);
      }

      // استعادة المنتجات
      for (var product in data['products']) {
        await dbHelper.insertProduct(product);
      }

      // استعادة أرقام هواتف العملاء
      for (var phone in data['customer_phones']) {
        await dbHelper.insertCustomerPhone(phone);
      }

      print('✅ تم استعادة جميع البيانات بنجاح');
    } catch (e) {
      print('❌ خطأ في استعادة البيانات: $e');
      rethrow;
    }
  }

  // الحصول على قائمة ملفات البيانات المحفوظة
  static Future<List<Map<String, dynamic>>> getSavedDataFiles() async {
    try {
      Directory? storageDir = await getExternalStorageDirectory();
      if (storageDir == null) {
        return [];
      }

      Directory atlasDir = Directory(path.join(storageDir.path, 'AtlasMedical'));
      if (!await atlasDir.exists()) {
        return [];
      }

      List<FileSystemEntity> files = await atlasDir.list().toList();
      List<Map<String, dynamic>> dataFiles = [];

      for (var file in files) {
        if (file is File && file.path.endsWith('.json')) {
          try {
            String content = await file.readAsString();
            Map<String, dynamic> data = json.decode(content);
            
            dataFiles.add({
              'path': file.path,
              'name': path.basename(file.path),
              'size': await file.length(),
              'timestamp': data['timestamp'],
              'statistics': data['statistics'],
            });
          } catch (e) {
            print('❌ خطأ في قراءة ملف: ${file.path}');
          }
        }
      }

      // ترتيب الملفات حسب التاريخ (الأحدث أولاً)
      dataFiles.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));
      
      return dataFiles;
    } catch (e) {
      print('❌ خطأ في الحصول على ملفات البيانات: $e');
      return [];
    }
  }

  // حذف ملف بيانات
  static Future<bool> deleteDataFile(String filePath) async {
    try {
      File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('❌ خطأ في حذف الملف: $e');
      return false;
    }
  }

  // تصدير ملف بيانات إلى موقع آخر
  static Future<String?> exportDataFile(String sourcePath, String targetPath) async {
    try {
      File sourceFile = File(sourcePath);
      File targetFile = File(targetPath);
      
      if (await sourceFile.exists()) {
        await sourceFile.copy(targetFile.path);
        return targetPath;
      }
      return null;
    } catch (e) {
      print('❌ خطأ في تصدير الملف: $e');
      return null;
    }
  }

  // تسجيل العمليات
  static Future<void> _logOperation(String message, Directory atlasDir) async {
    try {
      String logPath = path.join(atlasDir.path, _logsFileName);
      File logFile = File(logPath);
      
      String logEntry = '${DateTime.now().toIso8601String()}: $message\n';
      
      if (await logFile.exists()) {
        await logFile.writeAsString(logEntry, mode: FileMode.append);
      } else {
        await logFile.writeAsString(logEntry);
      }
    } catch (e) {
      print('❌ خطأ في تسجيل العملية: $e');
    }
  }

  // الحصول على إحصائيات التخزين
  static Future<Map<String, dynamic>> getStorageStatistics() async {
    try {
      Directory? storageDir = await getExternalStorageDirectory();
      if (storageDir == null) {
        return {
          'has_permissions': false,
          'storage_available': false,
          'message': 'لا يمكن الوصول إلى التخزين',
        };
      }

      Directory atlasDir = Directory(path.join(storageDir.path, 'AtlasMedical'));
      bool hasPermissions = await hasStoragePermissions();
      bool storageAvailable = await storageDir.exists();

      List<Map<String, dynamic>> dataFiles = await getSavedDataFiles();
      int totalSize = dataFiles.fold<int>(0, (sum, file) => sum + (file['size'] as int));

      return {
        'has_permissions': hasPermissions,
        'storage_available': storageAvailable,
        'atlas_directory_exists': await atlasDir.exists(),
        'data_files_count': dataFiles.length,
        'total_size_bytes': totalSize,
        'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
        'latest_file': dataFiles.isNotEmpty ? dataFiles.first['timestamp'] : null,
        'storage_path': storageDir.path,
        'atlas_path': atlasDir.path,
      };
    } catch (e) {
      return {
        'has_permissions': false,
        'storage_available': false,
        'error': e.toString(),
      };
    }
  }

  // إنشاء نسخة احتياطية تلقائية
  static Future<Map<String, dynamic>> createAutoBackup() async {
    try {
      if (!await hasStoragePermissions()) {
        return {
          'success': false,
          'message': 'لا توجد أذونات تخزين كافية',
        };
      }

      Directory? storageDir = await getExternalStorageDirectory();
      if (storageDir == null) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول إلى التخزين',
        };
      }

      Directory atlasDir = Directory(path.join(storageDir.path, 'AtlasMedical'));
      if (!await atlasDir.exists()) {
        await atlasDir.create(recursive: true);
      }

      final dbHelper = DatabaseHelper();
      final appData = await _collectAllAppData(dbHelper);

      String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      String backupPath = path.join(atlasDir.path, 'auto_backup_$timestamp.json');
      File backupFile = File(backupPath);
      await backupFile.writeAsString(json.encode(appData));

      // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 5 نسخ)
      await _cleanOldAutoBackups(atlasDir);

      return {
        'success': true,
        'message': 'تم إنشاء النسخة الاحتياطية التلقائية بنجاح',
        'backup_path': backupPath,
        'statistics': appData['statistics'],
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إنشاء النسخة الاحتياطية: $e',
      };
    }
  }

  // تنظيف النسخ الاحتياطية القديمة
  static Future<void> _cleanOldAutoBackups(Directory atlasDir) async {
    try {
      List<FileSystemEntity> files = await atlasDir.list().toList();
      List<File> backupFiles = files
          .where((file) => file is File && path.basename(file.path).startsWith('auto_backup_'))
          .cast<File>()
          .toList();

      if (backupFiles.length > 5) {
        backupFiles.sort((a, b) => a.statSync().modified.compareTo(b.statSync().modified));
        
        for (int i = 0; i < backupFiles.length - 5; i++) {
          await backupFiles[i].delete();
        }
      }
    } catch (e) {
      print('❌ خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
} 