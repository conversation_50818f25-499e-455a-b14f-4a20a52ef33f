import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/database/database_helper.dart';
import 'package:atlas_medical_supplies/services/auth_service.dart';
import 'package:atlas_medical_supplies/services/performance_service.dart';
import 'package:atlas_medical_supplies/services/storage_service.dart';
import 'package:atlas_medical_supplies/services/auto_sync_service.dart';
import 'package:atlas_medical_supplies/services/auto_backup_service.dart';
import 'package:atlas_medical_supplies/services/messaging_service.dart';
import 'package:atlas_medical_supplies/services/invoice_sharing_service.dart';
import 'package:atlas_medical_supplies/services/invoice_message_service.dart';
import 'package:atlas_medical_supplies/services/local_backup_service.dart';
import 'package:atlas_medical_supplies/services/permissions_service.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:io';

void main() {
  group('اختبار شامل للمشاكل', () {
    late DatabaseHelper databaseHelper;

    setUpAll(() async {
      databaseHelper = DatabaseHelper();
      await databaseHelper.database;
      await AuthService.createDefaultUser();
    });

    group('فحص قاعدة البيانات', () {
      test('فحص سلامة قاعدة البيانات', () async {
        try {
          final db = await databaseHelper.database;

          // فحص الجداول
          final tables = await db.query(
            'sqlite_master',
            where: 'type = ?',
            whereArgs: ['table'],
          );
          print('📋 عدد الجداول: ${tables.length}');

          // فحص حجم قاعدة البيانات
          final dbPath = join(await getDatabasesPath(), 'atlas_medical.db');
          final dbFile = File(dbPath);
          if (await dbFile.exists()) {
            final size = await dbFile.length();
            final sizeMB = size / 1024 / 1024;
            print('💾 حجم قاعدة البيانات: ${sizeMB.toStringAsFixed(2)} MB');

            if (sizeMB > 50) {
              print('⚠️ تحذير: قاعدة البيانات كبيرة جداً');
            }
          }

          expect(tables.length, greaterThan(0));
        } catch (e) {
          print('❌ خطأ في فحص قاعدة البيانات: $e');
          fail('فشل في فحص قاعدة البيانات');
        }
      });

      test('فحص البيانات المفقودة', () async {
        try {
          final customers = await databaseHelper.getCustomers();
          final invoices = await databaseHelper.getInvoices();
          final collections = await databaseHelper.getCollections();
          final users = await databaseHelper.getUsers();

          print('📊 إحصائيات البيانات:');
          print('   العملاء: ${customers.length}');
          print('   الفواتير: ${invoices.length}');
          print('   التحصيلات: ${collections.length}');
          print('   المستخدمين: ${users.length}');

          // فحص البيانات المفقودة
          for (final customer in customers) {
            if (customer['name'] == null ||
                customer['name'].toString().isEmpty) {
              print('⚠️ عميل بدون اسم: ID ${customer['id']}');
            }
          }

          for (final invoice in invoices) {
            if (invoice['customer_id'] == null) {
              print('⚠️ فاتورة بدون عميل: ID ${invoice['id']}');
            }
          }
        } catch (e) {
          print('❌ خطأ في فحص البيانات المفقودة: $e');
          fail('فشل في فحص البيانات المفقودة');
        }
      });
    });

    group('فحص الخدمات', () {
      test('فحص خدمة المصادقة', () async {
        try {
          final users = await databaseHelper.getUsers();
          print('👥 عدد المستخدمين: ${users.length}');

          if (users.isEmpty) {
            print('⚠️ تحذير: لا يوجد مستخدمين');
          }

          // فحص المستخدم الافتراضي
          final adminUser = users
              .where((user) => user['phone'] == 'admin')
              .firstOrNull;
          if (adminUser == null) {
            print('⚠️ تحذير: المستخدم الافتراضي غير موجود');
          } else {
            print('✅ المستخدم الافتراضي موجود');
          }
        } catch (e) {
          print('❌ خطأ في فحص خدمة المصادقة: $e');
          fail('فشل في فحص خدمة المصادقة');
        }
      });

      test('فحص خدمة الأداء', () async {
        try {
          final isFirstLaunch = await PerformanceService.isFirstLaunch();
          final needsBackup = await PerformanceService.needsBackupRestore();

          print('🚀 أول تشغيل: $isFirstLaunch');
          print('💾 يحتاج نسخة احتياطية: $needsBackup');

          expect(isFirstLaunch, isA<bool>());
          expect(needsBackup, isA<bool>());
        } catch (e) {
          print('❌ خطأ في فحص خدمة الأداء: $e');
          fail('فشل في فحص خدمة الأداء');
        }
      });

      test('فحص خدمة التخزين', () async {
        try {
          final hasPermissions = await StorageService.hasStoragePermissions();
          print('📱 أذونات التخزين: $hasPermissions');

          if (!hasPermissions) {
            print('⚠️ تحذير: لا توجد أذونات تخزين');
          }
        } catch (e) {
          print('❌ خطأ في فحص خدمة التخزين: $e');
          fail('فشل في فحص خدمة التخزين');
        }
      });
    });

    group('فحص المزامنة والنسخ الاحتياطي', () {
      test('فحص المزامنة التلقائية', () async {
        try {
          final isEnabled = await AutoSyncService.isAutoSyncEnabled();
          print('🔄 المزامنة التلقائية: $isEnabled');
        } catch (e) {
          print('❌ خطأ في فحص المزامنة التلقائية: $e');
          fail('فشل في فحص المزامنة التلقائية');
        }
      });

      test('فحص النسخ الاحتياطي التلقائي', () async {
        try {
          final isEnabled = await AutoBackupService.isAutoBackupEnabled();
          print('💾 النسخ الاحتياطي التلقائي: $isEnabled');
        } catch (e) {
          print('❌ خطأ في فحص النسخ الاحتياطي التلقائي: $e');
          fail('فشل في فحص النسخ الاحتياطي التلقائي');
        }
      });

      test('فحص النسخ الاحتياطية المحلية', () async {
        try {
          final backups = await LocalBackupService.getLocalBackups();
          print('📦 عدد النسخ الاحتياطية المحلية: ${backups.length}');

          if (backups.isNotEmpty) {
            final latestBackup = backups.first;
            print('📅 آخر نسخة احتياطية: ${latestBackup['date']}');
          } else {
            print('⚠️ تحذير: لا توجد نسخ احتياطية محلية');
          }
        } catch (e) {
          print('❌ خطأ في فحص النسخ الاحتياطية المحلية: $e');
          fail('فشل في فحص النسخ الاحتياطية المحلية');
        }
      });
    });

    group('فحص الرسائل والمشاركة', () {
      test('فحص خدمة الرسائل', () async {
        try {
          final messages = await MessagingService.getMessages();
          print('💬 عدد الرسائل: ${messages.length}');
        } catch (e) {
          print('❌ خطأ في فحص خدمة الرسائل: $e');
          fail('فشل في فحص خدمة الرسائل');
        }
      });

      test('فحص مشاركة الفواتير', () async {
        try {
          final canShare = await InvoiceSharingService.canShareInvoice();
          print('📤 إمكانية مشاركة الفواتير: $canShare');
        } catch (e) {
          print('❌ خطأ في فحص مشاركة الفواتير: $e');
          fail('فشل في فحص مشاركة الفواتير');
        }
      });

      test('فحص رسائل الفواتير', () async {
        try {
          final messages = await InvoiceMessageService.getInvoiceMessages();
          print('📄 عدد رسائل الفواتير: ${messages.length}');
        } catch (e) {
          print('❌ خطأ في فحص رسائل الفواتير: $e');
          fail('فشل في فحص رسائل الفواتير');
        }
      });
    });

    group('فحص الأذونات والصلاحيات', () {
      test('فحص الأذونات', () async {
        try {
          final permissions = await PermissionsService.getPermissions();
          print('🔐 الأذونات: ${permissions.length}');
        } catch (e) {
          print('❌ خطأ في فحص الأذونات: $e');
          fail('فشل في فحص الأذونات');
        }
      });
    });

    group('تقرير شامل', () {
      test('تقرير شامل عن حالة التطبيق', () async {
        print('\n📊 تقرير شامل عن حالة التطبيق:');
        print('=' * 50);

        try {
          // قاعدة البيانات
          final db = await databaseHelper.database;
          final tables = await db.query(
            'sqlite_master',
            where: 'type = ?',
            whereArgs: ['table'],
          );
          final dbPath = join(await getDatabasesPath(), 'atlas_medical.db');
          final dbFile = File(dbPath);
          final dbSize = await dbFile.exists() ? await dbFile.length() : 0;

          print('💾 قاعدة البيانات:');
          print('   الجداول: ${tables.length}');
          print('   الحجم: ${(dbSize / 1024 / 1024).toStringAsFixed(2)} MB');

          // البيانات
          final customers = await databaseHelper.getCustomers();
          final invoices = await databaseHelper.getInvoices();
          final collections = await databaseHelper.getCollections();
          final users = await databaseHelper.getUsers();

          print('📊 البيانات:');
          print('   العملاء: ${customers.length}');
          print('   الفواتير: ${invoices.length}');
          print('   التحصيلات: ${collections.length}');
          print('   المستخدمين: ${users.length}');

          // الخدمات
          final hasStoragePermissions =
              await StorageService.hasStoragePermissions();
          final isAutoSyncEnabled = await AutoSyncService.isAutoSyncEnabled();
          final isAutoBackupEnabled =
              await AutoBackupService.isAutoBackupEnabled();
          final backups = await LocalBackupService.getLocalBackups();

          print('🔧 الخدمات:');
          print('   أذونات التخزين: $hasStoragePermissions');
          print('   المزامنة التلقائية: $isAutoSyncEnabled');
          print('   النسخ الاحتياطي التلقائي: $isAutoBackupEnabled');
          print('   النسخ الاحتياطية المحلية: ${backups.length}');

          // التحذيرات
          final warnings = <String>[];
          if (dbSize > 50 * 1024 * 1024)
            warnings.add('قاعدة البيانات كبيرة جداً');
          if (users.isEmpty) warnings.add('لا يوجد مستخدمين');
          if (!hasStoragePermissions) warnings.add('لا توجد أذونات تخزين');
          if (backups.isEmpty) warnings.add('لا توجد نسخ احتياطية');

          if (warnings.isNotEmpty) {
            print('⚠️ التحذيرات:');
            for (final warning in warnings) {
              print('   - $warning');
            }
          } else {
            print('✅ لا توجد تحذيرات');
          }
        } catch (e) {
          print('❌ خطأ في إنشاء التقرير: $e');
          fail('فشل في إنشاء التقرير');
        }
      });
    });
  });
}
