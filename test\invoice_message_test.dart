import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/services/invoice_message_service.dart';

void main() {
  group('InvoiceMessageService Tests', () {
    test('generateSimpleInvoiceMessage should create correct message', () {
      final invoice = {
        'customer_name': 'أحمد محمد',
        'invoice_number': 'INV-2024-001',
        'date': '2024-01-15',
        'total_amount': 1500.0,
        'paid_amount': 500.0,
        'remaining_amount': 1000.0,
      };

      final message = InvoiceMessageService.generateSimpleInvoiceMessage(invoice);
      
      expect(message, contains('السيد/ أحمد محمد'));
      expect(message, contains('فاتورة رقم: INV-2024-001'));
      expect(message, contains('إجمالي الفاتورة: 1,500.00 ج.م'));
      expect(message, contains('تم الدفع: 500.00 ج.م'));
      expect(message, contains('المتبقي: 1,000.00 ج.م'));
      expect(message, contains('نشكر تعاونكم'));
    });

    test('generatePaymentReminderMessage should create correct reminder', () {
      final invoice = {
        'customer_name': 'فاطمة علي',
        'invoice_number': 'INV-2024-002',
        'date': '2024-01-10',
        'remaining_amount': 750.0,
      };

      final message = InvoiceMessageService.generatePaymentReminderMessage(invoice);
      
      expect(message, contains('السيد/ فاطمة علي'));
      expect(message, contains('تذكير بدفع الفاتورة رقم: INV-2024-002'));
      expect(message, contains('المبلغ المتبقي: 750.00 ج.م'));
      expect(message, contains('نرجو منكم تسديد المبلغ المتبقي'));
    });

    test('generatePaymentConfirmationMessage should create correct confirmation', () {
      final invoice = {
        'customer_name': 'محمد أحمد',
        'invoice_number': 'INV-2024-003',
        'total_amount': 2000.0,
        'remaining_amount': 500.0,
      };

      final message = InvoiceMessageService.generatePaymentConfirmationMessage(invoice, 1500.0);
      
      expect(message, contains('السيد/ محمد أحمد'));
      expect(message, contains('تم استلام مبلغ 1,500.00 ج.م'));
      expect(message, contains('للفاتورة رقم: INV-2024-003'));
      expect(message, contains('إجمالي الفاتورة: 2,000.00 ج.م'));
      expect(message, contains('المتبقي: 500.00 ج.م'));
      expect(message, contains('شكراً لكم على الدفع'));
    });

    test('should handle missing data gracefully', () {
      final invoice = {
        'invoice_number': 'INV-2024-004',
      };

      final message = InvoiceMessageService.generateSimpleInvoiceMessage(invoice);
      
      expect(message, contains('السيد/ عميل غير محدد'));
      expect(message, contains('فاتورة رقم: INV-2024-004'));
      expect(message, contains('تاريخ: غير محدد'));
      expect(message, contains('إجمالي الفاتورة: 0.00 ج.م'));
    });

    test('should format currency correctly', () {
      final invoice = {
        'customer_name': 'علي محمد',
        'invoice_number': 'INV-2024-005',
        'date': '2024-01-20',
        'total_amount': 1234567.89,
        'paid_amount': 987654.32,
        'remaining_amount': 246913.57,
      };

      final message = InvoiceMessageService.generateSimpleInvoiceMessage(invoice);
      
      expect(message, contains('إجمالي الفاتورة: 1,234,567.89 ج.م'));
      expect(message, contains('تم الدفع: 987,654.32 ج.م'));
      expect(message, contains('المتبقي: 246,913.57 ج.م'));
    });
  });
} 
