import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/invoice_sharing_service.dart';
import '../services/invoice_details_service.dart';
import '../services/invoice_printing_service.dart';
import '../widgets/customer_phones_widget.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen>
    with SingleTickerProviderStateMixin {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  late TabController _tabController;

  List<Map<String, dynamic>> _invoices = [];
  List<Map<String, dynamic>> _collections = [];
  bool _isLoading = true;
  bool _isManager = false;

  @override
  void initState() {
    super.initState();
    _loadCustomerData();
    _checkUserRole();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkUserRole() async {
    final isAdmin = await AuthService.isAdmin();
    setState(() {
      _isManager = isAdmin;
    });
  }

  Future<void> _loadCustomerData() async {
    try {
      final invoices = await _dbHelper.getInvoicesByCustomer(
        widget.customer['id'],
      );
      final collections = await _dbHelper.getCollectionsByCustomer(
        widget.customer['id'],
      );

      setState(() {
        _invoices = invoices;
        _collections = collections;
        _isLoading = false;
      });

      // تهيئة TabController بناءً على وجود الفواتير
      _initializeTabController();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _initializeTabController() {
    // إذا كان العميل ليس له فواتير، لا نعرض تبويب التحصيل
    final tabCount = _invoices.isEmpty ? 1 : 2;
    _tabController = TabController(length: tabCount, vsync: this);
  }

  void _showEditCollectionDialog(Map<String, dynamic> collection) {
    final amountController = TextEditingController(
      text: collection['amount'].toString(),
    );
    final notesController = TextEditingController(
      text: collection['notes'] ?? '',
    );
    String selectedPaymentMethod = collection['payment_method'] ?? 'نقداً';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('تعديل التحصيل'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ *',
                    border: OutlineInputBorder(),
                    suffixText: 'ج.م',
                  ),
                ),
                const SizedBox(height: 16),

                DropdownButtonFormField<String>(
                  value: selectedPaymentMethod,
                  decoration: const InputDecoration(
                    labelText: 'طريقة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'نقداً', child: Text('نقداً')),
                    DropdownMenuItem(
                      value: 'فودافون كاش',
                      child: Text('فودافون كاش'),
                    ),
                    DropdownMenuItem(
                      value: 'انستا باي',
                      child: Text('انستا باي'),
                    ),
                    DropdownMenuItem(
                      value: 'اتصالات كاش',
                      child: Text('اتصالات كاش'),
                    ),
                    DropdownMenuItem(
                      value: 'المحافظ المصرية',
                      child: Text('المحافظ المصرية'),
                    ),
                  ],
                  onChanged: (value) {
                    setDialogState(() {
                      selectedPaymentMethod = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                TextField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amountText = amountController.text.trim();

                if (amountText.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال المبلغ'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }

                final amount = double.tryParse(amountText);
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال مبلغ صحيح'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }

                try {
                  await _dbHelper.updateCollection(collection['id'], {
                    'amount': amount,
                    'payment_method': selectedPaymentMethod,
                    'notes': notesController.text.trim(),
                  });

                  Navigator.of(context).pop();
                  _loadCustomerData();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث التحصيل بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تحديث التحصيل: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteCollectionDialog(Map<String, dynamic> collection) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التحصيل'),
        content: Text(
          'هل أنت متأكد من حذف التحصيل بقيمة ${collection['amount']?.toStringAsFixed(2)} ج.م؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _dbHelper.deleteCollection(collection['id']);
                Navigator.of(context).pop();
                _loadCustomerData();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف التحصيل بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في حذف التحصيل: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showAddCollectionDialog() {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    String selectedPaymentMethod = 'نقداً';
    String? selectedInvoiceId;

    // جلب الفواتير غير المدفوعة بالكامل
    final unpaidInvoices = _invoices.where((invoice) {
      final remaining = invoice['remaining_amount'] ?? 0.0;
      return remaining > 0;
    }).toList();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('إضافة تحصيل جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ *',
                    border: OutlineInputBorder(),
                    suffixText: 'ج.م',
                  ),
                ),
                const SizedBox(height: 16),

                // اختيار الفاتورة (اختياري)
                if (unpaidInvoices.isNotEmpty) ...[
                  DropdownButtonFormField<String>(
                    value: selectedInvoiceId,
                    decoration: const InputDecoration(
                      labelText: 'الفاتورة (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('بدون فاتورة محددة'),
                      ),
                      ...unpaidInvoices.map(
                        (invoice) => DropdownMenuItem(
                          value: invoice['id'].toString(),
                          child: Text(
                            '${invoice['invoice_number']} - المتبقي: ${invoice['remaining_amount']?.toStringAsFixed(2)} ج.م',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setDialogState(() {
                        selectedInvoiceId = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                DropdownButtonFormField<String>(
                  value: selectedPaymentMethod,
                  decoration: const InputDecoration(
                    labelText: 'طريقة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'نقداً', child: Text('نقداً')),
                    DropdownMenuItem(
                      value: 'فودافون كاش',
                      child: Text('فودافون كاش'),
                    ),
                    DropdownMenuItem(
                      value: 'انستا باي',
                      child: Text('انستا باي'),
                    ),
                    DropdownMenuItem(
                      value: 'اتصالات كاش',
                      child: Text('اتصالات كاش'),
                    ),
                    DropdownMenuItem(
                      value: 'المحافظ المصرية',
                      child: Text('المحافظ المصرية'),
                    ),
                  ],
                  onChanged: (value) {
                    setDialogState(() {
                      selectedPaymentMethod = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                TextField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amountText = amountController.text.trim();

                if (amountText.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال المبلغ'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }

                final amount = double.tryParse(amountText);
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال مبلغ صحيح'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }

                try {
                  final currentUser = await AuthService.getCurrentUser();
                  final collectionData = {
                    'customer_id': widget.customer['id'],
                    'invoice_id': selectedInvoiceId != null
                        ? int.parse(selectedInvoiceId!)
                        : null,
                    'amount': amount,
                    'date': DateFormat('yyyy-MM-dd').format(DateTime.now()),
                    'collector_name': currentUser?['name'] ?? 'نظام',
                    'notes': notesController.text.trim(),
                    'customer_name': widget.customer['name'],
                    'payment_method': selectedPaymentMethod,
                    'created_by': currentUser?['id'],
                  };

                  await _dbHelper.insertCollection(collectionData);

                  // إذا كان التحصيل مرتبط بفاتورة، تحديث المبلغ المدفوع
                  if (selectedInvoiceId != null) {
                    final invoice = unpaidInvoices.firstWhere(
                      (inv) => inv['id'].toString() == selectedInvoiceId,
                    );
                    final currentPaid = invoice['paid_amount'] ?? 0.0;
                    final newPaid = currentPaid + amount;
                    final totalAmount = invoice['total_amount'] ?? 0.0;
                    final newRemaining = totalAmount - newPaid;

                    await _dbHelper.updateInvoice(invoice['id'], {
                      'paid_amount': newPaid,
                      'remaining_amount': newRemaining,
                    });
                  }

                  Navigator.of(context).pop();
                  _loadCustomerData();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة التحصيل بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة التحصيل: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  double get _totalInvoicesAmount {
    return _invoices.fold(
      0.0,
      (sum, invoice) => sum + (invoice['total_amount'] ?? 0.0),
    );
  }

  double get _totalPaidAmount {
    return _invoices.fold(
      0.0,
      (sum, invoice) => sum + (invoice['paid_amount'] ?? 0.0),
    );
  }

  double get _totalRemainingAmount {
    return _totalInvoicesAmount - _totalPaidAmount;
  }

  double get _totalCollectionsAmount {
    return _collections.fold(
      0.0,
      (sum, collection) => sum + (collection['amount'] ?? 0.0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: Text(
          'تفاصيل العميل: ${widget.customer['name']}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.phone),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => CustomerPhonesWidget(
                    customerId: widget.customer['id'],
                    customerName: widget.customer['name'],
                  ),
                ),
              );
            },
            tooltip: 'إدارة أرقام الهواتف',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () async {
              try {
                await InvoiceSharingService.sendCustomerStatement(
                  context,
                  widget.customer,
                );
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إرسال كشف الحساب: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            tooltip: 'إرسال كشف الحساب',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCustomerData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // معلومات العميل
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: const Color(0xFF4A90E2),
                            child: Text(
                              widget.customer['name']
                                      ?.toString()
                                      .substring(0, 1)
                                      .toUpperCase() ??
                                  'C',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.customer['name'] ?? '',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (widget.customer['primary_phone']
                                        ?.toString()
                                        .isNotEmpty ==
                                    true)
                                  Text(
                                    'الهاتف: ${widget.customer['primary_phone']}',
                                    style: const TextStyle(color: Colors.grey),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // الإحصائيات المالية
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatItem(
                              'إجمالي الفواتير',
                              '${_totalInvoicesAmount.toStringAsFixed(2)} ج.م',
                              Icons.receipt,
                              const Color(0xFF4A90E2),
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'المدفوع',
                              '${_totalPaidAmount.toStringAsFixed(2)} ج.م',
                              Icons.payment,
                              Colors.green,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'المتبقي',
                              '${_totalRemainingAmount.toStringAsFixed(2)} ج.م',
                              Icons.account_balance_wallet,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // التبويبات - تظهر فقط إذا كان للعميل فواتير
                if (_invoices.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TabBar(
                      controller: _tabController,
                      labelColor: const Color(0xFF4A90E2),
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: const Color(0xFF4A90E2),
                      tabs: const [
                        Tab(text: 'الفواتير'),
                        Tab(text: 'التحصيلات'),
                      ],
                    ),
                  ),

                // محتوى التبويبات
                Expanded(
                  child: _invoices.isEmpty
                      ? _buildInvoicesTab() // إذا لم تكن هناك فواتير، اعرض فقط قائمة الفواتير
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            _buildInvoicesTab(),
                            _buildCollectionsTab(),
                          ],
                        ),
                ),
              ],
            ),
      // تم إزالة FloatingActionButton حسب طلب المستخدم
      // floatingActionButton: _invoices.isNotEmpty
      //     ? FloatingActionButton(
      //         onPressed: _showAddCollectionDialog,
      //         backgroundColor: const Color(0xFF4A90E2),
      //         foregroundColor: Colors.white,
      //         child: const Icon(Icons.add),
      //       )
      //     : null,
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لعرض مبلغ مع عنوان ولون
  Widget _buildAmountItem(String title, num value, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${value.toStringAsFixed(2)} ج.م',
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInvoicesTab() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: _invoices.isEmpty
          ? const Center(
              child: Text(
                'لا توجد فواتير لهذا العميل',
                style: TextStyle(fontSize: 16),
              ),
            )
          : ListView.builder(
              itemCount: _invoices.length,
              itemBuilder: (context, index) {
                final invoice = _invoices[index];
                final remaining = invoice['remaining_amount'] ?? 0.0;
                final isFullyPaid = remaining <= 0;

                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // الصف العلوي: رقم الفاتورة + زر المزيد
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'فاتورة رقم: ${invoice['invoice_number']}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: Color(0xFF4A90E2),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            PopupMenuButton<String>(
                              icon: const Icon(
                                Icons.more_vert,
                                color: Color(0xFF4A90E2),
                              ),
                              onSelected: (value) {
                                switch (value) {
                                  case 'details':
                                    InvoiceDetailsService.showInvoiceDetails(
                                      context,
                                      invoice,
                                      widget.customer,
                                    );
                                    break;
                                  case 'print':
                                    InvoicePrintingService.showPrintOptions(
                                      context,
                                      invoice,
                                      widget.customer,
                                    );
                                    break;
                                  case 'share':
                                    // TODO: إضافة ميزة المشاركة
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'سيتم إضافة ميزة المشاركة قريباً',
                                        ),
                                        backgroundColor: Colors.blue,
                                      ),
                                    );
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'details',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.receipt,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      SizedBox(width: 8),
                                      Text('تفاصيل الفاتورة'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'print',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.print,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      SizedBox(width: 8),
                                      Text('طباعة الفاتورة'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'share',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.share,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      SizedBox(width: 8),
                                      Text('مشاركة الفاتورة'),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(height: 16),

                        // التاريخ
                        Row(
                          children: [
                            const Icon(
                              Icons.date_range,
                              size: 16,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'التاريخ: ${invoice['date']}',
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // المبالغ في صف واحد
                        Row(
                          children: [
                            Expanded(
                              child: _buildAmountItem(
                                'الإجمالي',
                                invoice['total_amount'] ?? 0.0,
                                Colors.blue,
                              ),
                            ),
                            Expanded(
                              child: _buildAmountItem(
                                'المدفوع',
                                invoice['paid_amount'] ?? 0.0,
                                Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildAmountItem(
                                'المتبقي',
                                remaining,
                                isFullyPaid ? Colors.green : Colors.orange,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // زر متبقي أو مدفوع بالكامل
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (!isFullyPaid)
                              ElevatedButton.icon(
                                onPressed: () {
                                  // TODO: إضافة ميزة إضافة تحصيل
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'سيتم إضافة ميزة إضافة تحصيل قريباً',
                                      ),
                                      backgroundColor: Colors.orange,
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.payment, size: 16),
                                label: const Text('إضافة تحصيل'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                              ),
                            if (isFullyPaid)
                              const Chip(
                                label: Text('مدفوع بالكامل'),
                                backgroundColor: Colors.green,
                                labelStyle: TextStyle(color: Colors.white),
                              ),
                            const Spacer(),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(
                                    Icons.receipt,
                                    color: Color(0xFF4A90E2),
                                  ),
                                  onPressed: () {
                                    InvoiceDetailsService.showInvoiceDetails(
                                      context,
                                      invoice,
                                      widget.customer,
                                    );
                                  },
                                  tooltip: 'تفاصيل الفاتورة',
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.print,
                                    color: Color(0xFF4A90E2),
                                  ),
                                  onPressed: () {
                                    InvoicePrintingService.showPrintOptions(
                                      context,
                                      invoice,
                                      widget.customer,
                                    );
                                  },
                                  tooltip: 'طباعة الفاتورة',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  Widget _buildCollectionsTab() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: _collections.isEmpty
          ? const Center(
              child: Text(
                'لا توجد تحصيلات لهذا العميل',
                style: TextStyle(fontSize: 16),
              ),
            )
          : ListView.builder(
              itemCount: _collections.length,
              itemBuilder: (context, index) {
                final collection = _collections[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: const Color(0xFF4A90E2),
                      child: const Icon(Icons.payment, color: Colors.white),
                    ),
                    title: Text(
                      'تحصيل: ${collection['amount']?.toStringAsFixed(2)} ج.م',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${collection['date']}'),
                        Text('طريقة الدفع: ${collection['payment_method']}'),
                        Text('المحصل: ${collection['collector_name']}'),
                        if (collection['notes']?.toString().isNotEmpty == true)
                          Text('ملاحظات: ${collection['notes']}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          collection['amount']?.toStringAsFixed(2) ?? '0.00',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF4A90E2),
                            fontSize: 16,
                          ),
                        ),
                        if (_isManager) ...[
                          const SizedBox(width: 8),
                          PopupMenuButton<String>(
                            icon: const Icon(Icons.more_vert),
                            onSelected: (value) {
                              switch (value) {
                                case 'edit':
                                  _showEditCollectionDialog(collection);
                                  break;
                                case 'delete':
                                  _showDeleteCollectionDialog(collection);
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              const PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit, color: Color(0xFF4A90E2)),
                                    SizedBox(width: 8),
                                    Expanded(child: Text('تعديل')),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(Icons.delete, color: Colors.red),
                                    SizedBox(width: 8),
                                    Expanded(child: Text('حذف')),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
