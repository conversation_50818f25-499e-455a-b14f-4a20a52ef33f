import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/invoice_sharing_service.dart';
import '../widgets/collection_dialog.dart';

class CustomerInvoicesScreen extends StatefulWidget {
  final Map<String, dynamic> customer;

  const CustomerInvoicesScreen({super.key, required this.customer});

  @override
  State<CustomerInvoicesScreen> createState() => _CustomerInvoicesScreenState();
}

class _CustomerInvoicesScreenState extends State<CustomerInvoicesScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<Map<String, dynamic>> _invoices = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCustomerInvoices();
  }

  Future<void> _loadCustomerInvoices() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final invoices = await _dbHelper.getInvoicesByCustomer(
        widget.customer['id'] as int,
      );
      setState(() {
        _invoices = invoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل فواتير العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addCollectionForInvoice(Map<String, dynamic> invoice) async {
    final remainingAmount = (invoice['remaining_amount'] ?? 0.0) as double;

    if (remainingAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('هذه الفاتورة مدفوعة بالكامل'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // عرض نافذة إضافة التحصيل
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) =>
          CollectionDialog(invoice: invoice, remainingAmount: remainingAmount),
    );

    if (result != null) {
      try {
        final currentUserId = await AuthService.getCurrentUserId();
        final currentUserName = await AuthService.getCurrentUserName();

        final collectionData = {
          'customer_id': invoice['customer_id'] as int,
          'invoice_id': invoice['id'] as int,
          'amount': result['amount'] as double,
          'date': result['date'] as String,
          'collector_name': result['collector_name'] as String,
          'payment_method': result['payment_method'] as String,
          'notes': result['notes'] as String,
          'created_by': currentUserId,
          'customer_name': invoice['customer_name'] as String,
        };

        await DatabaseHelper().insertCollection(collectionData);

        // تحديث الفاتورة
        final newPaidAmount =
            (invoice['paid_amount'] ?? 0.0) + result['amount'];
        final newRemainingAmount =
            (invoice['total_amount'] ?? 0.0) - newPaidAmount;

        await DatabaseHelper().updateInvoice(invoice['id'] as int, {
          'paid_amount': newPaidAmount,
          'remaining_amount': newRemainingAmount,
        });

        // إعادة تحميل الفواتير
        _loadCustomerInvoices();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة التحصيل بنجاح: ${result['amount'].toStringAsFixed(2)} ج.م',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة التحصيل: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _createNewInvoice() {
    Navigator.pushNamed(
      context,
      '/add-invoice',
      arguments: {'selectedCustomer': widget.customer},
    ).then((_) => _loadCustomerInvoices());
  }

  void _editInvoice(Map<String, dynamic> invoice) {
    Navigator.pushNamed(
      context,
      '/edit-invoice',
      arguments: invoice,
    ).then((_) => _loadCustomerInvoices());
  }

  void _deleteInvoice(int id, String invoiceNumber) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الفاتورة رقم "$invoiceNumber"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteInvoice(id);
        _loadCustomerInvoices();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الفاتورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _viewInvoiceDetails(Map<String, dynamic> invoice) {
    Navigator.pushNamed(context, '/invoice-details', arguments: invoice);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: Text('فواتير ${widget.customer['name']}'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCustomerInvoices,
          ),
        ],
      ),
      body: Column(
        children: [
          // معلومات العميل
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.person, color: Colors.white),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            (widget.customer['name'] ?? 'غير محدد') as String,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4A90E2),
                            ),
                          ),
                          if (widget.customer['primary_phone'] != null)
                            Text(
                              'الهاتف: ${widget.customer['primary_phone']}',
                              style: const TextStyle(color: Colors.grey),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (widget.customer['address'] != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'العنوان: ${widget.customer['address']}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
                if (widget.customer['governorate'] != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'المحافظة: ${widget.customer['governorate']}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ],
            ),
          ),

          // قائمة الفواتير
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
                  )
                : _invoices.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.receipt_outlined,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد فواتير لهذا العميل',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'اضغط على + لإنشاء فاتورة جديدة',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadCustomerInvoices,
                    color: const Color(0xFF4A90E2),
                    child: ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom:
                            80, // إضافة مساحة في الأسفل لتجنب تداخل FloatingActionButton
                      ),
                      itemCount: _invoices.length,
                      itemBuilder: (context, index) {
                        final invoice = _invoices[index];
                        final remainingAmount = (invoice['remaining_amount'] ?? 0.0) as double;
                        final isPaid = remainingAmount <= 0;

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(16),
                            leading: CircleAvatar(
                              backgroundColor: isPaid
                                  ? Colors.green
                                  : const Color(0xFF4A90E2),
                              child: Icon(
                                isPaid ? Icons.check : Icons.receipt,
                                color: Colors.white,
                              ),
                            ),
                            title: Text(
                              'فاتورة رقم ${invoice['invoice_number']}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF4A90E2),
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('التاريخ: ${invoice['date']}'),
                                Text(
                                  'المبلغ: ${(invoice['total_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                                if (invoice['created_by_name'] != null)
                                  Text(
                                    'أنشأها: ${invoice['created_by_name']}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                if (!isPaid)
                                  Text(
                                    'متبقي: ${remainingAmount.toStringAsFixed(2)} ج.م',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red,
                                    ),
                                  ),
                              ],
                            ),
                            trailing: PopupMenuButton<String>(
                              onSelected: (value) {
                                if (value == 'view') {
                                  _viewInvoiceDetails(invoice);
                                } else if (value == 'edit') {
                                  _editInvoice(invoice);
                                } else if (value == 'collect') {
                                  _addCollectionForInvoice(invoice);
                                } else if (value == 'share') {
                                  InvoiceSharingService.sendInvoice(
                                    context,
                                    invoice,
                                    widget.customer,
                                  );
                                } else if (value == 'delete') {
                                  _deleteInvoice(
                                    invoice['id'] as int,
                                    invoice['invoice_number'] as String,
                                  );
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'view',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.visibility,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('عرض التفاصيل')),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.edit,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('تعديل')),
                                    ],
                                  ),
                                ),
                                if (!isPaid)
                                  const PopupMenuItem(
                                    value: 'collect',
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.payment,
                                          color: Colors.green,
                                        ),
                                        SizedBox(width: 8),
                                        Expanded(child: Text('إضافة تحصيل')),
                                      ],
                                    ),
                                  ),
                                const PopupMenuItem(
                                  value: 'share',
                                  child: Row(
                                    children: [
                                      Icon(Icons.share, color: Colors.blue),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('مشاركة الفاتورة')),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('حذف')),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewInvoice,
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}
