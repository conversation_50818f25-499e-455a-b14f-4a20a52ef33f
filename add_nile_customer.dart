import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('Starting to add Nile Company customer...');

  try {
    // Open database
    String path = join(await getDatabasesPath(), 'atlas_medical.db');
    Database db = await openDatabase(path);

    // Customer data
    final customerData = {
      'name': 'شركة النيل',
      'phone': '01110473536',
      'address': 'أبو حمص',
      'governorate': 'البحيرة',
      'area': 'أبو حمص',
      'notes': 'Added on ${DateTime.now().toString().split(' ')[0]}',
    };

    print('Customer data:');
    print('  Name: ${customerData['name']}');
    print('  Phone: ${customerData['phone']}');
    print('  Governorate: ${customerData['governorate']}');
    print('  Area: ${customerData['area']}');
    print('  Address: ${customerData['address']}');

    // Check if customer already exists
    List<Map<String, dynamic>> existingCustomers = await db.query(
      'customers',
      where: 'phone = ?',
      whereArgs: [customerData['phone']],
    );

    if (existingCustomers.isNotEmpty) {
      print('Warning: Customer already exists in database');
      print('Customer ID: ${existingCustomers.first['id']}');
      print('Customer Name: ${existingCustomers.first['name']}');
      await db.close();
      return;
    }

    // Insert customer
    int customerId = await db.insert('customers', customerData);

    print('Customer added successfully!');
    print('Customer ID: $customerId');
    print('Name: ${customerData['name']}');
    print('Phone: ${customerData['phone']}');
    print('Governorate: ${customerData['governorate']}');
    print('Area: ${customerData['area']}');

    // Verify customer was added
    List<Map<String, dynamic>> addedCustomers = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [customerId],
    );

    if (addedCustomers.isNotEmpty) {
      Map<String, dynamic> addedCustomer = addedCustomers.first;
      print('\nCustomer details:');
      print('  ID: ${addedCustomer['id']}');
      print('  Name: ${addedCustomer['name']}');
      print('  Phone: ${addedCustomer['phone']}');
      print('  Address: ${addedCustomer['address']}');
      print('  Governorate: ${addedCustomer['governorate']}');
      print('  Area: ${addedCustomer['area']}');
      print('  Notes: ${addedCustomer['notes']}');
      print('  Created at: ${addedCustomer['created_at']}');
    }

    await db.close();
    print('\nNile Company customer added successfully to database!');
  } catch (e) {
    print('Error adding customer: $e');
  }
}
