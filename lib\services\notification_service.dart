import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // تهيئة timezone
    tz.initializeTimeZones();

    // إعدادات Android
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );

    // إعدادات iOS
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // إعدادات التهيئة
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    // تهيئة الإشعارات
    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    // يمكن إضافة منطق التنقل هنا
    print('تم النقر على الإشعار: ${response.payload}');
  }

  /// إشعار فاتورة جديدة
  static Future<void> showNewInvoiceNotification({
    required String customerName,
    required String invoiceNumber,
    required double amount,
  }) async {
    await _showNotification(
      id: 1,
      title: 'فاتورة جديدة',
      body:
          'تم إنشاء فاتورة رقم $invoiceNumber للعميل $customerName بمبلغ ${amount.toStringAsFixed(2)} ج.م',
      payload: 'invoice:$invoiceNumber',
    );
  }

  /// إشعار تحصيل جديد
  static Future<void> showNewCollectionNotification({
    required String customerName,
    required double amount,
    required String paymentMethod,
  }) async {
    await _showNotification(
      id: 2,
      title: 'تحصيل جديد',
      body:
          'تم تحصيل مبلغ ${amount.toStringAsFixed(2)} ج.م من العميل $customerName بطريقة $paymentMethod',
      payload: 'collection:$customerName',
    );
  }

  /// إشعار فاتورة متأخرة
  static Future<void> showOverdueInvoiceNotification({
    required String customerName,
    required String invoiceNumber,
    required double remainingAmount,
    required int daysOverdue,
  }) async {
    await _showNotification(
      id: 3,
      title: 'فاتورة متأخرة',
      body:
          'فاتورة رقم $invoiceNumber للعميل $customerName متأخرة $daysOverdue يوم. المبلغ المتبقي: ${remainingAmount.toStringAsFixed(2)} ج.م',
      payload: 'overdue:$invoiceNumber',
    );
  }

  /// إشعار تذكير يومي
  static Future<void> showDailyReminderNotification() async {
    await _showNotification(
      id: 4,
      title: 'تذكير يومي',
      body: 'تذكر مراجعة الفواتير المتأخرة والتحصيلات اليومية',
      payload: 'daily_reminder',
    );
  }

  /// إشعار نسخ احتياطي
  static Future<void> showBackupNotification({
    required bool isSuccess,
    String? errorMessage,
  }) async {
    if (isSuccess) {
      await _showNotification(
        id: 5,
        title: 'نسخ احتياطي ناجح',
        body: 'تم إنشاء نسخة احتياطية بنجاح',
        payload: 'backup_success',
      );
    } else {
      await _showNotification(
        id: 6,
        title: 'خطأ في النسخ الاحتياطي',
        body: errorMessage ?? 'حدث خطأ أثناء إنشاء النسخة الاحتياطية',
        payload: 'backup_error',
      );
    }
  }

  /// إشعار مخصص
  static Future<void> showCustomNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await _showNotification(id: id, title: title, body: body, payload: payload);
  }

  /// عرض إشعار فوري
  static Future<void> _showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    const androidDetails = AndroidNotificationDetails(
      'atlas_medical_supplies',
      'InvoFast',
      channelDescription: 'إشعارات تطبيق InvoFast',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      enableLights: true,
      color: Color(0xFF4A90E2),
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, details, payload: payload);
  }

  /// جدولة إشعار متكرر
  static Future<void> scheduleRepeatingNotification({
    required int id,
    required String title,
    required String body,
    required int hour,
    required int minute,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    const androidDetails = AndroidNotificationDetails(
      'atlas_medical_supplies_repeating',
      'InvoFast - متكرر',
              channelDescription: 'إشعارات متكررة لتطبيق InvoFast',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );

    const iosDetails = DarwinNotificationDetails();

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.periodicallyShow(
      id,
      title,
      body,
      RepeatInterval.daily,
      details,
      payload: payload,
    );
  }

  /// جدولة إشعار في وقت محدد
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    const androidDetails = AndroidNotificationDetails(
      'atlas_medical_supplies_scheduled',
              'InvoFast - مجدول',
              channelDescription: 'إشعارات مجدولة لتطبيق InvoFast',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
    );
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// الحصول على الإشعارات المجدولة
  static Future<List<PendingNotificationRequest>>
  getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  /// إشعار تذكير بالتحصيل
  static Future<void> showCollectionReminderNotification({
    required String customerName,
    required double remainingAmount,
    required String invoiceNumber,
  }) async {
    await _showNotification(
      id: 7,
      title: 'تذكير بالتحصيل',
      body:
          'تذكر تحصيل المبلغ المتبقي ${remainingAmount.toStringAsFixed(2)} ج.م من العميل $customerName (فاتورة رقم $invoiceNumber)',
      payload: 'collection_reminder:$invoiceNumber',
    );
  }

  /// إشعار إحصائيات يومية
  static Future<void> showDailyStatsNotification({
    required int invoicesCount,
    required int collectionsCount,
    required double totalSales,
    required double totalCollections,
  }) async {
    await _showNotification(
      id: 8,
      title: 'الإحصائيات اليومية',
      body:
          'اليوم: $invoicesCount فاتورة، $collectionsCount تحصيل، إجمالي المبيعات: ${totalSales.toStringAsFixed(2)} ج.م، إجمالي التحصيلات: ${totalCollections.toStringAsFixed(2)} ج.م',
      payload: 'daily_stats',
    );
  }

  /// إشعار تحذير المخزون
  static Future<void> showLowStockNotification({
    required String productName,
    required int currentStock,
  }) async {
    await _showNotification(
      id: 9,
      title: 'تحذير المخزون',
      body:
          'المنتج $productName منخفض في المخزون. الكمية الحالية: $currentStock',
      payload: 'low_stock:$productName',
    );
  }

  /// إشعار مزامنة البيانات
  static Future<void> showSyncNotification({
    required bool isSuccess,
    String? errorMessage,
  }) async {
    if (isSuccess) {
      await _showNotification(
        id: 10,
        title: 'مزامنة ناجحة',
        body: 'تم مزامنة البيانات بنجاح',
        payload: 'sync_success',
      );
    } else {
      await _showNotification(
        id: 11,
        title: 'خطأ في المزامنة',
        body: errorMessage ?? 'حدث خطأ أثناء مزامنة البيانات',
        payload: 'sync_error',
      );
    }
  }

  /// إعداد الإشعارات التلقائية
  static Future<void> setupAutomaticNotifications() async {
    // إشعار يومي في الساعة 9 صباحاً
    await scheduleRepeatingNotification(
      id: 100,
      title: 'تذكير يومي',
      body: 'تذكر مراجعة الفواتير والتحصيلات اليومية',
      hour: 9,
      minute: 0,
      payload: 'daily_reminder',
    );

    // إشعار أسبوعي يوم الأحد في الساعة 10 صباحاً
    await scheduleNotification(
      id: 101,
      title: 'تقرير أسبوعي',
      body: 'حان وقت مراجعة التقرير الأسبوعي للمبيعات والتحصيلات',
      scheduledDate: _getNextSundayAt10AM(),
      payload: 'weekly_report',
    );
  }

  /// الحصول على يوم الأحد القادم الساعة 10 صباحاً
  static DateTime _getNextSundayAt10AM() {
    final now = DateTime.now();
    final daysUntilSunday = (7 - now.weekday) % 7;
    final nextSunday = now.add(Duration(days: daysUntilSunday));
    return DateTime(nextSunday.year, nextSunday.month, nextSunday.day, 10, 0);
  }

  /// عرض إشعار تجريبي
  static Future<void> showTestNotification(BuildContext context) async {
    try {
      await showCustomNotification(
        id: 999,
        title: 'إشعار تجريبي',
        body: 'هذا إشعار تجريبي لاختبار نظام الإشعارات',
        payload: 'test_notification',
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال الإشعار التجريبي'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
