import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'google_drive_service.dart';

class AutoBackupService {
  static Timer? _backupTimer;
  static bool _isRunning = false;
  static const String _lastBackupKey = 'last_backup_time';
  static const String _autoBackupEnabledKey = 'auto_backup_enabled';

  // بدء النسخ الاحتياطية التلقائية
  static Future<void> startAutoBackup() async {
    if (_isRunning) return;

    try {
      // التحقق من تفعيل النسخ الاحتياطية التلقائية
      final prefs = await SharedPreferences.getInstance();
      final isEnabled = prefs.getBool(_autoBackupEnabledKey) ?? true;

      if (!isEnabled) {
        print('⏭️ النسخ الاحتياطية التلقائية معطلة');
        return;
      }

      _isRunning = true;
      print('🔄 بدء النسخ الاحتياطية التلقائية...');

      // تشغيل النسخ الاحتياطية كل ثانية
      _backupTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
        await _performBackup();
      });

      // عمل نسخة احتياطية فورية
      await _performBackup();
    } catch (e) {
      print('❌ خطأ في بدء النسخ الاحتياطية التلقائية: $e');
      _isRunning = false;
    }
  }

  // إيقاف النسخ الاحتياطية التلقائية
  static void stopAutoBackup() {
    _backupTimer?.cancel();
    _backupTimer = null;
    _isRunning = false;
    print('⏹️ تم إيقاف النسخ الاحتياطية التلقائية');
  }

  // تنفيذ النسخ الاحتياطية
  static Future<void> _performBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackup = prefs.getString(_lastBackupKey);
      final now = DateTime.now();

      // التحقق من عدم وجود نسخة احتياطية حديثة جداً (أقل من ثانية)
      if (lastBackup != null) {
        final lastBackupTime = DateTime.parse(lastBackup);
        final difference = now.difference(lastBackupTime);
        if (difference.inSeconds < 1) {
          return; // تخطي النسخ الاحتياطية إذا كانت حديثة جداً
        }
      }

      // رفع النسخة الاحتياطية
      final success = await GoogleDriveService.uploadBackup();
      
      if (success) {
        // حفظ وقت آخر نسخة احتياطية
        await prefs.setString(_lastBackupKey, now.toIso8601String());
        print('✅ تم رفع النسخة الاحتياطية التلقائية: ${now.toString()}');
        
        // تنظيف النسخ الاحتياطية القديمة
        await GoogleDriveService.cleanupOldBackups();
      } else {
        print('⚠️ فشل في رفع النسخة الاحتياطية التلقائية');
      }
    } catch (e) {
      print('❌ خطأ في النسخ الاحتياطية التلقائية: $e');
    }
  }

  // تفعيل/تعطيل النسخ الاحتياطية التلقائية
  static Future<void> setAutoBackupEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoBackupEnabledKey, enabled);
    
    if (enabled) {
      await startAutoBackup();
    } else {
      stopAutoBackup();
    }
    
    print(enabled ? '✅ تم تفعيل النسخ الاحتياطية التلقائية' : '❌ تم تعطيل النسخ الاحتياطية التلقائية');
  }

  // التحقق من حالة النسخ الاحتياطية التلقائية
  static Future<bool> isAutoBackupEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoBackupEnabledKey) ?? true;
  }

  // الحصول على وقت آخر نسخة احتياطية
  static Future<DateTime?> getLastBackupTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastBackup = prefs.getString(_lastBackupKey);
    if (lastBackup != null) {
      return DateTime.parse(lastBackup);
    }
    return null;
  }

  // التحقق من حالة الخدمة
  static bool get isRunning => _isRunning;

  // عمل نسخة احتياطية يدوية
  static Future<bool> performManualBackup() async {
    try {
      print('🔄 بدء النسخة الاحتياطية اليدوية...');
      final success = await GoogleDriveService.uploadBackup();
      
      if (success) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastBackupKey, DateTime.now().toIso8601String());
        print('✅ تم رفع النسخة الاحتياطية اليدوية بنجاح');
      } else {
        print('❌ فشل في رفع النسخة الاحتياطية اليدوية');
      }
      
      return success;
    } catch (e) {
      print('❌ خطأ في النسخة الاحتياطية اليدوية: $e');
      return false;
    }
  }

  // إحصائيات النسخ الاحتياطية
  static Future<Map<String, dynamic>> getBackupStats() async {
    try {
      final lastBackup = await getLastBackupTime();
      final isEnabled = await isAutoBackupEnabled();
      final backups = await GoogleDriveService.getBackups();
      
      return {
        'is_enabled': isEnabled,
        'is_running': _isRunning,
        'last_backup': lastBackup?.toIso8601String(),
        'total_backups': backups.length,
        'latest_backup': backups.isNotEmpty ? backups.first : null,
      };
    } catch (e) {
      print('❌ خطأ في جلب إحصائيات النسخ الاحتياطية: $e');
      return {
        'is_enabled': false,
        'is_running': false,
        'last_backup': null,
        'total_backups': 0,
        'latest_backup': null,
      };
    }
  }
} 
