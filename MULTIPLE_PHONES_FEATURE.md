# ميزة أرقام الهواتف المتعددة للعملاء

## نظرة عامة

تم إضافة ميزة جديدة تسمح للعملاء بإضافة أكثر من رقم هاتف واحد، مع إمكانية تحديد رقم أساسي وتصنيف الأرقام حسب النوع.

## الميزات الجديدة

### 1. جدول أرقام الهواتف الجديد
- **الجدول:** `customer_phones`
- **الحقول:**
  - `id`: معرف فريد
  - `customer_id`: معرف العميل (مفتاح خارجي)
  - `phone`: رقم الهاتف
  - `phone_type`: نوع الهاتف (الرئيسي، العمل، المنزل، إلخ)
  - `is_primary`: هل هو الرقم الأساسي (0 أو 1)
  - `notes`: ملاحظات إضافية
  - `created_at`: تاريخ الإنشاء

### 2. دوال قاعدة البيانات الجديدة

#### إدارة أرقام الهواتف:
- `insertCustomerPhone()`: إضافة رقم هاتف جديد
- `getCustomerPhones()`: جلب جميع أرقام هاتف العميل
- `getPrimaryCustomerPhone()`: جلب الرقم الأساسي للعميل
- `updateCustomerPhone()`: تحديث رقم هاتف
- `deleteCustomerPhone()`: حذف رقم هاتف
- `setPrimaryPhone()`: تعيين رقم كأساسي
- `searchCustomersByPhone()`: البحث في العملاء حسب رقم الهاتف

### 3. واجهات المستخدم الجديدة

#### أ. واجهة إدارة أرقام الهواتف (`CustomerPhonesWidget`)
- عرض جميع أرقام هاتف العميل
- إضافة رقم هاتف جديد
- تعديل رقم هاتف موجود
- حذف رقم هاتف
- تعيين رقم كأساسي
- تصنيف الأرقام حسب النوع

#### ب. تحديث صفحة إضافة/تعديل العميل
- إضافة قسم أرقام الهواتف
- إمكانية إضافة أرقام متعددة أثناء إنشاء العميل
- تحديد الرقم الأساسي
- التحقق من عدم تكرار الأرقام

#### ج. تحديث صفحة تفاصيل العميل
- إضافة زر إدارة أرقام الهواتف في AppBar
- عرض الرقم الأساسي في تفاصيل العميل

### 4. نموذج البيانات الجديد

#### `CustomerPhone` Model:
```dart
class CustomerPhone {
  final int? id;
  final int customerId;
  final String phone;
  final String phoneType;
  final bool isPrimary;
  final String? notes;
  final String? createdAt;
}
```

## كيفية الاستخدام

### 1. إضافة عميل جديد مع أرقام هواتف متعددة:
1. افتح صفحة "إضافة عميل جديد"
2. أدخل اسم العميل والمعلومات الأساسية
3. في قسم "أرقام الهواتف"، اضغط على "إضافة رقم"
4. أدخل رقم الهاتف والنوع والملاحظات
5. حدد ما إذا كان الرقم أساسي أم لا
6. كرر العملية لإضافة أرقام أخرى
7. احفظ العميل

### 2. إدارة أرقام هواتف عميل موجود:
1. افتح صفحة تفاصيل العميل
2. اضغط على أيقونة الهاتف في AppBar
3. ستظهر صفحة إدارة أرقام الهواتف
4. يمكنك إضافة، تعديل، أو حذف أرقام الهواتف
5. يمكنك تعيين أي رقم كأساسي

### 3. البحث في العملاء:
- تم تحديث دالة البحث لتشمل جميع أرقام الهواتف
- يمكن البحث بأي رقم هاتف للعميل

## التحديثات في قاعدة البيانات

### إصدار قاعدة البيانات: 11
- إضافة جدول `customer_phones`
- نقل أرقام الهواتف الموجودة من جدول `customers` إلى الجدول الجديد
- تحديث دوال البحث والاستعلام

### الترحيل التلقائي:
- عند تشغيل التطبيق، سيتم ترقية قاعدة البيانات تلقائياً
- نقل أرقام الهواتف الموجودة كأرقام أساسية
- الحفاظ على التوافق مع البيانات الموجودة

## المزايا

### 1. مرونة أكبر:
- إمكانية إضافة أرقام هواتف متعددة لكل عميل
- تصنيف الأرقام حسب النوع (الرئيسي، العمل، المنزل)
- إضافة ملاحظات لكل رقم

### 2. تحسين التواصل:
- سهولة الوصول لجميع أرقام العميل
- تحديد الرقم الأساسي للتواصل المباشر
- إمكانية إضافة أرقام احتياطية

### 3. تحسين البحث:
- البحث في جميع أرقام الهواتف
- نتائج بحث أكثر دقة
- سهولة العثور على العملاء

### 4. واجهة مستخدم محسنة:
- عرض منظم لأرقام الهواتف
- أيقونات واضحة للرقم الأساسي
- إمكانية التعديل والحذف بسهولة

## ملاحظات تقنية

### 1. التوافق مع الإصدارات السابقة:
- جميع البيانات الموجودة محفوظة
- التطبيق يعمل مع الإصدارات السابقة
- ترقية تلقائية لقاعدة البيانات

### 2. الأداء:
- فهارس محسنة للبحث السريع
- استعلامات محسنة للعرض
- تحميل تدريجي للبيانات

### 3. الأمان:
- التحقق من صحة أرقام الهواتف
- منع تكرار الأرقام
- حماية من الأخطاء

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `lib/models/customer_phone.dart`
- `lib/widgets/customer_phones_widget.dart`
- `MULTIPLE_PHONES_FEATURE.md`

### ملفات محدثة:
- `lib/database/database_helper.dart`
- `lib/screens/customer_details_screen.dart`
- `lib/screens/add_edit_customer_screen.dart`

## الخطوات التالية

### 1. اختبار الميزة:
- اختبار إضافة أرقام هواتف متعددة
- اختبار التعديل والحذف
- اختبار البحث والتصفية

### 2. تحسينات مستقبلية:
- إضافة أنواع هواتف مخصصة
- إمكانية استيراد أرقام الهواتف
- إضافة إشعارات للتواصل
- تكامل مع تطبيقات الهاتف

### 3. التوثيق:
- تحديث دليل المستخدم
- إضافة أمثلة عملية
- إنشاء فيديوهات تعليمية

---

**تاريخ الإضافة:** 2024-12-19  
**الإصدار:** 1.0  
**المطور:** Atlas Medical Supplies Team 