import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'lib/database/database_helper.dart';

void main() async {
  print('🚀 بدء إضافة عميل شركة النيل...');
  
  try {
    // إنشاء مثيل من DatabaseHelper
    final dbHelper = DatabaseHelper();
    
    // بيانات العميل الجديد
    final customerData = {
      'name': 'شركة النيل',
      'phone': '01110473536',
      'address': 'أبو حمص',
      'governorate': 'البحيرة',
      'area': 'أبو حمص',
      'notes': 'تم الإضافة بتاريخ ${DateTime.now().toString().split(' ')[0]}',
    };
    
    print('📋 بيانات العميل:');
    print('   الاسم: ${customerData['name']}');
    print('   الهاتف: ${customerData['phone']}');
    print('   المحافظة: ${customerData['governorate']}');
    print('   المنطقة: ${customerData['area']}');
    print('   العنوان: ${customerData['address']}');
    
    // التحقق من عدم وجود العميل مسبقاً
    final existingCustomer = await dbHelper.getCustomerByPhone(customerData['phone']!);
    if (existingCustomer != null) {
      print('⚠️  تحذير: العميل موجود بالفعل في قاعدة البيانات');
      print('   معرف العميل: ${existingCustomer['id']}');
      print('   الاسم: ${existingCustomer['name']}');
      return;
    }
    
    // إضافة العميل إلى قاعدة البيانات
    final customerId = await dbHelper.insertCustomer(customerData);
    
    print('✅ تم إضافة العميل بنجاح!');
    print('   معرف العميل: $customerId');
    print('   الاسم: ${customerData['name']}');
    print('   الهاتف: ${customerData['phone']}');
    print('   المحافظة: ${customerData['governorate']}');
    print('   المنطقة: ${customerData['area']}');
    
    // التحقق من إضافة العميل
    final addedCustomer = await dbHelper.getCustomer(customerId);
    if (addedCustomer != null) {
      print('\n📊 تفاصيل العميل المضاف:');
      print('   المعرف: ${addedCustomer['id']}');
      print('   الاسم: ${addedCustomer['name']}');
      print('   الهاتف: ${addedCustomer['phone']}');
      print('   العنوان: ${addedCustomer['address']}');
      print('   المحافظة: ${addedCustomer['governorate']}');
      print('   المنطقة: ${addedCustomer['area']}');
      print('   الملاحظات: ${addedCustomer['notes']}');
      print('   تاريخ الإنشاء: ${addedCustomer['created_at']}');
    }
    
    print('\n🎉 تم إضافة عميل شركة النيل بنجاح إلى قاعدة البيانات!');
    
  } catch (e) {
    print('❌ خطأ في إضافة العميل: $e');
  }
} 