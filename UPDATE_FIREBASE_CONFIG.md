# 🔧 تحديث إعدادات Firebase

## 📝 خطوات تحديث firebase_options.dart

### 1️⃣ الحصول على الإعدادات من Firebase Console

1. **اذهب إلى Firebase Console:**
   - https://console.firebase.google.com/
   - اختر مشروعك: `atlas-medical-supplies`

2. **افتح Project Settings:**
   - انقر على ⚙️ (إعدادات المشروع)
   - اختر "General" tab

3. **انزل إلى "Your apps" section:**
   - ستجد تطبيق Android
   - انقر عليه

4. **انسخ الإعدادات التالية:**
   - `apiKey`
   - `appId`
   - `messagingSenderId`
   - `projectId`

### 2️⃣ تحديث firebase_options.dart

افتح الملف: `lib/firebase_options.dart`

استبدل القيم في `FirebaseOptions android`:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'YOUR-ACTUAL-API-KEY-HERE',           // ← استبدل هذا
  appId: 'YOUR-ACTUAL-APP-ID-HERE',             // ← استبدل هذا
  messagingSenderId: 'YOUR-ACTUAL-SENDER-ID',   // ← استبدل هذا
  projectId: 'atlas-medical-supplies',          // ← تأكد من هذا
  storageBucket: 'atlas-medical-supplies.appspot.com',
);
```

### 3️⃣ مثال على الإعدادات الحقيقية

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz',
  appId: '1:123456789012:android:abcdef1234567890',
  messagingSenderId: '123456789012',
  projectId: 'atlas-medical-supplies',
  storageBucket: 'atlas-medical-supplies.appspot.com',
);
```

### 4️⃣ تحديث google-services.json

1. **تحميل الملف من Firebase Console:**
   - في Project Settings > General
   - انزل إلى "Your apps"
   - انقر على "Download google-services.json"

2. **استبدال الملف:**
   - احفظ الملف في: `android/app/google-services.json`
   - استبدل الملف الموجود

### 5️⃣ اختبار الربط

بعد التحديث:

```bash
flutter clean
flutter pub get
flutter run
```

### 6️⃣ التحقق من الربط

1. **شغل التطبيق**
2. **سجل دخول:**
   - رقم الهاتف: `01125312343`
   - كلمة المرور: `123456`

3. **تحقق من Firebase Console:**
   - اذهب إلى Firestore Database
   - ستجد collections جديدة

## ✅ علامات النجاح:

- ✅ التطبيق يعمل بدون أخطاء
- ✅ تسجيل الدخول يعمل
- ✅ البيانات تظهر في Firestore
- ✅ لا توجد أخطاء في Console

## 🚨 إذا واجهت مشاكل:

1. **تأكد من صحة الإعدادات**
2. **تحقق من google-services.json**
3. **أعد تشغيل التطبيق**
4. **تحقق من Firebase Console** 