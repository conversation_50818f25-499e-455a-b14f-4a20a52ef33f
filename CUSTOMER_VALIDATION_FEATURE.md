# ميزة التحقق من صحة بيانات العميل

## نظرة عامة

تم تحديث شاشة إضافة/تعديل العميل لجعل حقول الاسم ورقم الهاتف والمحافظة إجبارية، مع إضافة تحقق شامل من صحة البيانات وعدم تكرارها.

## الميزات المضافة

### 1. الحقول الإجبارية
- **اسم العميل**: حقل إجباري مع علامة (*)
- **رقم الهاتف**: حقل إجباري مع علامة (*) وتحقق من صحة الرقم
- **المحافظة**: حقل إجباري مع علامة (*)

### 2. التحقق من صحة البيانات
- **اسم العميل**: لا يمكن أن يكون فارغاً
- **رقم الهاتف**: 
  - لا يمكن أن يكون فارغاً
  - يجب أن يكون رقم هاتف مصري صحيح (01xxxxxxxxx)
  - لا يمكن تكراره مع عميل آخر
- **المحافظة**: لا يمكن أن تكون فارغة

### 3. رسائل خطأ واضحة
- رسائل خطأ باللغة العربية
- توجيه المستخدم لتصحيح البيانات
- منع الحفظ حتى تصحيح جميع الأخطاء

## الملفات المحدثة

### 1. `lib/screens/add_edit_customer_screen.dart`

#### تحديث حقل الاسم
```dart
labelText: 'اسم العميل *',
validator: (value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال اسم العميل';
  }
  return null;
},
```

#### تحديث حقل رقم الهاتف
```dart
labelText: 'رقم الهاتف *',
validator: (value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال رقم الهاتف';
  }
  // التحقق من صحة رقم الهاتف (رقم مصري)
  final phoneRegex = RegExp(r'^(01)[0-2,5]{1}[0-9]{8}$');
  if (!phoneRegex.hasMatch(value.trim())) {
    return 'يرجى إدخال رقم هاتف مصري صحيح';
  }
  return null;
},
```

#### تحديث حقل المحافظة
```dart
labelText: 'المحافظة *',
validator: (value) {
  if (value == null || value.isEmpty) {
    return 'يرجى اختيار المحافظة';
  }
  return null;
},
```

#### تحقق إضافي في دالة الحفظ
```dart
// تحقق إضافي من البيانات الإجبارية
final name = _nameController.text.trim();
final phone = _phoneController.text.trim();
final governorate = _selectedGovernorate;

if (name.isEmpty) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('يرجى إدخال اسم العميل'),
      backgroundColor: Colors.red,
    ),
  );
  return;
}
```

#### التحقق من عدم تكرار رقم الهاتف
```dart
// التحقق من عدم تكرار رقم الهاتف
final existingCustomer = await dbHelper.getCustomerByPhone(phone);
if (existingCustomer != null) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('رقم الهاتف موجود بالفعل في قاعدة البيانات'),
      backgroundColor: Colors.red,
    ),
  );
  return;
}
```

### 2. `lib/database/database_helper.dart`

#### إضافة دالة جديدة
```dart
Future<Map<String, dynamic>?> getCustomerByPhone(String phone) async {
  final db = await database;
  final result = await db.query(
    'customers',
    where: 'phone = ?',
    whereArgs: [phone],
    limit: 1,
  );
  return result.isNotEmpty ? result.first : null;
}
```

## أمثلة على التحقق

### 1. اسم العميل فارغ
```
❌ خطأ: يرجى إدخال اسم العميل
```

### 2. رقم الهاتف فارغ
```
❌ خطأ: يرجى إدخال رقم الهاتف
```

### 3. رقم الهاتف غير صحيح
```
❌ خطأ: يرجى إدخال رقم هاتف مصري صحيح
```

### 4. رقم الهاتف مكرر
```
❌ خطأ: رقم الهاتف موجود بالفعل في قاعدة البيانات
```

### 5. المحافظة غير محددة
```
❌ خطأ: يرجى اختيار المحافظة
```

## الميزات التقنية

### 1. التحقق المزدوج
- **تحقق في الواجهة**: باستخدام `validator` في `TextFormField`
- **تحقق في الخادم**: تحقق إضافي في دالة الحفظ
- **تحقق من قاعدة البيانات**: التحقق من عدم تكرار رقم الهاتف

### 2. التعبير النمطي (Regex)
```dart
final phoneRegex = RegExp(r'^(01)[0-2,5]{1}[0-9]{8}$');
```
- يتحقق من أن الرقم يبدأ بـ 01
- الرقم الثاني يجب أن يكون 0، 1، 2، أو 5
- إجمالي 11 رقم

### 3. معالجة الأخطاء
- رسائل خطأ واضحة باللغة العربية
- منع الحفظ حتى تصحيح جميع الأخطاء
- إعادة تعيين حالة التحميل في حالة الخطأ

## الفوائد

### 1. جودة البيانات
- ضمان إدخال البيانات المطلوبة
- منع تكرار أرقام الهاتف
- تحسين دقة قاعدة البيانات

### 2. تجربة المستخدم
- رسائل خطأ واضحة
- توجيه المستخدم لتصحيح الأخطاء
- منع الحفظ غير الصحيح

### 3. صحة النظام
- منع البيانات المفقودة
- ضمان تكامل البيانات
- تحسين أداء النظام

## الاستخدام

### للمستخدمين الجدد
1. أدخل اسم العميل (إجباري)
2. أدخل رقم الهاتف المصري (إجباري)
3. اختر المحافظة (إجبارية)
4. أدخل البيانات الأخرى (اختيارية)
5. اضغط حفظ

### للمستخدمين الحاليين
- نفس الواجهة مع تحقق محسن
- رسائل خطأ أوضح
- منع الحفظ غير الصحيح

## التحديثات المستقبلية

### 1. تحقق إضافي
- التحقق من صحة العنوان
- التحقق من صحة البريد الإلكتروني
- التحقق من صحة الرقم القومي

### 2. تحسينات الواجهة
- تمييز بصري للحقول الإجبارية
- رسائل مساعدة للحقول
- اقتراحات تلقائية للمحافظات

### 3. تحسينات قاعدة البيانات
- فهارس لتحسين الأداء
- قيود فريدة على مستوى قاعدة البيانات
- نسخ احتياطية تلقائية 