# 🚀 ربط Firebase - دليل سريع

## ⚡ الخطوات السريعة:

### 1️⃣ إنشاء مشروع Firebase
1. اذهب إلى: https://console.firebase.google.com/
2. انقر "Create project"
3. اسم المشروع: `atlas-medical-supplies`
4. انقر "Create project"

### 2️⃣ إضافة تطبيق Android
1. انقر أيقونة Android 📱
2. Package name: `com.atlas.medical.atlas_medical_supplies`
3. انقر "Register app"
4. حمل `google-services.json`
5. ضعه في `android/app/google-services.json`

### 3️⃣ تفعيل الخدمات
1. **Authentication:**
   - اذهب إلى Authentication
   - فعّل Email/Password
   
2. **Firestore Database:**
   - اذهب إلى Firestore Database
   - انقر "Create database"
   - اختر "Start in test mode"

### 4️⃣ تحديث الإعدادات
1. في Project Settings > General
2. انسخ الإعدادات من تطبيق Android
3. حدث `lib/firebase_options.dart`

### 5️⃣ اختبار الربط
```bash
flutter clean
flutter pub get
flutter run
```

## ✅ علامات النجاح:
- التطبيق يعمل بدون أخطاء
- تسجيل الدخول يعمل
- البيانات تظهر في Firestore

## 📞 للمساعدة:
- راجع `FIREBASE_SETUP_STEPS.md` للتفاصيل
- راجع `UPDATE_FIREBASE_CONFIG.md` لتحديث الإعدادات 