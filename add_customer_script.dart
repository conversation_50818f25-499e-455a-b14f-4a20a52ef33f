import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'lib/database/database_helper.dart';

void main() async {
  print('🚀 بدء إضافة العميل الجديد...');
  
  try {
    final dbHelper = DatabaseHelper();
    
    // بيانات العميل الجديد
    final customerData = {
      'name': 'شركة النيل',
      'phone': '', // سيتم إدخال رقم الهاتف لاحقاً
      'address': 'أبو حمص - محافظة البحيرة',
      'notes': 'مكتب أية البحيرة - كفر الدوار\nمكتب مكة - البحيرة - كفر الدوار',
      'governorate': 'البحيرة',
      'area': 'أبو حمص',
    };
    
    // التحقق من عدم وجود العميل بالفعل
    final existingCustomer = await dbHelper.getCustomerByPhone(customerData['phone'] ?? '');
    if (existingCustomer != null) {
      print('⚠️ العميل موجود بالفعل في قاعدة البيانات');
      return;
    }
    
    // إدراج العميل الجديد
    final customerId = await dbHelper.insertCustomer(customerData);
    
    print('✅ تم إضافة العميل بنجاح!');
    print('📋 تفاصيل العميل:');
    print('   - الاسم: ${customerData['name']}');
    print('   - المحافظة: ${customerData['governorate']}');
    print('   - المنطقة: ${customerData['area']}');
    print('   - العنوان: ${customerData['address']}');
    print('   - الملاحظات: ${customerData['notes']}');
    print('   - معرف العميل: $customerId');
    
  } catch (e) {
    print('❌ خطأ في إضافة العميل: $e');
  }
} 