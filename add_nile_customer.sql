-- إضافة عميل شركة النيل إلى قاعدة البيانات
-- تاريخ الإنشاء: 2024-12-19

-- التحقق من عدم وجود العميل مسبقاً
SELECT id, name, phone FROM customers WHERE phone = '01110473536';

-- إضافة العميل الجديد
INSERT INTO customers (
    name, 
    phone, 
    address, 
    governorate, 
    area, 
    notes, 
    created_at
) VALUES (
    'شركة النيل',
    '01110473536',
    'أبو حمص',
    'البحيرة',
    'أبو حمص',
    'تم الإضافة بتاريخ 2024-12-19',
    datetime('now')
);

-- التحقق من إضافة العميل
SELECT 
    id,
    name,
    phone,
    address,
    governorate,
    area,
    notes,
    created_at
FROM customers 
WHERE phone = '01110473536';

-- عرض جميع العملاء في محافظة البحيرة
SELECT 
    id,
    name,
    phone,
    area,
    created_at
FROM customers 
WHERE governorate = 'البحيرة'
ORDER BY name ASC;

-- إحصائيات العملاء حسب المحافظة
SELECT 
    governorate,
    COUNT(*) as customer_count
FROM customers 
WHERE governorate IS NOT NULL AND governorate != ''
GROUP BY governorate
ORDER BY customer_count DESC; 