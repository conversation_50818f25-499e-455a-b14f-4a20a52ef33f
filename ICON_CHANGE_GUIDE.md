# 🎨 دليل تغيير أيقونة التطبيق

## 📋 متطلبات الأيقونة

### 🖼️ المواصفات المطلوبة:
- **التنسيق**: PNG أو JPG
- **الحجم**: 1024x1024 بكسل (مقترح)
- **الشكل**: مربع
- **الخلفية**: شفافة أو صلبة
- **الجودة**: عالية الدقة

## 🔄 خطوات تغيير الأيقونة

### 1. **إضافة الأيقونة الجديدة**
```bash
# انسخ الأيقونة الجديدة إلى مجلد الصور
cp your_new_icon.png assets/images/
```

### 2. **تحديث إعدادات الأيقونة في `pubspec.yaml`**
```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/your_new_icon.png"  # ← غير هذا
  min_sdk_android: 23
  adaptive_icon_background: "#40E0D0"
  adaptive_icon_foreground: "assets/images/your_new_icon.png"  # ← غير هذا
  web:
    generate: true
    image_path: "assets/images/your_new_icon.png"  # ← غير هذا
    background_color: "#40E0D0"
    theme_color: "#40E0D0"
  windows:
    generate: true
    image_path: "assets/images/your_new_icon.png"  # ← غير هذا
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/your_new_icon.png"  # ← غير هذا
```

### 3. **تطبيق الأيقونة الجديدة**
```bash
# تحديث التبعيات
flutter pub get

# إنشاء الأيقونات لجميع المنصات
flutter pub run flutter_launcher_icons:main
```

### 4. **تنظيف وإعادة بناء**
```bash
# تنظيف المشروع
flutter clean

# إعادة بناء
flutter pub get

# تشغيل التطبيق
flutter run
```

## 🎯 خيارات متقدمة

### **أيقونة Android التكيفية**
```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  adaptive_icon_background: "#40E0D0"  # خلفية الأيقونة
  adaptive_icon_foreground: "assets/images/icon_foreground.png"  # مقدمة الأيقونة
```

### **أيقونة iOS**
```yaml
flutter_launcher_icons:
  ios: true
  image_path: "assets/images/ios_icon.png"
  remove_alpha_ios: true  # إزالة الشفافية لـ iOS
```

### **أيقونة Web**
```yaml
flutter_launcher_icons:
  web:
    generate: true
    image_path: "assets/images/web_icon.png"
    background_color: "#40E0D0"
    theme_color: "#40E0D0"
```

## 🛠️ أدوات مفيدة

### **إنشاء أيقونة جديدة برمجياً**
```bash
# تشغيل سكريبت إنشاء الأيقونة
dart create_new_icon.dart
```

### **تحويل الصور**
```bash
# تحويل صورة إلى الحجم المطلوب
convert input.png -resize 1024x1024 output.png
```

### **ضغط الصور**
```bash
# ضغط الصورة مع الحفاظ على الجودة
pngquant --quality=85-95 input.png
```

## 📱 اختبار الأيقونة

### **Android**
```bash
# بناء APK
flutter build apk

# تثبيت على الجهاز
flutter install
```

### **iOS**
```bash
# بناء iOS
flutter build ios

# فتح في Xcode
open ios/Runner.xcworkspace
```

### **Web**
```bash
# بناء Web
flutter build web

# تشغيل خادم محلي
flutter run -d chrome
```

## ⚠️ ملاحظات مهمة

1. **Android**: الأيقونة التكيفية تتطلب خلفية ومقدمة منفصلتين
2. **iOS**: لا تدعم الشفافية، استخدم خلفية صلبة
3. **Web**: يحتاج إلى favicon و PWA icons
4. **Windows/macOS**: يدعمان الشفافية

## 🔧 استكشاف الأخطاء

### **الأيقونة لا تظهر**
```bash
# تنظيف كامل
flutter clean
flutter pub get
flutter pub run flutter_launcher_icons:main
```

### **خطأ في الأيقونة**
- تأكد من أن الصورة مربعة
- تأكد من الحجم (1024x1024 مقترح)
- تأكد من التنسيق (PNG/JPG)

### **أيقونة Android لا تتغير**
```bash
# حذف APK القديم
adb uninstall com.atlas.medical.atlas_medical_supplies

# إعادة تثبيت
flutter install
```

## 📊 أحجام الأيقونات المطلوبة

| المنصة | الحجم | الوصف |
|--------|-------|-------|
| Android | 1024x1024 | الأيقونة الرئيسية |
| Android | 432x432 | الأيقونة التكيفية |
| iOS | 1024x1024 | App Store |
| iOS | 180x180 | iPhone |
| Web | 192x192 | PWA |
| Web | 512x512 | PWA |
| Windows | 48x48 | Desktop |
| macOS | 1024x1024 | App Store | 