import 'package:flutter/material.dart';
import 'dart:io';
import '../services/storage_service.dart';
import '../services/auth_service.dart';

class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({Key? key}) : super(key: key);

  @override
  State<StorageManagementScreen> createState() => _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  bool _isLoading = false;
  Map<String, dynamic> _storageStats = {};
  List<Map<String, dynamic>> _savedFiles = [];
  String? _currentUserName;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final userName = await AuthService.getCurrentUserName();
      setState(() {
        _currentUserName = userName;
      });
    } catch (e) {
      print('❌ خطأ في تحميل اسم المستخدم: $e');
    }
  }

  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await StorageService.getStorageStatistics();
      final files = await StorageService.getSavedDataFiles();
      
      setState(() {
        _storageStats = stats;
        _savedFiles = files;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل معلومات التخزين: $e');
    }
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final granted = await StorageService.requestStoragePermissions();
      
      if (granted) {
        _showSuccessSnackBar('تم منح أذونات التخزين بنجاح');
        await _loadStorageInfo();
      } else {
        _showErrorSnackBar('لم يتم منح أذونات التخزين المطلوبة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في طلب الأذونات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await StorageService.saveAllAppData();
      
      if (result['success']) {
        _showSuccessSnackBar('تم حفظ جميع البيانات بنجاح');
        await _loadStorageInfo();
        
        // عرض تفاصيل الحفظ
        _showSaveDetailsDialog(result);
      } else {
        _showErrorSnackBar(result['message']);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createAutoBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await StorageService.createAutoBackup();
      
      if (result['success']) {
        _showSuccessSnackBar('تم إنشاء النسخة الاحتياطية التلقائية بنجاح');
        await _loadStorageInfo();
      } else {
        _showErrorSnackBar(result['message']);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء النسخة الاحتياطية: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreFromFile(String filePath) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'سيتم استبدال جميع البيانات الحالية بالبيانات من الملف المحدد. هل أنت متأكد؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await StorageService.restoreAppDataFromFile(filePath);
      
      if (result['success']) {
        _showSuccessSnackBar('تم استعادة البيانات بنجاح');
        await _loadStorageInfo();
        
        // عرض تفاصيل الاستعادة
        _showRestoreDetailsDialog(result);
      } else {
        _showErrorSnackBar(result['message']);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في استعادة البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteFile(String filePath, String fileName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الملف: $fileName؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final deleted = await StorageService.deleteDataFile(filePath);
      
      if (deleted) {
        _showSuccessSnackBar('تم حذف الملف بنجاح');
        await _loadStorageInfo();
      } else {
        _showErrorSnackBar('فشل في حذف الملف');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الملف: $e');
    }
  }

  void _showSaveDetailsDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الحفظ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم حفظ البيانات بنجاح'),
            const SizedBox(height: 8),
            Text('حجم البيانات: ${(result['data_size'] / 1024).toStringAsFixed(2)} KB'),
            const SizedBox(height: 8),
            Text('عدد العملاء: ${result['statistics']['customers_count']}'),
            Text('عدد الفواتير: ${result['statistics']['invoices_count']}'),
            Text('عدد التحصيلات: ${result['statistics']['collections_count']}'),
            Text('عدد المستخدمين: ${result['statistics']['users_count']}'),
            Text('عدد المنتجات: ${result['statistics']['products_count']}'),
            Text('عدد أرقام الهواتف: ${result['statistics']['customer_phones_count']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDetailsDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الاستعادة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم استعادة البيانات بنجاح'),
            const SizedBox(height: 8),
            Text('عدد العملاء المستعادة: ${result['restored_data']['customers_count']}'),
            Text('عدد الفواتير المستعادة: ${result['restored_data']['invoices_count']}'),
            Text('عدد التحصيلات المستعادة: ${result['restored_data']['collections_count']}'),
            Text('عدد المستخدمين المستعادة: ${result['restored_data']['users_count']}'),
            Text('عدد المنتجات المستعادة: ${result['restored_data']['products_count']}'),
            Text('عدد أرقام الهواتف المستعادة: ${result['restored_data']['customer_phones_count']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة التخزين'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStorageInfo,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadStorageInfo,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات المستخدم
                    if (_currentUserName != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              const Icon(Icons.person, color: Color(0xFF4A90E2)),
                              const SizedBox(width: 8),
                              Text(
                                'المستخدم: $_currentUserName',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    
                    const SizedBox(height: 16),
                    
                    // حالة الأذونات
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _storageStats['has_permissions'] == true
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: _storageStats['has_permissions'] == true
                                      ? Colors.green
                                      : Colors.red,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'أذونات التخزين',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _storageStats['has_permissions'] == true
                                  ? 'تم منح أذونات التخزين'
                                  : 'لم يتم منح أذونات التخزين',
                              style: TextStyle(
                                color: _storageStats['has_permissions'] == true
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                            if (_storageStats['has_permissions'] != true) ...[
                              const SizedBox(height: 8),
                              ElevatedButton.icon(
                                onPressed: _requestPermissions,
                                icon: const Icon(Icons.security),
                                label: const Text('طلب الأذونات'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF4A90E2),
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // إحصائيات التخزين
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'إحصائيات التخزين',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildStatItem('عدد ملفات البيانات', '${_storageStats['data_files_count'] ?? 0}'),
                            _buildStatItem('إجمالي الحجم', '${_storageStats['total_size_mb'] ?? '0.00'} MB'),
                            _buildStatItem('آخر حفظ', _storageStats['latest_file'] ?? 'لا يوجد'),
                            if (_storageStats['storage_path'] != null)
                              _buildStatItem('مسار التخزين', _storageStats['storage_path']),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // أزرار العمليات
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'عمليات البيانات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _saveAllData,
                                    icon: const Icon(Icons.save),
                                    label: const Text('حفظ جميع البيانات'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF4A90E2),
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _createAutoBackup,
                                    icon: const Icon(Icons.backup),
                                    label: const Text('نسخة احتياطية'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // قائمة الملفات المحفوظة
                    if (_savedFiles.isNotEmpty) ...[
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'الملفات المحفوظة',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ...(_savedFiles.map((file) => _buildFileItem(file))),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildFileItem(Map<String, dynamic> file) {
    final fileName = file['name'] as String;
    final fileSize = (file['size'] as int) / 1024; // Convert to KB
    final timestamp = file['timestamp'] as String;
    final filePath = file['path'] as String;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.file_copy, color: Color(0xFF4A90E2)),
        title: Text(fileName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحجم: ${fileSize.toStringAsFixed(2)} KB'),
            Text('التاريخ: ${DateTime.parse(timestamp).toString().substring(0, 19)}'),
            if (file['statistics'] != null) ...[
              Text('العملاء: ${file['statistics']['customers_count']}'),
              Text('الفواتير: ${file['statistics']['invoices_count']}'),
            ],
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            if (value == 'restore') {
              _restoreFromFile(filePath);
            } else if (value == 'delete') {
              _deleteFile(filePath, fileName);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'restore',
              child: Row(
                children: [
                  Icon(Icons.restore, color: Color(0xFF4A90E2)),
                  SizedBox(width: 8),
                  Text('استعادة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 