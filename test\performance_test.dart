import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/database/database_helper.dart';
import 'dart:async';

void main() {
  group('اختبار الأداء', () {
    late DatabaseHelper databaseHelper;
    late Stopwatch stopwatch;

    setUpAll(() async {
      databaseHelper = DatabaseHelper();
      await databaseHelper.database;
    });

    setUp(() {
      stopwatch = Stopwatch();
    });

    test('اختبار سرعة قاعدة البيانات', () async {
      stopwatch.start();
      final db = await databaseHelper.database;
      stopwatch.stop();

      print('وقت فتح قاعدة البيانات: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });

    test('اختبار سرعة استعلام العملاء', () async {
      stopwatch.start();
      final customers = await databaseHelper.getCustomers();
      stopwatch.stop();

      print('وقت استعلام العملاء: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
    });

    test('اختبار سرعة استعلام الفواتير', () async {
      stopwatch.start();
      final invoices = await databaseHelper.getInvoices();
      stopwatch.stop();

      print('وقت استعلام الفواتير: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
    });
  });
}
