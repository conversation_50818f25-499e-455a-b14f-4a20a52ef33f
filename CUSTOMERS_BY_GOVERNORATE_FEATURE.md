# ميزة العملاء حسب المحافظة

## نظرة عامة
تم إضافة ميزة جديدة تسمح بعرض العملاء مقسمين حسب المحافظات بطريقة أكثر تنظيماً. عند الضغط على أي محافظة في شاشة "الفواتير حسب المحافظات"، يتم الانتقال إلى شاشة مخصصة لعرض جميع العملاء في تلك المحافظة.

## الميزات الجديدة

### 1. شاشة العملاء حسب المحافظة (`CustomersByGovernorateScreen`)
- **الوصف**: شاشة مخصصة لعرض جميع العملاء في محافظة محددة
- **الميزات**:
  - عرض معلومات المحافظة وعدد العملاء
  - شريط بحث للبحث في العملاء
  - قائمة العملاء مع معلوماتهم الكاملة
  - إمكانية إنشاء فاتورة جديدة للعميل
  - إمكانية عرض فواتير العميل
  - إمكانية تعديل بيانات العميل
  - إضافة عميل جديد للمحافظة

### 2. تحديث شاشة فواتير المحافظات
- **التغيير**: تم استبدال `ExpansionTile` بـ `InkWell` مع `Card`
- **النتيجة**: عند الضغط على المحافظة، يتم الانتقال إلى شاشة العملاء المخصصة
- **المظهر**: إضافة سهم للإشارة إلى إمكانية الانتقال

## كيفية الاستخدام

### الوصول إلى الميزة
1. من الشاشة الرئيسية: اضغط على "فواتير المحافظات"
2. من شاشة العملاء: اضغط على أيقونة الفواتير في الشريط العلوي
3. من شاشة الفواتير: اضغط على أيقونة المحافظات في الشريط العلوي

### إنشاء فاتورة جديدة
1. انتقل إلى شاشة "الفواتير حسب المحافظات"
2. اضغط على المحافظة المطلوبة
3. في شاشة العملاء، اضغط على القائمة المنسدلة لأي عميل
4. اختر "إنشاء فاتورة"
5. سيتم فتح شاشة إنشاء الفاتورة مع تحديد العميل تلقائياً

### إدارة العملاء
- **إضافة عميل جديد**: اضغط على زر + في شاشة العملاء حسب المحافظة
- **تعديل العميل**: اضغط على القائمة المنسدلة واختر "تعديل العميل"
- **عرض فواتير العميل**: اضغط على القائمة المنسدلة واختر "عرض الفواتير"

## الملفات المضافة/المعدلة

### ملفات جديدة
- `lib/screens/customers_by_governorate_screen.dart`
- `CUSTOMERS_BY_GOVERNORATE_FEATURE.md`

### ملفات معدلة
- `lib/screens/governorates_invoices_screen.dart`
- `lib/main.dart`

## الفوائد

### 1. تنظيم أفضل
- فصل واضح بين المحافظات
- عرض مخصص لكل محافظة
- سهولة في التنقل والبحث

### 2. تحسين تجربة المستخدم
- واجهة أكثر وضوحاً
- انتقال سلس بين الشاشات
- إمكانية البحث في العملاء

### 3. كفاءة في العمل
- إنشاء فواتير أسرع
- إدارة أفضل للعملاء
- تنظيم شامل حسب المحافظات

## التطوير المستقبلي

### ميزات مقترحة
1. **إحصائيات المحافظة**:
   - إجمالي الفواتير في المحافظة
   - إجمالي المبالغ المستحقة
   - عدد العملاء النشطين

2. **تصفية متقدمة**:
   - تصفية حسب المنطقة
   - تصفية حسب نوع العميل
   - تصفية حسب حالة الدفع

3. **تقارير المحافظة**:
   - تقرير شهري للمحافظة
   - تقرير المبيعات حسب المحافظة
   - تقرير العملاء الجدد

4. **ميزات إضافية**:
   - إرسال رسائل جماعية لعملاء المحافظة
   - تصدير بيانات المحافظة
   - خريطة تفاعلية للمحافظات

## ملاحظات تقنية

### الأداء
- تحميل البيانات بشكل غير متزامن
- تحديث تلقائي للبيانات عند العودة للشاشة
- معالجة الأخطاء وعرض رسائل مناسبة

### التوافق
- متوافق مع جميع أحجام الشاشات
- دعم الاتجاه من اليمين لليسار
- تصميم متجاوب

### الأمان
- التحقق من صحة البيانات
- معالجة الحالات الاستثنائية
- حماية من الأخطاء البرمجية 