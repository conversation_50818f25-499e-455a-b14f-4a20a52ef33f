# نظام الرسائل النصية - Atlas Medical Supplies

## نظرة عامة
تم تحديث نظام الرسائل النصية في التطبيق ليعمل عبر تطبيق الهاتف الافتراضي بدلاً من استخدام أذونات SMS المباشرة.

## الميزات الرئيسية

### 1. إرسال الرسائل عبر تطبيق الهاتف
- **لا حاجة لأذونات خاصة**: التطبيق لا يحتاج أذونات SMS
- **توافق أفضل**: يعمل مع جميع أنواع الهواتف
- **سهولة الاستخدام**: يفتح تطبيق الرسائل تلقائياً

### 2. تنظيف أرقام الهواتف
- **إلغاء تفعيل كود الدولة**: لا يتم تعديل الرقم تلقائياً
- **الاحتفاظ بالرقم كما هو**: الرقم يبقى كما هو مسجل في قاعدة البيانات
- **إزالة الرموز فقط**: يتم إزالة الرموز والمسافات فقط

### 3. أنواع الرسائل المدعومة
- **رسائل الفواتير**: تفاصيل كاملة للفاتورة
- **رسائل التحصيل**: تنبيهات التحصيل
- **رسائل الاختبار**: لاختبار النظام
- **رسائل مخصصة**: رسائل مخصصة حسب الحاجة

## كيفية الاستخدام

### 1. إرسال رسالة فاتورة
```dart
await MessagingService.sendInvoiceViaSMS(
  customerPhone: '01123456789',
  customerName: 'اسم العميل',
  invoiceNumber: 'INV-001',
  totalAmount: 1000.0,
  paidAmount: 500.0,
  remainingAmount: 500.0,
  invoiceDate: '2024-01-01',
  invoiceItems: [],
);
```

### 2. إرسال رسالة تحصيل
```dart
await MessagingService.sendCollectionAlertViaSMS(
  customerPhone: '01123456789',
  customerName: 'اسم العميل',
  amount: 500.0,
  collectionDate: '2024-01-01',
  collectorName: 'اسم المحصل',
  notes: 'ملاحظات إضافية',
);
```

### 3. إرسال رسالة اختبار
```dart
await MessagingService.testSMS('01123456789');
```

### 4. إرسال رسالة مخصصة
```dart
await MessagingService.sendCustomSMS(
  phone: '01123456789',
  message: 'رسالة مخصصة',
);
```

## تنظيف أرقام الهواتف

### قبل التحديث (مشكلة)
```dart
// كان الكود يضيف رمز البلد المصري (2) تلقائياً
رقم أصلي: 01123456789
رقم منسق: 201123456789 (خطأ)
```

### بعد التحديث (حل)
```dart
// الآن الرقم يبقى كما هو مسجل
رقم أصلي: 01123456789
رقم منسق: 01123456789 (صحيح)
```

## دالة تنظيف الرقم المحدثة

```dart
static String _cleanPhoneNumber(String phone) {
  // إزالة جميع الرموز والمسافات فقط (بدون تعديل الرقم)
  String clean = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  print('🔍 تنظيف رقم الهاتف: "$phone" -> "$clean"');

  // إذا كان الرقم فارغاً، إرجاع رقم افتراضي
  if (clean.isEmpty) {
    print('⚠️ رقم الهاتف فارغ، استخدام رقم افتراضي');
    return '201125312343'; // رقم افتراضي للاختبار
  }

  // إلغاء تفعيل كود الدولة - إرجاع الرقم كما هو بعد إزالة الرموز فقط
  print('✅ الرقم النهائي (بدون تعديل): "$clean"');
  return clean;
}
```

## معلومات الرقم

### عرض معلومات الرقم
```dart
String info = MessagingService.getFormattedPhoneInfo('01123456789');
print(info);
```

### النتيجة
```
معلومات الرقم:
الرقم الأصلي: 01123456789
الرقم المنسق: 01123456789
ملاحظة: تم إلغاء تفعيل كود الدولة - الرقم كما هو مسجل
```

## تنسيق الرسائل

### رسالة الفاتورة
```
أطلس للمستلزمات الطبية
تفاصيل الفاتورة

العميل: اسم العميل
رقم الفاتورة: INV-001
التاريخ: 2024-01-01

الإجماليات:
المبلغ الإجمالي: 1,000.00 ج.م
المبلغ المدفوع: 500.00 ج.م
المبلغ المتبقي: 500.00 ج.م

شكراً لتعاملكم معنا
للاستفسار: 01125312343
```

### رسالة التحصيل
```
أطلس للمستلزمات الطبية
تنبيه تحصيل

العميل: اسم العميل
المبلغ المحصل: 500.00 ج.م
تاريخ التحصيل: 2024-01-01
المحصل: اسم المحصل
ملاحظات: ملاحظات إضافية

شكراً لتعاملكم معنا
للاستفسار: 01125312343
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا يفتح تطبيق الرسائل
- **السبب**: عدم وجود تطبيق رسائل على الجهاز
- **الحل**: تثبيت تطبيق رسائل أو استخدام تطبيق بديل

#### 2. الرقم غير صحيح
- **السبب**: تم تعديل الرقم تلقائياً (مشكلة قديمة)
- **الحل**: تم إصلاحها - الرقم يبقى كما هو مسجل

#### 3. رسالة خطأ في الإرسال
- **السبب**: مشكلة في تنسيق الرقم
- **الحل**: استخدام زر "معلومات الرقم" للتحقق

### أدوات التصحيح

#### 1. زر معلومات الرقم
- موجود في شاشة تفاصيل الفاتورة
- يعرض الرقم الأصلي والمنسق
- يساعد في اكتشاف المشاكل

#### 2. رسائل التصحيح
- تظهر في وحدة التحكم (Console)
- تتبع عملية تنظيف الرقم خطوة بخطوة
- تساعد في فهم ما يحدث

#### 3. زر الاختبار
- يرسل رسالة اختبار بسيطة
- يساعد في التأكد من عمل النظام
- لا يحتاج لبيانات فاتورة

## الملفات المتعلقة

1. `lib/services/messaging_service.dart` - الخدمة الرئيسية
2. `lib/screens/invoice_details_screen.dart` - واجهة الإرسال
3. `PHONE_NUMBER_CLEANING_UPDATE.md` - تفاصيل التحديث

## الإصدارات

- **الإصدار 1.0.9**: إلغاء تفعيل كود الدولة
- **الإصدار 1.0.8**: إضافة دعم تطبيق الهاتف
- **الإصدارات السابقة**: نظام SMS مباشر (تم إزالته) 