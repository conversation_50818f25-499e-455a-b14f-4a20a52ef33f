# 🔄 ميزة النسخ الاحتياطي المحلي

## 📋 نظرة عامة

تم إلغاء التواصل مع Firebase وتحويل التطبيق إلى قاعدة بيانات محلية مع نظام نسخ احتياطي تلقائي كل ثانية. يتم حفظ النسخ الاحتياطية محلياً مع إمكانية تصديرها واستيرادها.

## ✅ الميزات المضافة

### 1. **النسخ الاحتياطي التلقائي**
- ✅ نسخ احتياطي كل ثانية
- ✅ حفظ محلي في مجلد `backups`
- ✅ الاحتفاظ بآخر 10 نسخ احتياطية
- ✅ تنظيف تلقائي للنسخ القديمة

### 2. **إدارة النسخ الاحتياطي**
- ✅ عرض قائمة النسخ الاحتياطية
- ✅ إحصائيات مفصلة
- ✅ استعادة النسخ الاحتياطية
- ✅ حذف النسخ الاحتياطية
- ✅ إنشاء نسخة احتياطية يدوية

### 3. **تصدير واستيراد**
- ✅ تصدير النسخ الاحتياطية إلى ملفات خارجية
- ✅ استيراد النسخ الاحتياطية من ملفات خارجية
- ✅ دعم تنسيق JSON

## 🔧 التفاصيل التقنية

### 1. **خدمة النسخ الاحتياطي المحلي**
```dart
class LocalBackupService {
  static Timer? _backupTimer;
  static bool _isInitialized = false;

  // تهيئة خدمة النسخ الاحتياطي
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // بدء النسخ الاحتياطي التلقائي كل ثانية
      _startAutoBackup();
      _isInitialized = true;
      
      print('✅ تم تهيئة خدمة النسخ الاحتياطي المحلي بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة خدمة النسخ الاحتياطي: $e');
    }
  }
}
```

### 2. **إنشاء النسخ الاحتياطية**
```dart
// إنشاء نسخة احتياطية
static Future<void> _createBackup() async {
  try {
    final dbHelper = DatabaseHelper();
    
    // جمع البيانات من قاعدة البيانات المحلية
    final backupData = await _collectBackupData(dbHelper);
    
    // حفظ النسخة الاحتياطية محلياً
    await _saveLocalBackup(backupData);
    
    print('✅ تم إنشاء النسخة الاحتياطية بنجاح');
  } catch (e) {
    print('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
  }
}
```

### 3. **جمع البيانات**
```dart
// جمع بيانات النسخة الاحتياطية
static Future<Map<String, dynamic>> _collectBackupData(DatabaseHelper dbHelper) async {
  final customers = await dbHelper.getCustomers();
  final invoices = await dbHelper.getInvoices();
  final collections = await dbHelper.getCollections();
  final users = await dbHelper.getUsers();

  return {
    'timestamp': DateTime.now().toIso8601String(),
    'version': '1.0',
    'data': {
      'customers': customers,
      'invoices': invoices,
      'collections': collections,
      'users': users,
    },
    'statistics': {
      'customers_count': customers.length,
      'invoices_count': invoices.length,
      'collections_count': collections.length,
      'users_count': users.length,
    },
  };
}
```

### 4. **حفظ النسخ الاحتياطية**
```dart
// حفظ النسخة الاحتياطية محلياً
static Future<String> _saveLocalBackup(Map<String, dynamic> backupData) async {
  final directory = await getApplicationDocumentsDirectory();
  final backupDir = Directory(path.join(directory.path, 'backups'));
  
  if (!await backupDir.exists()) {
    await backupDir.create(recursive: true);
  }

  final timestamp = DateTime.now().millisecondsSinceEpoch;
  final backupFileName = 'atlas_backup_$timestamp.json';
  final backupPath = path.join(backupDir.path, backupFileName);

  final backupFile = File(backupPath);
  await backupFile.writeAsString(json.encode(backupData));

  // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
  await _cleanOldBackups(backupDir);

  return backupPath;
}
```

## 📱 واجهة المستخدم

### 1. **شاشة إدارة النسخ الاحتياطي**
- عرض إحصائيات النسخ الاحتياطي
- قائمة النسخ الاحتياطية المتاحة
- إمكانية الاستعادة والحذف
- إنشاء نسخة احتياطية يدوية

### 2. **إحصائيات النسخ الاحتياطي**
- إجمالي عدد النسخ الاحتياطية
- الحجم الإجمالي للنسخ
- تاريخ آخر نسخة احتياطية
- حالة النسخ التلقائي

### 3. **قائمة النسخ الاحتياطية**
- اسم الملف
- تاريخ الإنشاء
- حجم الملف
- إحصائيات البيانات
- خيارات الاستعادة والحذف

## 🎯 سيناريوهات الاستخدام

### 1. **النسخ الاحتياطي التلقائي**
1. التطبيق يعمل في الخلفية
2. يتم إنشاء نسخة احتياطية كل ثانية
3. يتم حفظ النسخة في مجلد `backups`
4. يتم تنظيف النسخ القديمة تلقائياً

### 2. **إنشاء نسخة احتياطية يدوية**
1. المستخدم يفتح شاشة النسخ الاحتياطي
2. يضغط على زر "+"
3. يتم إنشاء نسخة احتياطية فورية
4. تظهر رسالة نجاح

### 3. **استعادة نسخة احتياطية**
1. المستخدم يفتح شاشة النسخ الاحتياطي
2. يختار نسخة احتياطية من القائمة
3. يضغط على "استعادة"
4. يؤكد العملية
5. يتم استبدال البيانات الحالية

### 4. **حذف نسخة احتياطية**
1. المستخدم يفتح شاشة النسخ الاحتياطي
2. يختار نسخة احتياطية من القائمة
3. يضغط على "حذف"
4. يؤكد العملية
5. يتم حذف الملف

## 🔍 الاختبار

### 1. **اختبار النسخ الاحتياطي التلقائي**
```dart
// اختبار تهيئة خدمة النسخ الاحتياطي
await LocalBackupService.initialize();

// انتظار إنشاء نسخة احتياطية
await Future.delayed(const Duration(seconds: 2));

// التحقق من وجود النسخ الاحتياطية
final backups = await LocalBackupService.getLocalBackups();
assert(backups.isNotEmpty);
```

### 2. **اختبار إنشاء نسخة احتياطية يدوية**
```dart
// إنشاء نسخة احتياطية يدوية
final result = await LocalBackupService.createManualBackup();
assert(result['success'] == true);
assert(result['local_path'] != null);
```

### 3. **اختبار استعادة نسخة احتياطية**
```dart
// الحصول على قائمة النسخ الاحتياطية
final backups = await LocalBackupService.getLocalBackups();
if (backups.isNotEmpty) {
  final backupPath = backups.first['path'];
  
  // استعادة النسخة الاحتياطية
  final result = await LocalBackupService.restoreFromBackup(backupPath);
  assert(result['success'] == true);
}
```

## 🚨 ملاحظات مهمة

### 1. **الأمان**
- النسخ الاحتياطية محفوظة محلياً فقط
- لا توجد مزامنة مع السحابة
- يجب تصدير النسخ الاحتياطية يدوياً للنسخ الاحتياطية الخارجية

### 2. **الأداء**
- النسخ الاحتياطي كل ثانية قد يؤثر على الأداء
- يتم الاحتفاظ بآخر 10 نسخ فقط لتوفير المساحة
- يمكن تعديل الفاصل الزمني حسب الحاجة

### 3. **التوافق**
- تم إلغاء جميع تبعيات Firebase
- التطبيق يعمل بشكل مستقل
- قاعدة البيانات محلية باستخدام SQLite

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | إلغاء Firebase | `main.dart`, `pubspec.yaml` |
| 2024-01-15 | إنشاء خدمة النسخ الاحتياطي المحلي | `local_backup_service.dart` |
| 2024-01-15 | إنشاء شاشة إدارة النسخ الاحتياطي | `backup_screen.dart` |
| 2024-01-15 | إضافة المستخدم الافتراضي | `auth_service.dart` |
| 2024-01-15 | إضافة زر النسخ الاحتياطي للوحة التحكم | `dashboard_screen.dart` |

## 🎉 النتيجة النهائية

تم تحويل التطبيق بنجاح إلى قاعدة بيانات محلية مع نظام نسخ احتياطي متقدم! 🚀

### الميزات المتاحة:
- ✅ قاعدة بيانات محلية مستقلة
- ✅ نسخ احتياطي تلقائي كل ثانية
- ✅ إدارة شاملة للنسخ الاحتياطية
- ✅ استعادة وحذف النسخ الاحتياطية
- ✅ تصدير واستيراد النسخ الاحتياطية
- ✅ واجهة مستخدم جذابة وسهلة الاستخدام
- ✅ إحصائيات مفصلة
- ✅ تنظيف تلقائي للنسخ القديمة

### بيانات تسجيل الدخول الافتراضية:
- **رقم الهاتف:** `admin`
- **كلمة المرور:** `123456`
- **الدور:** `مدير النظام`

الآن يمكن استخدام التطبيق بدون الحاجة لاتصال بالإنترنت مع حماية كاملة للبيانات! 😊 