import 'package:flutter/material.dart';

class AtlasAppIcon extends StatelessWidget {
  final double size;

  const AtlasAppIcon({super.key, this.size = 1024});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4A90E2), Color(0xFF20B2AA), Color(0xFF008B8B)],
        ),
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خلفية الشعار
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(size * 0.2),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.1),
                  ],
                ),
              ),
            ),
          ),
          // كلمة ATLAS
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'ATLAS',
                  style: TextStyle(
                    fontSize: size * 0.4,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                    letterSpacing: size * 0.04,
                    shadows: [
                      Shadow(
                        offset: Offset(size * 0.02, size * 0.02),
                        blurRadius: size * 0.04,
                        color: Colors.black26,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: size * 0.03),
                Container(
                  width: size * 0.3,
                  height: size * 0.02,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(size * 0.01),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: size * 0.02,
                        offset: Offset(0, size * 0.01),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // تم حذف أيقونة الطب الصغيرة
        ],
      ),
    );
  }
}
