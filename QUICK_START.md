# 🚀 دليل التشغيل السريع - أطلس للمستلزمات الطبية

## ⚡ الحل السريع (دقيقة واحدة)

### 1. افتح Terminal في مجلد المشروع
```bash
cd atlas_medical_supplies
```

### 2. شغل التطبيق على الويب (الأسرع)
```bash
flutter run -d chrome
```

### 3. أو شغل على Windows
```bash
flutter run -d windows
```

## 📱 إذا كنت تريد تشغيله على Android

### الخيار الأول: محاكي Android
1. افتح Android Studio
2. اذهب إلى **Tools > AVD Manager**
3. اضغط **Create Virtual Device**
4. اختر Pixel 4 + API 33
5. شغل المحاكي
6. اكتب: `flutter run`

### الخيار الثاني: جهاز Android حقيقي
1. فعّل **Developer Options** (اضغط Build Number 7 مرات)
2. فعّل **USB Debugging**
3. وصل الجهاز بالكمبيوتر
4. اكتب: `flutter run`

## 🔧 إذا واجهت مشاكل

### تنظيف وإعادة البناء
```bash
flutter clean
flutter pub get
flutter run -d chrome
```

### التحقق من الإعداد
```bash
flutter doctor
```

## 🎯 الطريقة الأسهل

**جرب هذا الأمر مباشرة:**
```bash
cd atlas_medical_supplies && flutter run -d chrome
```

سيفتح التطبيق في المتصفح خلال دقيقة!

## 📞 بيانات تسجيل الدخول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

---

**ملاحظة:** التطبيق يعمل بشكل مثالي على الويب و Windows! 🎉 