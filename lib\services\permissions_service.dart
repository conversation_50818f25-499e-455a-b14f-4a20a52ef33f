import 'package:shared_preferences/shared_preferences.dart';

class PermissionsService {
  static const String _keyUserPermissions = 'user_permissions';

  // تعريف الصلاحيات المتاحة
  static const Map<String, String> availablePermissions = {
    // إدارة العملاء
    'customers_view': 'عرض العملاء',
    'customers_add': 'إضافة عملاء',
    'customers_edit': 'تعديل العملاء',
    'customers_delete': 'حذف العملاء',

    // إدارة الفواتير
    'invoices_view': 'عرض الفواتير',
    'invoices_add': 'إضافة فواتير',
    'invoices_edit': 'تعديل الفواتير',
    'invoices_delete': 'حذف الفواتير',
    'invoices_send': 'إرسال الفواتير',

    // إدارة التحصيل
    'collections_view': 'عرض التحصيل',
    'collections_add': 'إضافة تحصيل',
    'collections_edit': 'تعديل التحصيل',
    'collections_delete': 'حذف التحصيل',

    // إدارة المستخدمين (للمدير فقط)
    'users_manage': 'إدارة المستخدمين',
    'permissions_manage': 'إدارة الصلاحيات',

    // الإعدادات
    'settings_access': 'الوصول للإعدادات',
    'reports_view': 'عرض التقارير',
    'dashboard_view': 'عرض لوحة التحكم',
  };

  // الصلاحيات الافتراضية للمدير
  static const List<String> adminPermissions = [
    'customers_view',
    'customers_add',
    'customers_edit',
    'customers_delete',
    'invoices_view',
    'invoices_add',
    'invoices_edit',
    'invoices_delete',
    'invoices_send',
    'collections_view',
    'collections_add',
    'collections_edit',
    'collections_delete',
    'users_manage',
    'permissions_manage',
    'settings_access',
    'reports_view',
    'dashboard_view',
  ];

  // الصلاحيات الافتراضية للمستخدم العادي
  static const List<String> userPermissions = [
    'customers_view',
    'customers_add',
    'invoices_view',
    'invoices_add',
    'invoices_send',
    'collections_view',
    'collections_add',
    'dashboard_view',
  ];

  // حفظ صلاحيات المستخدم
  static Future<void> saveUserPermissions(
    int userId,
    List<String> permissions,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final permissionsString = permissions.join(',');
    await prefs.setString('${_keyUserPermissions}_$userId', permissionsString);
  }

  // جلب صلاحيات المستخدم
  static Future<List<String>> getUserPermissions(int userId) async {
    final prefs = await SharedPreferences.getInstance();
    final permissionsString = prefs.getString('${_keyUserPermissions}_$userId');

    if (permissionsString != null && permissionsString.isNotEmpty) {
      return permissionsString.split(',');
    }

    // إرجاع الصلاحيات الافتراضية حسب الدور
    return userPermissions;
  }

  // التحقق من وجود صلاحية معينة
  static Future<bool> hasPermission(int userId, String permission) async {
    final permissions = await getUserPermissions(userId);
    return permissions.contains(permission);
  }

  // التحقق من وجود أي من الصلاحيات المطلوبة
  static Future<bool> hasAnyPermission(
    int userId,
    List<String> permissions,
  ) async {
    final userPermissions = await getUserPermissions(userId);
    return permissions.any(
      (permission) => userPermissions.contains(permission),
    );
  }

  // التحقق من وجود جميع الصلاحيات المطلوبة
  static Future<bool> hasAllPermissions(
    int userId,
    List<String> permissions,
  ) async {
    final userPermissions = await getUserPermissions(userId);
    return permissions.every(
      (permission) => userPermissions.contains(permission),
    );
  }

  // حذف صلاحيات المستخدم
  static Future<void> deleteUserPermissions(int userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('${_keyUserPermissions}_$userId');
  }

  // جلب جميع الصلاحيات المتاحة
  static Map<String, String> getAllAvailablePermissions() {
    return availablePermissions;
  }

  // إنشاء صلاحيات افتراضية حسب الدور
  static List<String> getDefaultPermissionsByRole(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
      case 'مدير':
        return adminPermissions;
      case 'user':
      case 'مستخدم':
        return userPermissions;
      case 'sales':
      case 'مبيعات':
        return [
          'customers_view',
          'customers_add',
          'invoices_view',
          'invoices_add',
          'invoices_send',
          'collections_view',
          'collections_add',
          'dashboard_view',
        ];
      case 'accountant':
      case 'محاسب':
        return [
          'customers_view',
          'invoices_view',
          'invoices_edit',
          'collections_view',
          'collections_add',
          'collections_edit',
          'reports_view',
          'dashboard_view',
        ];
      case 'seller':
      case 'بائع':
        return [
          'customers_view',
          'customers_add',
          'invoices_view',
          'invoices_add',
          'invoices_send',
          'collections_view',
          'collections_add',
          'dashboard_view',
        ];
      default:
        return userPermissions;
    }
  }

  // تحديث صلاحيات المستخدم حسب الدور
  static Future<void> updatePermissionsByRole(int userId, String role) async {
    final defaultPermissions = getDefaultPermissionsByRole(role);
    await saveUserPermissions(userId, defaultPermissions);
  }
}
