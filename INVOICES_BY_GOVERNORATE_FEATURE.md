# ميزة تنظيم الفواتير حسب المحافظات

## نظرة عامة

تم تحديث قائمة الفواتير لتعرض الفواتير منظمة حسب المحافظات، تماماً مثل قائمة العملاء. هذه الميزة تتيح للمستخدمين تصفح الفواتير بطريقة أكثر تنظيماً وسهولة في الوصول.

## الميزات المضافة

### 1. الشاشة الرئيسية للفواتير
- **عرض المحافظات**: تعرض قائمة بجميع المحافظات التي تحتوي على فواتير
- **عدد الفواتير**: يظهر عدد الفواتير لكل محافظة
- **التصميم المتناسق**: نفس تصميم قائمة العملاء حسب المحافظات

### 2. شاشة فواتير المحافظة
- **عرض الفواتير**: تعرض جميع فواتير المحافظة المحددة
- **البحث**: إمكانية البحث في فواتير المحافظة
- **جميع الوظائف**: تعديل، حذف، إضافة تحصيل، إرسال رسائل

## الملفات المحدثة

### 1. `lib/database/database_helper.dart`
تم إضافة الوظائف التالية:

#### `getInvoicesByGovernorate(String governorate)`
```dart
Future<List<Map<String, dynamic>>> getInvoicesByGovernorate(String governorate)
```
- تجلب جميع الفواتير لمحافظة محددة
- تتضمن بيانات العميل والمستخدم المنشئ

#### `getInvoiceCountByGovernorate()`
```dart
Future<Map<String, int>> getInvoiceCountByGovernorate()
```
- تجلب عدد الفواتير لكل محافظة
- ترتب النتائج حسب عدد الفواتير (تنازلياً)

#### `getGovernoratesWithInvoices()`
```dart
Future<List<String>> getGovernoratesWithInvoices()
```
- تجلب قائمة المحافظات التي تحتوي على فواتير
- ترتب النتائج أبجدياً

### 2. `lib/screens/invoices_screen.dart`
تم إعادة هيكلة الملف بالكامل:

#### الشاشة الرئيسية (`InvoicesScreen`)
- تعرض قائمة المحافظات مع عدد الفواتير
- تصميم متناسق مع قائمة العملاء
- زر إضافة فاتورة جديدة

#### شاشة فواتير المحافظة (`InvoicesByGovernorateScreen`)
- تعرض فواتير محافظة محددة
- شريط بحث للفواتير
- جميع وظائف إدارة الفواتير

## مثال على الاستخدام

### الشاشة الرئيسية
```
الفواتير حسب المحافظات
├── الغربية (15 فاتورة)
├── القاهرة (12 فاتورة)
├── الإسكندرية (8 فواتير)
├── الجيزة (6 فواتير)
└── المنوفية (3 فواتير)
```

### شاشة محافظة محددة
```
فواتير الغربية
├── فاتورة رقم INV-2024-001
│   ├── التاريخ: 15/01/2024
│   ├── العميل: أحمد محمد
│   ├── المبلغ: 1,500.00 ج.م
│   └── المدفوع: 500.00 ج.م
├── فاتورة رقم INV-2024-002
│   ├── التاريخ: 14/01/2024
│   ├── العميل: فاطمة علي
│   ├── المبلغ: 2,300.00 ج.م
│   └── المدفوع: 2,300.00 ج.م
└── ...
```

## الميزات التقنية

### 1. الأداء المحسن
- تحميل الفواتير حسب المحافظة فقط
- تقليل حجم البيانات المحملة
- تحسين سرعة الاستجابة

### 2. التنظيم المحسن
- تجميع منطقي للفواتير
- سهولة الوصول للفواتير
- واجهة مستخدم أكثر وضوحاً

### 3. المرونة
- دعم البحث في فواتير المحافظة
- إمكانية إضافة فواتير جديدة
- جميع وظائف إدارة الفواتير متاحة

## التحديثات المستقبلية

### 1. إحصائيات متقدمة
- إجمالي المبيعات لكل محافظة
- متوسط قيمة الفاتورة
- نسبة التحصيل لكل محافظة

### 2. تصفية إضافية
- تصفية حسب التاريخ
- تصفية حسب حالة الدفع
- تصفية حسب المستخدم المنشئ

### 3. تقارير محسنة
- تقارير مبيعات المحافظات
- مقارنة الأداء بين المحافظات
- تحليل اتجاهات المبيعات

## الاستخدام

### للمستخدمين الجدد
1. انتقل إلى قائمة الفواتير
2. اختر المحافظة المطلوبة
3. استعرض فواتير المحافظة
4. استخدم وظائف إدارة الفواتير

### للمستخدمين الحاليين
- نفس الوظائف السابقة متاحة
- تنظيم محسن للفواتير
- سهولة أكبر في الوصول للفواتير

## الفوائد

### 1. للمستخدمين
- تنظيم أفضل للفواتير
- سهولة الوصول للفواتير
- واجهة مستخدم محسنة

### 2. للإدارة
- رؤية واضحة لتوزيع الفواتير
- تحليل أداء المحافظات
- إدارة أفضل للمبيعات

### 3. للتطبيق
- أداء محسن
- تنظيم أفضل للبيانات
- قابلية للتوسع المستقبلي 