# دليل إعداد Firebase Firestore للتطبيق

## 📋 المتطلبات المسبقة
- حس<PERSON><PERSON> Google
- Flutter SDK مثبت
- Android Studio أو VS Code

## 🚀 الخطوات التفصيلية

### 1. إنشاء مشروع Firebase

1. **اذهب إلى Firebase Console**
   - افتح [https://console.firebase.google.com/](https://console.firebase.google.com/)
   - سجل دخول بحساب Google

2. **إنشاء مشروع جديد**
   - انقر على "إنشاء مشروع"
   - أدخل اسم المشروع: `Atlas Medical Supplies`
   - اختر "لا" لـ Google Analytics (اختياري)
   - انقر "إنشاء المشروع"

### 2. إعداد تطبيق Android

1. **إضافة تطبيق Android**
   - في لوحة التحكم، انقر على أيقونة Android
   - أدخل package name: `com.example.atlas_medical_supplies`
   - انقر "تسجيل التطبيق"

2. **تحميل ملف التكوين**
   - حمل ملف `google-services.json`
   - ضعه في مجلد `android/app/` في مشروع Flutter

### 3. إعداد تطبيق Web

1. **إضافة تطبيق Web**
   - انقر على أيقونة Web
   - أدخل اسم التطبيق: `Atlas Medical Supplies`
   - انقر "تسجيل التطبيق"

2. **نسخ إعدادات Firebase**
   - انسخ الإعدادات المعروضة

### 4. تفعيل Firestore Database

1. **إنشاء قاعدة البيانات**
   - في القائمة الجانبية، اختر "Firestore Database"
   - انقر "إنشاء قاعدة بيانات"
   - اختر "بدء في وضع الاختبار"
   - اختر موقع قاعدة البيانات (مثلاً: `europe-west1`)

2. **إعداد قواعد الأمان**
   - انقر على "قواعد" في أعلى الصفحة
   - استبدل القواعد الحالية بـ:
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // السماح بالقراءة والكتابة للمستخدمين المسجلين
       match /{document=**} {
         allow read, write: if request.auth != null;
       }
     }
   }
   ```
   - انقر "نشر"

### 5. تفعيل Authentication

1. **إعداد المصادقة**
   - اختر "Authentication" من القائمة الجانبية
   - انقر "Get started"
   - فعّل "Email/Password"
   - فعّل "Phone" (اختياري)

2. **إضافة مستخدم تجريبي**
   - انقر على "Users" في أعلى الصفحة
   - انقر "Add user"
   - أدخل:
     - Email: `<EMAIL>`
     - Password: `123456`

### 6. تحديث إعدادات Firebase في التطبيق

1. **تحديث firebase_options.dart**
   - افتح ملف `lib/firebase_options.dart`
   - استبدل جميع القيم بـ الإعدادات الحقيقية من Firebase Console

2. **مثال للإعدادات:**
   ```dart
   static const FirebaseOptions web = FirebaseOptions(
     apiKey: 'AIzaSyC...', // من Firebase Console
     appId: '1:123456789:web:abc123', // من Firebase Console
     messagingSenderId: '123456789', // من Firebase Console
     projectId: 'atlas-medical-supplies', // من Firebase Console
     authDomain: 'atlas-medical-supplies.firebaseapp.com',
     storageBucket: 'atlas-medical-supplies.appspot.com',
   );
   ```

### 7. تثبيت التبعيات

```bash
cd atlas_medical_supplies
flutter pub get
```

### 8. اختبار التطبيق

```bash
flutter run
```

## 🔧 استكشاف الأخطاء

### مشكلة: "Firebase not initialized"
- تأكد من تحديث `firebase_options.dart` بالإعدادات الصحيحة
- تأكد من وجود `google-services.json` في `android/app/`

### مشكلة: "Permission denied"
- تأكد من إعداد قواعد الأمان في Firestore
- تأكد من تفعيل Authentication

### مشكلة: "User not found"
- تأكد من إضافة المستخدم في Firebase Authentication
- تأكد من استخدام نفس email/password

## 📱 المميزات المتاحة بعد الإعداد

✅ **مزامنة فورية** للبيانات بين الأجهزة
✅ **نسخ احتياطي تلقائي** في السحابة
✅ **مصادقة آمنة** للمستخدمين
✅ **قواعد أمان** قابلة للتخصيص
✅ **أداء عالي** وقابلية التوسع

## 🆘 الدعم

إذا واجهت أي مشاكل:
1. تحقق من إعدادات Firebase Console
2. تأكد من تحديث جميع الملفات
3. جرب `flutter clean` ثم `flutter pub get`
4. تحقق من سجلات الأخطاء في Firebase Console 