import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🚀 بدء إضافة عميل شركة النيل...');
  
  try {
    // فتح قاعدة البيانات
    String path = join(await getDatabasesPath(), 'atlas_medical.db');
    Database db = await openDatabase(path);
    
    // بيانات العميل الجديد
    final customerData = {
      'name': 'شركة النيل',
      'phone': '01110473536',
      'address': 'أبو حمص',
      'governorate': 'البحيرة',
      'area': 'أبو حمص',
      'notes': 'تم الإضافة بتاريخ ${DateTime.now().toString().split(' ')[0]}',
    };
    
    print('📋 بيانات العميل:');
    print('   الاسم: ${customerData['name']}');
    print('   الهاتف: ${customerData['phone']}');
    print('   المحافظة: ${customerData['governorate']}');
    print('   المنطقة: ${customerData['area']}');
    print('   العنوان: ${customerData['address']}');
    
    // التحقق من عدم وجود العميل مسبقاً
    List<Map<String, dynamic>> existingCustomers = await db.query(
      'customers',
      where: 'phone = ?',
      whereArgs: [customerData['phone']],
    );
    
    if (existingCustomers.isNotEmpty) {
      print('⚠️  تحذير: العميل موجود بالفعل في قاعدة البيانات');
      print('   معرف العميل: ${existingCustomers.first['id']}');
      print('   الاسم: ${existingCustomers.first['name']}');
      await db.close();
      return;
    }
    
    // إضافة العميل إلى قاعدة البيانات
    int customerId = await db.insert('customers', customerData);
    
    print('✅ تم إضافة العميل بنجاح!');
    print('   معرف العميل: $customerId');
    print('   الاسم: ${customerData['name']}');
    print('   الهاتف: ${customerData['phone']}');
    print('   المحافظة: ${customerData['governorate']}');
    print('   المنطقة: ${customerData['area']}');
    
    // التحقق من إضافة العميل
    List<Map<String, dynamic>> addedCustomers = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [customerId],
    );
    
    if (addedCustomers.isNotEmpty) {
      Map<String, dynamic> addedCustomer = addedCustomers.first;
      print('\n📊 تفاصيل العميل المضاف:');
      print('   المعرف: ${addedCustomer['id']}');
      print('   الاسم: ${addedCustomer['name']}');
      print('   الهاتف: ${addedCustomer['phone']}');
      print('   العنوان: ${addedCustomer['address']}');
      print('   المحافظة: ${addedCustomer['governorate']}');
      print('   المنطقة: ${addedCustomer['area']}');
      print('   الملاحظات: ${addedCustomer['notes']}');
      print('   تاريخ الإنشاء: ${addedCustomer['created_at']}');
    }
    
    await db.close();
    print('\n🎉 تم إضافة عميل شركة النيل بنجاح إلى قاعدة البيانات!');
    
  } catch (e) {
    print('❌ خطأ في إضافة العميل: $e');
  }
} 