import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class IconGeneratorScreen extends StatefulWidget {
  const IconGeneratorScreen({super.key});

  @override
  State<IconGeneratorScreen> createState() => _IconGeneratorScreenState();
}

class _IconGeneratorScreenState extends State<IconGeneratorScreen> {
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: const Text(
          'إنشاء أيقونة التطبيق',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // عرض الأيقونة المولدة
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const RadialGradient(
                  colors: [
                    Color(0xFF4A90E2),
                    Color(0xFF20B2AA),
                    Color(0xFF008B8B),
                  ],
                  stops: [0.0, 0.5, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // الصليب الطبي
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 16,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 80,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ],
                  ),
                  // النص
                  Positioned(
                    bottom: 40,
                    child: Column(
                      children: [
                        const Text(
                          'ATLAS',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                offset: Offset(1, 1),
                                blurRadius: 2,
                                color: Colors.black26,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'MEDICAL',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.normal,
                            shadows: [
                              Shadow(
                                offset: Offset(1, 1),
                                blurRadius: 2,
                                color: Colors.black26,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),
            
            // زر إنشاء الأيقونة
            SizedBox(
              width: 200,
              height: 50,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateIcon,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A90E2),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                ),
                child: _isGenerating
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'إنشاء أيقونة التطبيق',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 20),
            
            // تعليمات
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Column(
                children: [
                  Text(
                    'تعليمات إنشاء الأيقونة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4A90E2),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. اضغط على زر "إنشاء أيقونة التطبيق"\n'
                    '2. انتظر حتى يتم إنشاء الأيقونة\n'
                    '3. سيتم حفظ الأيقونة في مجلد assets/images\n'
                    '4. شغل: flutter pub run flutter_launcher_icons\n'
                    '5. أعد بناء التطبيق لرؤية الأيقونة الجديدة',
                    style: TextStyle(fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateIcon() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      // إنشاء الأيقونة
      final iconBytes = await _createIconBytes();
      
      // حفظ الأيقونة
      final directory = await getApplicationDocumentsDirectory();
      final assetsDir = Directory('${directory.path}/../assets/images');
      if (!await assetsDir.exists()) {
        await assetsDir.create(recursive: true);
      }
      
      final iconFile = File('${assetsDir.path}/atlas_icon.png');
      await iconFile.writeAsBytes(iconBytes);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ تم إنشاء الأيقونة بنجاح في: ${iconFile.path}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في إنشاء الأيقونة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<Uint8List> _createIconBytes() async {
    // إنشاء صورة 1024x1024
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    const size = Size(1024, 1024);
    
    // رسم الخلفية الدائرية
    _drawBackground(canvas, size);
    
    // رسم الصليب الطبي
    _drawMedicalCross(canvas, size);
    
    // رسم النص
    _drawText(canvas, size);
    
    // رسم العناصر الزخرفية
    _drawDecorations(canvas, size);
    
    // تحويل الرسم إلى صورة
    final picture = recorder.endRecording();
    final image = await picture.toImage(1024, 1024);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  void _drawBackground(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    const radius = 480.0;
    
    // إنشاء تدرج لوني
    final gradient = RadialGradient(
      colors: const [
        Color(0xFF4A90E2), // أزرق تركوازي
        Color(0xFF20B2AA), // أزرق متوسط
        Color(0xFF008B8B), // أزرق داكن
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    final paint = Paint()
      ..shader = gradient.createShader(Rect.fromCircle(center: center, radius: radius));
    
    // رسم الدائرة
    canvas.drawCircle(center, radius, paint);
    
    // إضافة ظل
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(
      center + const Offset(4, 4),
      radius,
      shadowPaint,
    );
  }

  void _drawMedicalCross(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    const crossWidth = 16.0;
    const crossHeight = 240.0;
    
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    // الخط العمودي
    final verticalRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: crossWidth,
        height: crossHeight,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(verticalRect, paint);
    
    // الخط الأفقي
    final horizontalRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: crossHeight,
        height: crossWidth,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(horizontalRect, paint);
  }

  void _drawText(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // رسم نص "ATLAS"
    final atlasTextPainter = TextPainter(
      text: const TextSpan(
        text: 'ATLAS',
        style: TextStyle(
          fontSize: 120,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    atlasTextPainter.layout();
    
    final atlasPosition = Offset(
      center.dx - atlasTextPainter.width / 2,
      center.dy + 80,
    );
    
    // إضافة ظل للنص
    final shadowPainter = TextPainter(
      text: const TextSpan(
        text: 'ATLAS',
        style: TextStyle(
          fontSize: 120,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    shadowPainter.layout();
    shadowPainter.paint(canvas, atlasPosition + const Offset(4, 4));
    
    // رسم النص الرئيسي
    atlasTextPainter.paint(canvas, atlasPosition);
    
    // رسم نص "MEDICAL"
    final medicalTextPainter = TextPainter(
      text: const TextSpan(
        text: 'MEDICAL',
        style: TextStyle(
          fontSize: 48,
          fontWeight: FontWeight.normal,
          color: Colors.white,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    medicalTextPainter.layout();
    
    final medicalPosition = Offset(
      center.dx - medicalTextPainter.width / 2,
      center.dy + 200,
    );
    
    // إضافة ظل للنص
    final medicalShadowPainter = TextPainter(
      text: const TextSpan(
        text: 'MEDICAL',
        style: TextStyle(
          fontSize: 48,
          fontWeight: FontWeight.normal,
          color: Colors.black,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    medicalShadowPainter.layout();
    medicalShadowPainter.paint(canvas, medicalPosition + const Offset(2, 2));
    
    // رسم النص الفرعي
    medicalTextPainter.paint(canvas, medicalPosition);
  }

  void _drawDecorations(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    const radius = 20.0;
    const positions = [
      Offset(200, 200),
      Offset(824, 200),
      Offset(200, 824),
      Offset(824, 824),
    ];
    
    for (final position in positions) {
      canvas.drawCircle(position, radius, paint);
    }
  }
} 
