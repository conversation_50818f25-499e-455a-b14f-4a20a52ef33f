import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/main.dart';
import 'package:atlas_medical_supplies/screens/dashboard_screen.dart';
import 'package:atlas_medical_supplies/screens/customers_screen.dart';
import 'dart:async';

void main() {
  group('اختبار أداء الواجهات', () {
    testWidgets('اختبار سرعة تحميل الشاشة الرئيسية', (
      WidgetTester tester,
    ) async {
      final stopwatch = Stopwatch();

      stopwatch.start();
      await tester.pumpWidget(const AtlasMedicalApp());
      await tester.pumpAndSettle();
      stopwatch.stop();

      print('وقت تحميل الشاشة الرئيسية: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(3000));
    });

    testWidgets('اختبار سرعة تحميل شاشة العملاء', (WidgetTester tester) async {
      final stopwatch = Stopwatch();

      stopwatch.start();
      await tester.pumpWidget(const MaterialApp(home: CustomersScreen()));
      await tester.pumpAndSettle();
      stopwatch.stop();

      print('وقت تحميل شاشة العملاء: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
    });



    testWidgets('اختبار سرعة تحميل لوحة التحكم', (WidgetTester tester) async {
      final stopwatch = Stopwatch();

      stopwatch.start();
      await tester.pumpWidget(const MaterialApp(home: DashboardScreen()));
      await tester.pumpAndSettle();
      stopwatch.stop();

      print('وقت تحميل لوحة التحكم: ${stopwatch.elapsedMilliseconds}ms');
      expect(stopwatch.elapsedMilliseconds, lessThan(3000));
    });
  });
}
