# 📱 ميزة إرسال تفاصيل الفواتير والتحصيل

## 📋 نظرة عامة

تم إضافة ميزة إرسال تفاصيل الفواتير والمبالغ المدفوعة عبر WhatsApp والرسائل النصية. هذه الميزة تسمح للمستخدمين بإرسال تفاصيل الفواتير والتحصيلات للعملاء بشكل تلقائي.

## 🚀 الميزات المضافة

### 1. **إرسال تفاصيل الفواتير**
- إرسال تفاصيل الفاتورة عبر WhatsApp
- إرسال تفاصيل الفاتورة عبر الرسائل النصية
- تنسيق جميل للرسائل مع الرموز التعبيرية
- تضمين جميع تفاصيل الفاتورة والمنتجات

### 2. **إرسال تنبيهات التحصيل**
- إرسال تنبيهات التحصيل عبر WhatsApp
- إرسال تنبيهات التحصيل عبر الرسائل النصية
- تضمين تفاصيل المبلغ المحصل والمحصل

### 3. **إدارة الأذونات**
- طلب أذونات إرسال الرسائل النصية
- التحقق من وجود WhatsApp
- معالجة الأخطاء بشكل مناسب

## 📦 المكتبات المطلوبة

### إضافة المكتبات إلى `pubspec.yaml`:
```yaml
dependencies:
  # Permissions & Messaging
  permission_handler: ^11.3.1
  url_launcher: ^6.2.2
  intl: ^0.20.2
```

### تثبيت المكتبات:
```bash
flutter pub get
```

## 🔧 الإعدادات المطلوبة

### 1. **إعدادات Android**

#### أ. إضافة الأذونات في `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
```

#### ب. إضافة Method Channel في `android/app/src/main/kotlin/.../MainActivity.kt`:
```kotlin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.telephony.SmsManager
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity: FlutterActivity() {
    private val CHANNEL = "sms_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "sendSMS" -> {
                    val phoneNumber = call.argument<String>("phoneNumber")
                    val message = call.argument<String>("message")
                    
                    if (phoneNumber != null && message != null) {
                        sendSMS(phoneNumber, message, result)
                    } else {
                        result.error("INVALID_ARGUMENTS", "Phone number or message is null", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun sendSMS(phoneNumber: String, message: String, result: MethodChannel.Result) {
        try {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.SEND_SMS) == PackageManager.PERMISSION_GRANTED) {
                val smsManager = SmsManager.getDefault()
                smsManager.sendTextMessage(phoneNumber, null, message, null, null)
                result.success(true)
            } else {
                result.error("PERMISSION_DENIED", "SMS permission not granted", null)
            }
        } catch (e: Exception) {
            result.error("SMS_ERROR", "Failed to send SMS: ${e.message}", null)
        }
    }
}
```

### 2. **إعدادات iOS**

#### أ. إضافة الأذونات في `ios/Runner/Info.plist`:
```xml
<key>NSUserTrackingUsageDescription</key>
<string>This app needs access to send SMS messages.</string>
```

## 📱 استخدام الميزة

### 1. **إرسال تفاصيل الفاتورة عبر WhatsApp**
```dart
final success = await MessagingService.sendInvoiceViaWhatsApp(
  customerPhone: '01012345678',
  customerName: 'أحمد محمد',
  invoiceNumber: 'INV-2024-001',
  totalAmount: 1500.0,
  paidAmount: 500.0,
  remainingAmount: 1000.0,
  invoiceDate: '2024-01-15',
  invoiceItems: [
    {
      'name': 'قفازات طبية',
      'quantity': 10,
      'price': 50.0,
    },
    {
      'name': 'كمامات',
      'quantity': 20,
      'price': 25.0,
    },
  ],
  invoiceId: 123, // معرف الفاتورة لجلب تفاصيل التحصيلات
);
```

### 2. **إرسال تفاصيل الفاتورة عبر الرسائل النصية**
```dart
final success = await MessagingService.sendInvoiceViaSMS(
  customerPhone: '01012345678',
  customerName: 'أحمد محمد',
  invoiceNumber: 'INV-2024-001',
  totalAmount: 1500.0,
  paidAmount: 500.0,
  remainingAmount: 1000.0,
  invoiceDate: '2024-01-15',
  invoiceItems: [
    {
      'name': 'قفازات طبية',
      'quantity': 10,
      'price': 50.0,
    },
  ],
  invoiceId: 123, // معرف الفاتورة لجلب تفاصيل التحصيلات
);
```

### 3. **إرسال تنبيه التحصيل**
```dart
final success = await MessagingService.sendCollectionAlertViaWhatsApp(
  customerPhone: '01012345678',
  customerName: 'أحمد محمد',
  amount: 500.0,
  collectionDate: '2024-01-15',
  collectorName: 'محمد علي',
  notes: 'تم التحصيل نقداً',
);
```

## 🆕 الميزات الجديدة (2024-12-19)

### ✅ **تفاصيل التحصيلات في رسالة الفاتورة**
- **إضافة تفاصيل كاملة للتحصيلات** مع التواريخ
- **عرض طريقة الدفع** لكل تحصيل (نقداً، شيك، تحويل بنكي)
- **عرض اسم المحصل** لكل عملية تحصيل
- **عرض الملاحظات** إن وجدت
- **ترتيب التحصيلات حسب التاريخ**

### ✅ **تحسينات الرسالة**
- **تنسيق محسن** مع رموز تعبيرية
- **معلومات أكثر تفصيلاً** عن كل تحصيل
- **عرض تاريخ الفاتورة** بشكل واضح
- **حفظ جميع البيانات** حتى لو لم تكن هناك تحصيلات

## 📄 تنسيق الرسائل

### 1. **رسالة الفاتورة (محدثة)**
```
🏥 أطلس للمستلزمات الطبية
📋 تفاصيل الفاتورة

👤 العميل: أحمد محمد
📄 رقم الفاتورة: INV-2024-001
📅 تاريخ الفاتورة: 2024-01-15

📦 المنتجات:
• قفازات طبية
  الكمية: 10 × 50.00 = 500.00 ج.م
• كمامات
  الكمية: 20 × 25.00 = 500.00 ج.م

💰 الإجماليات:
المبلغ الإجمالي: 1,500.00 ج.م
المبلغ المدفوع: 500.00 ج.م
المبلغ المتبقي: 1,000.00 ج.م

📊 تفاصيل التحصيلات:
📅 2024-01-16
💰 المبلغ: 300.00 ج.م
👤 المحصل: محمد علي
💳 طريقة الدفع: نقداً

📅 2024-01-20
💰 المبلغ: 200.00 ج.م
👤 المحصل: أحمد حسن
💳 طريقة الدفع: شيك
📝 ملاحظات: شيك رقم 12345

شكراً لتعاملكم معنا 🙏
للاستفسار: 01125312343
```

### 2. **رسالة التحصيل**
```
🏥 أطلس للمستلزمات الطبية
💰 تنبيه تحصيل

👤 العميل: أحمد محمد
💵 المبلغ المحصل: 500.00 ج.م
📅 تاريخ التحصيل: 2024-01-15
👨‍💼 المحصل: محمد علي
📝 ملاحظات: تم التحصيل نقداً

شكراً لتعاملكم معنا 🙏
للاستفسار: 01125312343
```

## 🔧 الوظائف المساعدة

### 1. **تنظيف رقم الهاتف**
```dart
final cleanPhone = MessagingService._cleanPhoneNumber('************');
// النتيجة: 20101234567
```

### 2. **التحقق من وجود WhatsApp**
```dart
final hasWhatsApp = await MessagingService.isWhatsAppInstalled();
```

### 3. **التحقق من إمكانية إرسال الرسائل النصية**
```dart
final canSendSMS = await MessagingService.canSendSMS();
```

### 4. **طلب الأذونات**
```dart
final permissions = await MessagingService.requestPermissions();
print('SMS: ${permissions['sms']}');
print('Phone: ${permissions['phone']}');
```

## 🎯 التكامل مع الواجهات

### 1. **إضافة أزرار الإرسال في شاشة الفواتير**
```dart
PopupMenuButton<String>(
  onSelected: (value) {
    if (value == 'whatsapp') {
      _sendInvoiceViaWhatsApp(invoice);
    } else if (value == 'sms') {
      _sendInvoiceViaSMS(invoice);
    }
  },
  itemBuilder: (context) => [
    PopupMenuItem(
      value: 'whatsapp',
      child: Row(
        children: [
          Icon(Icons.message, color: Colors.green),
          SizedBox(width: 8),
          Text('إرسال واتساب'),
        ],
      ),
    ),
    PopupMenuItem(
      value: 'sms',
      child: Row(
        children: [
          Icon(Icons.sms, color: Colors.blue),
          SizedBox(width: 8),
          Text('رسالة نصية'),
        ],
      ),
    ),
  ],
),
```

### 2. **إضافة أزرار الإرسال في شاشة التحصيل**
```dart
ElevatedButton.icon(
  onPressed: () => _sendCollectionAlert(collection),
  icon: Icon(Icons.message),
  label: Text('إرسال تنبيه'),
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.green,
    foregroundColor: Colors.white,
  ),
),
```

## ⚠️ ملاحظات مهمة

### 1. **الأذونات**
- يجب منح إذن إرسال الرسائل النصية
- يجب وجود WhatsApp مثبت للإرسال عبر WhatsApp
- قد تحتاج لإعدادات إضافية حسب نظام التشغيل

### 2. **تنسيق الأرقام**
- يتم تنظيف أرقام الهاتف تلقائياً
- إضافة رمز البلد (+2) تلقائياً
- إزالة الرموز والمسافات

### 3. **معالجة الأخطاء**
- جميع العمليات محمية بـ try-catch
- رسائل خطأ واضحة للمستخدم
- تسجيل الأخطاء للتحليل

## 🧪 اختبار الميزة

### 1. **اختبار WhatsApp**
```dart
// اختبار إرسال فاتورة عبر WhatsApp
final success = await MessagingService.sendInvoiceViaWhatsApp(
  customerPhone: '01012345678',
  customerName: 'عميل اختبار',
  invoiceNumber: 'TEST-001',
  totalAmount: 100.0,
  paidAmount: 50.0,
  remainingAmount: 50.0,
  invoiceDate: '2024-01-15',
  invoiceItems: [
    {'name': 'منتج اختبار', 'quantity': 1, 'price': 100.0},
  ],
);
```

### 2. **اختبار الرسائل النصية**
```dart
// اختبار إرسال رسالة نصية
final success = await MessagingService.sendInvoiceViaSMS(
  customerPhone: '01012345678',
  customerName: 'عميل اختبار',
  invoiceNumber: 'TEST-001',
  totalAmount: 100.0,
  paidAmount: 50.0,
  remainingAmount: 50.0,
  invoiceDate: '2024-01-15',
  invoiceItems: [
    {'name': 'منتج اختبار', 'quantity': 1, 'price': 100.0},
  ],
);
```

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | إنشاء خدمة الإرسال | `messaging_service.dart` |
| 2024-01-15 | إضافة المكتبات المطلوبة | `pubspec.yaml` |
| 2024-01-15 | تحديث شاشة الفواتير | `invoices_screen.dart` |
| 2024-01-15 | إضافة الأذونات | `AndroidManifest.xml` |

## 🔮 التطويرات المستقبلية

### 1. **ميزات إضافية**
- إرسال رسائل جماعية
- جدولة الرسائل
- قوالب رسائل قابلة للتخصيص
- إحصائيات الإرسال

### 2. **تحسينات**
- دعم Telegram
- دعم البريد الإلكتروني
- إرسال ملفات PDF
- توقيع رقمي للرسائل

### 3. **أمان**
- تشفير الرسائل
- التحقق من صحة الأرقام
- حماية من الرسائل المكررة
- سجل الإرسال 