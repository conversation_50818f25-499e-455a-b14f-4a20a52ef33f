import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../database/database_helper.dart';

class InvoiceSharingService {
  // إضافة cache للرسائل
  static final Map<String, String> _messageCache = {};
  static const Duration _cacheDuration = Duration(minutes: 10);

  /// تنظيف وتنسيق رقم الهاتف - إلغاء تفعيل كود الدولة
  static String formatPhoneNumber(String phone) {
    // إزالة جميع الرموز والمسافات فقط (بدون تعديل الرقم)
    String cleanPhone = phone.trim().replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من صحة الرقم
    if (cleanPhone.length < 10) {
      throw Exception('رقم الهاتف غير صحيح');
    }

    // إلغاء تفعيل كود الدولة - إرجاع الرقم كما هو بعد إزالة الرموز فقط
    print('🔍 تنظيف رقم الهاتف: "$phone" -> "$cleanPhone"');
    print('✅ الرقم النهائي (بدون تعديل): "$cleanPhone"');

    return cleanPhone;
  }

  /// إنشاء رسالة الفاتورة مع تفاصيل التحصيلات
  static Future<String> createInvoiceMessage(
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    // التحقق من cache
    final cacheKey = 'invoice_${invoice['id']}_${customer['id']}';
    if (_messageCache.containsKey(cacheKey)) {
      return _messageCache[cacheKey]!;
    }

    final totalAmount = invoice['total_amount'] ?? 0.0;
    final paidAmount = invoice['paid_amount'] ?? 0.0;
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;
    final isPaid = remainingAmount <= 0;
    final invoiceDate = invoice['date'] ?? '';
    final invoiceNumber = invoice['invoice_number'] ?? '';
    final invoiceId = invoice['id'];

    // جلب تفاصيل التحصيلات
    List<Map<String, dynamic>> collections = [];
    if (invoiceId != null) {
      try {
        final dbHelper = DatabaseHelper();
        collections = await dbHelper.getCollectionsByInvoice(invoiceId);
      } catch (e) {
        print('خطأ في جلب التحصيلات: $e');
      }
    }

    // إنشاء تفاصيل التحصيلات
    String collectionsDetails = '';
    if (collections.isNotEmpty) {
      collectionsDetails = '\nتفاصيل التحصيلات:\n';
      for (int i = 0; i < collections.length; i++) {
        final collection = collections[i];
        final amount = collection['amount'] ?? 0.0;
        final date = collection['date'] ?? '';
        final collectorName = collection['collector_name'] ?? '';
        final paymentMethod = collection['payment_method'] ?? 'نقداً';
        final notes = collection['notes'] ?? '';

        collectionsDetails +=
            '''
${i + 1}. مبلغ: ${amount.toStringAsFixed(2)} ج.م
   تاريخ الدفع: $date
   المحصل: $collectorName
   طريقة الدفع: $paymentMethod
   ${notes.isNotEmpty ? 'ملاحظات: $notes' : ''}
''';
      }
    }

    // إنشاء رسالة مختصرة ومهنية بدون رموز
    String message = 'InvoFast\n';
    message += '-------------------\n\n';
    message += 'مرحباً ${customer['name']}،\n\n';

    message += 'فاتورة رقم: $invoiceNumber\n';
    message += 'تاريخ الفاتورة: $invoiceDate\n';
    message += '-------------------\n';
    message += 'المبلغ الإجمالي: ${totalAmount.toStringAsFixed(2)} ج.م\n';

    // إضافة المبلغ المدفوع وقت إنشاء الفاتورة
    if (paidAmount > 0) {
      message +=
          'تم دفع مبلغ ${paidAmount.toStringAsFixed(2)} ج.م عند إنشاء الفاتورة بتاريخ $invoiceDate\n';
    }

    if (!isPaid) {
      message += 'المبلغ المتبقي: ${remainingAmount.toStringAsFixed(2)} ج.م\n';
    }

    // إضافة تفاصيل التحصيلات
    if (collections.isNotEmpty) {
      message += '-------------------\n';
      message += collectionsDetails;
    }

    message += '-------------------\n';
    message += 'شكراً لتعاملكم معنا\n';
    message += 'للاستفسار: 01125312343';

    // حفظ في cache
    _messageCache[cacheKey] = message;

    // تنظيف cache القديم
    _cleanOldCache();

    return message;
  }

  /// تنظيف cache القديم
  static void _cleanOldCache() {
    if (_messageCache.length > 50) {
      // إذا تجاوز عدد العناصر 50
      _messageCache.clear();
    }
  }

  /// إرسال عبر واتساب
  static Future<bool> sendViaWhatsApp(String phone, String message) async {
    try {
      final whatsappUrl =
          'https://wa.me/$phone?text=${Uri.encodeComponent(message)}';

      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(
          Uri.parse(whatsappUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        // محاولة فتح واتساب ويب
        final whatsappWebUrl =
            'https://web.whatsapp.com/send?phone=$phone&text=${Uri.encodeComponent(message)}';
        if (await canLaunchUrl(Uri.parse(whatsappWebUrl))) {
          await launchUrl(
            Uri.parse(whatsappWebUrl),
            mode: LaunchMode.externalApplication,
          );
          return true;
        } else {
          throw Exception('لا يمكن فتح واتساب');
        }
      }
    } catch (e) {
      throw Exception('خطأ في فتح واتساب: $e');
    }
  }

  /// إرسال عبر الرسائل النصية
  static Future<bool> sendViaSMS(String phone, String message) async {
    try {
      final smsUrl = 'sms:$phone?body=${Uri.encodeComponent(message)}';

      if (await canLaunchUrl(Uri.parse(smsUrl))) {
        await launchUrl(
          Uri.parse(smsUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        throw Exception('لا يمكن فتح تطبيق الرسائل');
      }
    } catch (e) {
      throw Exception('خطأ في فتح الرسائل: $e');
    }
  }

  /// نسخ النص إلى الحافظة
  static Future<bool> copyToClipboard(String message) async {
    try {
      await Clipboard.setData(ClipboardData(text: message));
      return true;
    } catch (e) {
      throw Exception('خطأ في نسخ النص: $e');
    }
  }

  /// إرسال عبر البريد الإلكتروني
  static Future<bool> sendViaEmail(
    String email,
    String subject,
    String message,
  ) async {
    try {
      final emailUrl =
          'mailto:$email?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(message)}';

      if (await canLaunchUrl(Uri.parse(emailUrl))) {
        await launchUrl(
          Uri.parse(emailUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        throw Exception('لا يمكن فتح تطبيق البريد الإلكتروني');
      }
    } catch (e) {
      throw Exception('خطأ في فتح البريد الإلكتروني: $e');
    }
  }

  /// عرض خيارات الإرسال
  static Future<String?> showSharingOptions(
    BuildContext context,
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    final phone = customer['primary_phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      throw Exception('لا يوجد رقم هاتف للعميل');
    }

    final formattedPhone = formatPhoneNumber(phone);
    final message = await createInvoiceMessage(invoice, customer);

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إرسال الفاتورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('إرسال فاتورة رقم: ${invoice['invoice_number']}'),
            const SizedBox(height: 8),
            Text('إلى: ${customer['name']}'),
            Text('الهاتف: $formattedPhone'),
            if (customer['email'] != null &&
                customer['email'].toString().isNotEmpty)
              Text('البريد الإلكتروني: ${customer['email']}'),
            const SizedBox(height: 16),
            const Text('اختر طريقة الإرسال:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('whatsapp'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.message, color: Colors.green),
                SizedBox(width: 8),
                Text('واتساب'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('sms'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.sms, color: Colors.blue),
                SizedBox(width: 8),
                Text('رسالة نصية'),
              ],
            ),
          ),
          if (customer['email'] != null &&
              customer['email'].toString().isNotEmpty)
            TextButton(
              onPressed: () => Navigator.of(context).pop('email'),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.email, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('بريد إلكتروني'),
                ],
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('copy'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.copy, color: Colors.purple),
                SizedBox(width: 8),
                Text('نسخ النص'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// إرسال الفاتورة بالطريقة المختارة
  static Future<void> sendInvoice(
    BuildContext context,
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    try {
      final method = await showSharingOptions(context, invoice, customer);
      if (method == null) return;

      final phone = formatPhoneNumber(customer['primary_phone'].toString());
      final message = await createInvoiceMessage(invoice, customer);

      bool success = false;
      String successMessage = '';

      switch (method) {
        case 'whatsapp':
          success = await sendViaWhatsApp(phone, message);
          successMessage = 'تم فتح واتساب بنجاح';
          break;
        case 'sms':
          success = await sendViaSMS(phone, message);
          successMessage = 'تم فتح تطبيق الرسائل بنجاح';
          break;
        case 'email':
          final email = customer['email'].toString();
          final subject =
              'فاتورة رقم ${invoice['invoice_number']} - InvoFast';
          success = await sendViaEmail(email, subject, message);
          successMessage = 'تم فتح تطبيق البريد الإلكتروني بنجاح';
          break;
        case 'copy':
          success = await copyToClipboard(message);
          successMessage = 'تم نسخ نص الفاتورة إلى الحافظة';
          break;
      }

      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(successMessage),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// إنشاء كشف حساب شامل للعميل
  static Future<String> createCustomerStatement(
    Map<String, dynamic> customer,
  ) async {
    final customerId = customer['id'];
    final customerName = customer['name'] ?? '';
    final customerPhone = customer['primary_phone'] ?? '';
    final customerAddress = customer['address'] ?? '';
    final customerGovernorate = customer['governorate'] ?? '';

    if (customerId == null) {
      throw Exception('معرف العميل غير صحيح');
    }

    try {
      final dbHelper = DatabaseHelper();

      // جلب جميع فواتير العميل
      final invoices = await dbHelper.getInvoicesByCustomer(customerId);

      // جلب جميع تحصيلات العميل
      final collections = await dbHelper.getCollectionsByCustomer(customerId);

      // حساب الإجماليات
      double totalInvoices = 0.0;
      double totalPaid = 0.0;
      double totalRemaining = 0.0;

      for (var invoice in invoices) {
        totalInvoices += invoice['total_amount'] ?? 0.0;
        totalPaid += invoice['paid_amount'] ?? 0.0;
        totalRemaining += invoice['remaining_amount'] ?? 0.0;
      }

      // إنشاء تفاصيل الفواتير
      String invoicesDetails = '';
      if (invoices.isNotEmpty) {
        invoicesDetails = '\n📄 تفاصيل الفواتير:\n';
        for (int i = 0; i < invoices.length; i++) {
          final invoice = invoices[i];
          final invoiceNumber = invoice['invoice_number'] ?? '';
          final date = invoice['date'] ?? '';
          final totalAmount = invoice['total_amount'] ?? 0.0;
          final paidAmount = invoice['paid_amount'] ?? 0.0;
          final remainingAmount = invoice['remaining_amount'] ?? 0.0;
          final status = remainingAmount <= 0 ? '✅ مسددة' : '⚠️ متبقية';

          invoicesDetails +=
              '''
${i + 1}. فاتورة رقم: $invoiceNumber
   📅 التاريخ: $date
   💰 المبلغ: ${totalAmount.toStringAsFixed(2)} ج.م
   💳 المدفوع: ${paidAmount.toStringAsFixed(2)} ج.م
   ⚖️ المتبقي: ${remainingAmount.toStringAsFixed(2)} ج.م
   📊 الحالة: $status
''';
        }
      }

      // إنشاء تفاصيل التحصيلات
      String collectionsDetails = '';
      if (collections.isNotEmpty) {
        collectionsDetails = '\n💰 تفاصيل التحصيلات:\n';
        for (int i = 0; i < collections.length; i++) {
          final collection = collections[i];
          final amount = collection['amount'] ?? 0.0;
          final date = collection['date'] ?? '';
          final collectorName = collection['collector_name'] ?? '';
          final paymentMethod = collection['payment_method'] ?? 'نقداً';
          final invoiceNumber = collection['invoice_number'] ?? '';

          collectionsDetails +=
              '''
${i + 1}. 💰 مبلغ: ${amount.toStringAsFixed(2)} ج.م
   📅 التاريخ: $date
   👤 المحصل: $collectorName
   💳 الطريقة: $paymentMethod
   📄 الفاتورة: $invoiceNumber
''';
        }
      }

      return '''
        📋InvoFast
كشف حساب شامل
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

👤 بيانات العميل:
الاسم: $customerName
الهاتف: $customerPhone
${customerAddress.isNotEmpty ? 'العنوان: $customerAddress' : ''}
${customerGovernorate.isNotEmpty ? 'المحافظة: $customerGovernorate' : ''}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 ملخص الحساب:
💰 إجمالي الفواتير: ${totalInvoices.toStringAsFixed(2)} ج.م
💳 إجمالي المدفوع: ${totalPaid.toStringAsFixed(2)} ج.م
⚖️ إجمالي المتبقي: ${totalRemaining.toStringAsFixed(2)} ج.م
${totalRemaining <= 0 ? '✅ الحساب مسدد بالكامل' : '⚠️ يوجد مبالغ متبقية'}$invoicesDetails$collectionsDetails
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

شكراً لتعاملكم معنا 🌟
📞 للاستفسار: 01125312343
📍 العنوان: [TANTA] 
''';
    } catch (e) {
      throw Exception('خطأ في إنشاء كشف الحساب: $e');
    }
  }

  /// إرسال كشف الحساب الشامل للعميل
  static Future<void> sendCustomerStatement(
    BuildContext context,
    Map<String, dynamic> customer,
  ) async {
    try {
      final phone = customer['primary_phone']?.toString().trim();
      if (phone == null || phone.isEmpty) {
        throw Exception('لا يوجد رقم هاتف للعميل');
      }

      final formattedPhone = formatPhoneNumber(phone);
      final message = await createCustomerStatement(customer);

      // عرض خيارات الإرسال
      final method = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إرسال كشف الحساب'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('إرسال كشف حساب شامل لـ: ${customer['name']}'),
              const SizedBox(height: 8),
              Text('الهاتف: $formattedPhone'),
              if (customer['email'] != null &&
                  customer['email'].toString().isNotEmpty)
                Text('البريد الإلكتروني: ${customer['email']}'),
              const SizedBox(height: 16),
              const Text('اختر طريقة الإرسال:'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop('whatsapp'),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.message, color: Colors.green),
                  SizedBox(width: 8),
                  Text('واتساب'),
                ],
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('sms'),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.sms, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('رسالة نصية'),
                ],
              ),
            ),
            if (customer['email'] != null &&
                customer['email'].toString().isNotEmpty)
              TextButton(
                onPressed: () => Navigator.of(context).pop('email'),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.email, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('بريد إلكتروني'),
                  ],
                ),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('copy'),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.copy, color: Colors.purple),
                  SizedBox(width: 8),
                  Text('نسخ النص'),
                ],
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (method == null) return;

      bool success = false;
      String successMessage = '';

      switch (method) {
        case 'whatsapp':
          success = await sendViaWhatsApp(formattedPhone, message);
          successMessage = 'تم فتح واتساب بنجاح';
          break;
        case 'sms':
          success = await sendViaSMS(formattedPhone, message);
          successMessage = 'تم فتح تطبيق الرسائل بنجاح';
          break;
        case 'email':
          final email = customer['email'].toString();
          final subject =
              'كشف حساب شامل - ${customer['name']} - InvoFast';
          success = await sendViaEmail(email, subject, message);
          successMessage = 'تم فتح تطبيق البريد الإلكتروني بنجاح';
          break;
        case 'copy':
          success = await copyToClipboard(message);
          successMessage = 'تم نسخ كشف الحساب إلى الحافظة';
          break;
      }

      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(successMessage),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
