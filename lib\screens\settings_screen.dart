import 'package:flutter/material.dart';
import '../services/auth_service.dart';
// import '../services/firebase_service.dart'; // TODO: Re-enable when Firebase is activated
import '../services/backup_service.dart';
// حذف استيراد خدمة المزامنة التلقائية
// import '../services/auto_sync_service.dart';
import '../database/database_helper.dart';
import 'login_screen.dart';
import '../widgets/atlas_logo.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  String? _currentUserName;
  String? _currentUserEmail;
  String? _currentUserRole;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final user = await AuthService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUserName = user['name'];
        _currentUserEmail = user['email'];
        _currentUserRole = user['role'];
      });
    }
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await AuthService.logout();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    }
  }

  Future<void> _showChangePasswordDialog() async {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('تغيير كلمة المرور'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: currentPasswordController,
                  obscureText: obscureCurrentPassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور الحالية',
                    prefixIcon: const Icon(
                      Icons.lock,
                      color: Color(0xFF4A90E2),
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureCurrentPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFF4A90E2),
                      ),
                      onPressed: () {
                        setDialogState(() {
                          obscureCurrentPassword = !obscureCurrentPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: newPasswordController,
                  obscureText: obscureNewPassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور الجديدة',
                    prefixIcon: const Icon(
                      Icons.lock_outline,
                      color: Color(0xFF4A90E2),
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureNewPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFF4A90E2),
                      ),
                      onPressed: () {
                        setDialogState(() {
                          obscureNewPassword = !obscureNewPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: confirmPasswordController,
                  obscureText: obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'تأكيد كلمة المرور الجديدة',
                    prefixIcon: const Icon(
                      Icons.lock_outline,
                      color: Color(0xFF4A90E2),
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureConfirmPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFF4A90E2),
                      ),
                      onPressed: () {
                        setDialogState(() {
                          obscureConfirmPassword = !obscureConfirmPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (currentPasswordController.text.isEmpty ||
                    newPasswordController.text.isEmpty ||
                    confirmPasswordController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء جميع الحقول'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (newPasswordController.text !=
                    confirmPasswordController.text) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('كلمة المرور الجديدة غير متطابقة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (newPasswordController.text.length < 6) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final result = await AuthService.changePassword(
                  currentPasswordController.text,
                  newPasswordController.text,
                );

                if (!mounted) return;

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(result['message']),
                    backgroundColor: result['success']
                        ? Colors.green
                        : Colors.red,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A90E2),
                foregroundColor: Colors.white,
              ),
              child: const Text('تغيير'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSyncDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المزامنة مع Firebase'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.construction, size: 48, color: Color(0xFF4A90E2)),
            SizedBox(height: 16),
            Text(
              'قريباً',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم إضافة ميزة المزامنة مع Firebase قريباً',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _syncWithFirebase() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري المزامنة مع Firebase...')),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final result = await FirebaseService.syncWithFirebase();
      final result = <String, dynamic>{
        'success': false,
        'message': 'Firebase integration is currently disabled',
      };

      if (mounted) {
        Navigator.of(context).pop(); // إغلاق dialog التحميل

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Text(result['message']),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق dialog التحميل

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء المزامنة: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _showAdvancedSyncDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المزامنة المتقدمة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.construction, size: 48, color: Color(0xFF4A90E2)),
            SizedBox(height: 16),
            Text(
              'قريباً',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم إضافة ميزات المزامنة المتقدمة قريباً',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadLocalData() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري رفع البيانات المحلية...')),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // هنا يمكنك إضافة منطق لجلب البيانات المحلية
      // final result = await FirebaseService.uploadLocalDataToFirebase(
      //   [], // customers
      //   [], // invoices
      //   [], // collections
      // );
      final result = <String, dynamic>{
        'success': false,
        'message': 'Firebase integration is currently disabled',
      };

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Text(result['message']),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء رفع البيانات: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _downloadFromFirebase() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري تحميل البيانات من Firebase...')),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final result = await FirebaseService.downloadDataFromFirebase();
      final result = <String, dynamic>{
        'success': false,
        'message': 'Firebase integration is currently disabled',
      };

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Text(result['message']),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _checkConnection() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(color: Color(0xFF4A90E2)),
            SizedBox(width: 16),
            Text('جاري فحص الاتصال...'),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final isConnected = await FirebaseService.checkFirebaseConnection();
      final isConnected = false;

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(isConnected ? 'متصل' : 'غير متصل'),
            content: Text(
              isConnected
                  ? 'الاتصال بـ Firebase يعمل بشكل صحيح'
                  : 'لا يمكن الاتصال بـ Firebase',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء فحص الاتصال: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('النسخ الاحتياطي'),
        content: const Text('اختر نوع النسخ الاحتياطي:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showCreateBackupDialog();
            },
            child: const Text('إنشاء نسخة احتياطية'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showRestoreBackupDialog();
            },
            child: const Text('استعادة نسخة احتياطية'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showManageBackupsDialog();
            },
            child: const Text('إدارة النسخ الاحتياطية'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDatabaseFixDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إصلاح قاعدة البيانات'),
          content: const Text(
            'سيتم إعادة إنشاء جدول عناصر الفواتير لحل مشاكل قاعدة البيانات. هل تريد المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _fixDatabase();
              },
              child: const Text('إصلاح'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _fixDatabase() async {
    try {
      await _dbHelper.recreateInvoiceItemsTable();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إصلاح قاعدة البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إصلاح قاعدة البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showComingSoonDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.construction, size: 48, color: Color(0xFF4A90E2)),
            const SizedBox(height: 16),
            const Text(
              'قريباً',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showCreateBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء نسخة احتياطية'),
        content: const Text('اختر نوع النسخة الاحتياطية:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _createLocalBackup();
            },
            child: const Text('محلية'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonDialog(
                'نسخ احتياطي Firebase',
                'سيتم إضافة النسخ الاحتياطي على Firebase قريباً',
              );
            },
            child: const Text('Firebase (قريباً)'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonDialog(
                'نسخة احتياطية شاملة',
                'سيتم إضافة النسخ الاحتياطي الشامل قريباً',
              );
            },
            child: const Text('شاملة (محلية + Firebase) (قريباً)'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showRestoreBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة نسخة احتياطية'),
        content: const Text('اختر مصدر النسخة الاحتياطية:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showLocalBackupsList();
            },
            child: const Text('من النسخ المحلية'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonDialog(
                'استعادة من Firebase',
                'سيتم إضافة استعادة النسخ الاحتياطية من Firebase قريباً',
              );
            },
            child: const Text('من Firebase (قريباً)'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showManageBackupsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة النسخ الاحتياطية'),
        content: const Text('اختر الإجراء المطلوب:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showLocalBackupsList(manageMode: true);
            },
            child: const Text('إدارة النسخ المحلية'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonDialog(
                'إدارة نسخ Firebase',
                'سيتم إضافة إدارة النسخ الاحتياطية على Firebase قريباً',
              );
            },
            child: const Text('إدارة نسخ Firebase (قريباً)'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _createLocalBackup() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري إنشاء النسخة الاحتياطية المحلية...')),
          ],
        ),
      ),
    );

    try {
      final backupService = BackupService();
      final result = await backupService.createFullBackup();

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(result['success'] ? 'تم إنشاء النسخة الاحتياطية بنجاح' : result['error'] ?? 'حدث خطأ'),
                if (result['success']) ...[
                  const SizedBox(height: 8),
                  Text('عدد السجلات: ${result['backup_count']}'),
                  Text('حجم الملف: ${(result['file_size'] / 1024).toStringAsFixed(1)} KB'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _createFirebaseBackup() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(
              child: Text('جاري إنشاء النسخة الاحتياطية في Firebase...'),
            ),
          ],
        ),
      ),
    );

    try {
      // Firebase backup is not implemented yet
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('غير متاح'),
            content: const Text('نسخ Firebase غير متاح حالياً. سيتم تفعيله قريباً.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _createComprehensiveBackup() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري إنشاء النسخة الاحتياطية الشاملة...')),
          ],
        ),
      ),
    );

    try {
      final backupService = BackupService();
      final result = await backupService.createFullBackup();

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(result['success'] ? 'تم إنشاء النسخة الاحتياطية الشاملة بنجاح' : result['error'] ?? 'حدث خطأ'),
                if (result['success']) ...[
                  const SizedBox(height: 8),
                  Text('عدد السجلات: ${result['backup_count']}'),
                  Text('حجم الملف: ${(result['file_size'] / 1024).toStringAsFixed(1)} KB'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _showLocalBackupsList({bool manageMode = false}) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF4A90E2)),
            const SizedBox(width: 16),
            Flexible(child: Text('جاري تحميل النسخ الاحتياطية المحلية...')),
          ],
        ),
      ),
    );

    try {
      final backupService = BackupService();
      final backups = await backupService.getBackupFiles();

      if (mounted) {
        Navigator.of(context).pop();

        if (backups.isEmpty) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('لا توجد نسخ احتياطية'),
              content: const Text('لم يتم العثور على نسخ احتياطية محلية.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('حسناً'),
                ),
              ],
            ),
          );
          return;
        }

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              manageMode ? 'إدارة النسخ المحلية' : 'استعادة نسخة محلية',
            ),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: backups.length,
                itemBuilder: (context, index) {
                  final backup = backups[index];
                  final date = DateTime.parse(backup['created_at']);

                  return ListTile(
                    title: Text(backup['file_name']),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${date.day}/${date.month}/${date.year}'),
                        Text('العملاء: ${backup['total_customers']}'),
                        Text('الفواتير: ${backup['total_invoices']}'),
                      ],
                    ),
                    trailing: manageMode
                        ? IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () =>
                                _deleteLocalBackup(backup['file_path']),
                          )
                        : IconButton(
                            icon: const Icon(
                              Icons.restore,
                              color: Color(0xFF4A90E2),
                            ),
                            onPressed: () =>
                                _restoreLocalBackup(backup['file_path']),
                          ),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء تحميل النسخ الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _showFirebaseBackupsList({bool manageMode = false}) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(color: Color(0xFF4A90E2)),
            SizedBox(width: 16),
            Text('جاري تحميل نسخ Firebase...'),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final backups = await BackupService.getFirebaseBackups();
      final backups = <Map<String, dynamic>>[];

      if (mounted) {
        Navigator.of(context).pop();

        if (backups.isEmpty) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('لا توجد نسخ احتياطية'),
              content: const Text(
                'لم يتم العثور على نسخ احتياطية في Firebase.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('حسناً'),
                ),
              ],
            ),
          );
          return;
        }

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              manageMode ? 'إدارة نسخ Firebase' : 'استعادة نسخة من Firebase',
            ),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: backups.length,
                itemBuilder: (context, index) {
                  final backup = backups[index];
                  final date = DateTime.parse(backup['created_at']);

                  return ListTile(
                    title: Text('نسخة ${index + 1}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: ${date.day}/${date.month}/${date.year}'),
                        Text('العملاء: ${backup['total_customers']}'),
                        Text('الفواتير: ${backup['total_invoices']}'),
                      ],
                    ),
                    trailing: manageMode
                        ? IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () =>
                                _deleteFirebaseBackup(backup['backup_id']),
                          )
                        : IconButton(
                            icon: const Icon(
                              Icons.restore,
                              color: Color(0xFF4A90E2),
                            ),
                            onPressed: () =>
                                _restoreFirebaseBackup(backup['backup_id']),
                          ),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء تحميل نسخ Firebase: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _restoreLocalBackup(String filePath) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'سيتم حذف جميع البيانات الحالية واستبدالها بالنسخة الاحتياطية. هل أنت متأكد؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(color: Color(0xFF4A90E2)),
            SizedBox(width: 16),
            Text('جاري استعادة النسخة الاحتياطية...'),
          ],
        ),
      ),
    );

    try {
      final backupService = BackupService();
      final result = await backupService.restoreBackup(filePath);

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(result['success'] ? 'تم استعادة النسخة الاحتياطية بنجاح' : result['error'] ?? 'حدث خطأ'),
                if (result['success']) ...[
                  const SizedBox(height: 8),
                  Text('تم استعادة: ${result['restored_records']} سجل'),
                  Text('تاريخ النسخة: ${result['backup_date']}'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء استعادة النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _restoreFirebaseBackup(String backupId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'سيتم حذف جميع البيانات الحالية واستبدالها بالنسخة الاحتياطية. هل أنت متأكد؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(color: Color(0xFF4A90E2)),
            SizedBox(width: 16),
            Text('جاري استعادة النسخة الاحتياطية من Firebase...'),
          ],
        ),
      ),
    );

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final result = await BackupService.restoreFirebaseBackup(backupId);
      final result = <String, dynamic>{
        'success': false,
        'message': 'Firebase integration is currently disabled',
        'restored_customers': 0,
        'restored_invoices': 0,
        'restored_collections': 0,
        'restored_users': 0,
      };

      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(result['success'] ? 'نجح' : 'خطأ'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(result['message']),
                if (result['success']) ...[
                  const SizedBox(height: 8),
                  Text('تم استعادة:'),
                  Text('العملاء: ${result['restored_customers']}'),
                  Text('الفواتير: ${result['restored_invoices']}'),
                  Text('التحصيلات: ${result['restored_collections']}'),
                  Text('المستخدمين: ${result['restored_users']}'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء استعادة النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _deleteLocalBackup(String filePath) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final backupService = BackupService();
      final success = await backupService.deleteBackup(filePath);

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(success ? 'نجح' : 'خطأ'),
            content: Text(
              success
                  ? 'تم حذف النسخة الاحتياطية بنجاح'
                  : 'فشل في حذف النسخة الاحتياطية',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء حذف النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> _deleteFirebaseBackup(String backupId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذه النسخة الاحتياطية من Firebase؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // TODO: Firebase integration - re-enable when Firebase is activated
      // final success = await BackupService.deleteFirebaseBackup(backupId);
      final success = false;

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(success ? 'نجح' : 'خطأ'),
            content: Text(
              success
                  ? 'تم حذف النسخة الاحتياطية من Firebase بنجاح'
                  : 'فشل في حذف النسخة الاحتياطية من Firebase',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء حذف النسخة الاحتياطية: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    }
  }

  // حذف دالة عرض حالة المزامنة التلقائية
  // void _showAutoSyncStatus() {
  //   final status = AutoSyncService.getSyncStatus();

  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: const Text('حالة المزامنة التلقائية'),
  //       content: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           _buildStatusRow(
  //             'الحالة',
  //             status['is_enabled'] ? 'مفعلة' : 'معطلة',
  //             status['is_enabled'] ? Colors.green : Colors.red,
  //           ),
  //           _buildStatusRow(
  //             'المزامنة الأولية',
  //             status['is_initial_sync_done'] ? 'مكتملة' : 'قيد التنفيذ',
  //             status['is_initial_sync_done'] ? Colors.green : Colors.orange,
  //           ),
  //           _buildStatusRow(
  //             'آخر مزامنة',
  //             status['last_sync_time'] ?? 'لم تتم بعد',
  //             Colors.blue,
  //           ),
  //           _buildStatusRow(
  //             'آخر نسخة احتياطية',
  //             status['last_backup_time'] ?? 'لم تتم بعد',
  //             Colors.blue,
  //           ),
  //           _buildStatusRow(
  //             'فترة المزامنة',
  //             'كل ${status['sync_interval_seconds']} ثانية',
  //             Colors.grey,
  //           ),
  //           _buildStatusRow(
  //             'فترة النسخ الاحتياطي',
  //             'كل ${status['backup_interval_minutes']} دقيقة',
  //             Colors.grey,
  //           ),
  //           const SizedBox(height: 8),
  //           Container(
  //             padding: const EdgeInsets.all(8),
  //             decoration: BoxDecoration(
  //               color: Colors.grey[100],
  //               borderRadius: BorderRadius.circular(8),
  //             ),
  //             child: Text(
  //               'الحالة الحالية: ${status['last_sync_status']}',
  //               style: const TextStyle(fontSize: 12),
  //             ),
  //           ),
  //         ],
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () {
  //             Navigator.of(context).pop();
  //             _toggleAutoSync(status['is_enabled']);
  //           },
  //           child: Text(
  //             status['is_enabled'] ? 'إيقاف المزامنة' : 'تفعيل المزامنة',
  //           ),
  //         ),
  //         TextButton(
  //           onPressed: () => Navigator.of(context).pop(),
  //           child: const Text('إغلاق'),
  //           ),
  //         ],
  //       ),
  //     );
  //   }
  // }

  // حذف دالة بناء صف الحالة
  // Widget _buildStatusRow(String label, String value, Color color) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(vertical: 4),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       children: [
  //         Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
  //         Text(
  //           value,
  //           style: TextStyle(color: color, fontWeight: FontWeight.w500),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // حذف دالة تبديل المزامنة التلقائية
  // void _toggleAutoSync(bool isCurrentlyEnabled) {
  //   if (isCurrentlyEnabled) {
  //     AutoSyncService.disableAutoSync();
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('تم إيقاف المزامنة التلقائية'),
  //         backgroundColor: Colors.orange,
  //       ),
  //     );
  //   } else {
  //     AutoSyncService.enableAutoSync();
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('تم تفعيل المزامنة التلقائية'),
  //         backgroundColor: Colors.green,
  //       ),
  //     );
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: Text(
          'الإعدادات',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات المستخدم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4A90E2),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: const Color(0xFF4A90E2),
                          child: Text(
                            _currentUserName?.substring(0, 1) ?? 'م',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _currentUserName ?? '',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                _currentUserEmail ?? '',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: 4),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF40E0D0,
                                  ).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  _currentUserRole ?? '',
                                  style: const TextStyle(
                                    color: Color(0xFF4A90E2),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات الحساب
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.lock, color: Color(0xFF4A90E2)),
                    title: const Text('تغيير كلمة المرور'),
                    subtitle: const Text('تحديث كلمة المرور الخاصة بك'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _showChangePasswordDialog,
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(
                      Icons.cloud_sync,
                      color: Color(0xFF4A90E2),
                    ),
                    title: const Text('المزامنة مع Firebase'),
                    subtitle: const Text('قريباً - مزامنة البيانات مع السحابة'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _showSyncDialog,
                  ),
                  // حذف عنصر المزامنة التلقائية
                  // const Divider(height: 1),
                  // ListTile(
                  //   leading: const Icon(Icons.sync, color: Color(0xFF4A90E2)),
                  //   title: const Text('المزامنة التلقائية'),
                  //   subtitle: const Text('حالة النسخ الاحتياطي التلقائي'),
                  //   trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  //   onTap: _showAutoSyncStatus,
                  // ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.backup, color: Color(0xFF4A90E2)),
                    title: const Text('النسخ الاحتياطي'),
                    subtitle: const Text('إنشاء واستعادة النسخ الاحتياطية'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _showBackupDialog,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // معلومات التطبيق
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.info, color: Color(0xFF4A90E2)),
                    title: const Text('حول التطبيق'),
                    subtitle: const Text(
                      'InvoFast - Version 1.1.0',
                    ),
                    onTap: () {
                      showAboutDialog(
                        context: context,
                        applicationName: 'InvoFast',
                        applicationVersion: '1.1.0',
                        applicationIcon: const Icon(
                          Icons.medical_services,
                          size: 48,
                          color: Color(0xFF4A90E2),
                        ),
                        children: const [
                          Text('تطبيق إدارة العملاء والفواتير والمدفوعات'),
                          SizedBox(height: 8),
                          Text('تم تطويره باستخدام Flutter'),
                          SizedBox(height: 16),
                          Text(
                            'الإصدار: 1.1.0',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4A90E2),
                            ),
                          ),
                          Text('رقم البناء: 3'),
                          SizedBox(height: 8),
                          Text('آخر تحديث: يناير 2025'),
                        ],
                      );
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.build, color: Color(0xFF4A90E2)),
                    title: const Text('إصلاح قاعدة البيانات'),
                    subtitle: const Text('إعادة إنشاء جدول عناصر الفواتير'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _showDatabaseFixDialog,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // زر تسجيل الخروج
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: _logout,
                icon: const Icon(Icons.logout),
                label: const Text(
                  'تسجيل الخروج',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 3,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
