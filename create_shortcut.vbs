' إنشاء اختصار لتطبيق أطلس للمستلزمات الطبية
' VBScript

Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\أطلس للمستلزمات الطبية.lnk")

' مسار الملف
oShellLink.TargetPath = WshShell.CurrentDirectory & "\run_app.bat"

' مجلد العمل
oShellLink.WorkingDirectory = WshShell.CurrentDirectory

' وصف الاختصار
oShellLink.Description = "تشغيل تطبيق أطلس للمستلزمات الطبية"

' أيقونة الاختصار (استخدام أيقونة Flutter)
oShellLink.IconLocation = WshShell.CurrentDirectory & "\android\app\src\main\res\mipmap-hdpi\ic_launcher.png"

' حفظ الاختصار
oShellLink.Save

MsgBox "تم إنشاء اختصار 'أطلس للمستلزمات الطبية' على سطح المكتب بنجاح!" & vbCrLf & vbCrLf & "يمكنك الآن النقر على الاختصار لتشغيل التطبيق.", 64, "تم إنشاء الاختصار" 