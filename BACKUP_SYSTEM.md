# نظام النسخ الاحتياطية الجديد - Atlas Medical Supplies

## 📋 نظرة عامة

تم إزالة Firebase وإضافة نظام نسخ احتياطية محلي مع رفع تلقائي على Google Drive كل ثانية.

## 🔄 التغييرات الرئيسية

### ✅ ما تم إزالته:
- **Firebase Service** - تم حذفه بالكامل
- **Firebase Options** - تم حذفه
- **Firebase Authentication** - تم إزالته من AuthService

### ✅ ما تم إضافته:
- **Google Drive Service** - لرفع النسخ الاحتياطية
- **Auto Backup Service** - للنسخ الاحتياطية التلقائية كل ثانية
- **Backup Settings Screen** - لإدارة النسخ الاحتياطية
- **قاعدة بيانات محلية** - SQLite فقط

## 🚀 الميزات الجديدة

### 1. النسخ الاحتياطية التلقائية
- **التكرار**: كل ثانية
- **الموقع**: Google Drive Folder
- **التنسيق**: JSON
- **الاحتفاظ**: آخر 10 نسخ فقط

### 2. إعدادات النسخ الاحتياطية
- تفعيل/تعطيل النسخ التلقائي
- نسخة احتياطية يدوية
- استعادة من نسخة احتياطية
- حذف نسخ احتياطية قديمة

### 3. قاعدة البيانات المحلية
- **SQLite** - قاعدة بيانات محلية سريعة
- **لا حاجة للإنترنت** - يعمل بدون اتصال
- **أداء عالي** - استجابة فورية

## 📁 هيكل الملفات

```
lib/
├── services/
│   ├── google_drive_service.dart     # خدمة Google Drive
│   ├── auto_backup_service.dart      # النسخ الاحتياطية التلقائية
│   └── auth_service.dart             # محدث (بدون Firebase)
├── screens/
│   └── backup_settings_screen.dart   # إعدادات النسخ الاحتياطية
└── database/
    └── database_helper.dart          # قاعدة البيانات المحلية
```

## ⚙️ الإعداد المطلوب

### 1. Google Drive API
```yaml
# في pubspec.yaml
dependencies:
  googleapis: ^13.0.0
  googleapis_auth: ^1.4.1
  http: ^1.1.0
```

### 2. Service Account
يحتاج إلى إعداد Service Account في Google Cloud Console:
- إنشاء مشروع جديد
- تفعيل Google Drive API
- إنشاء Service Account
- تحميل ملف JSON للcredentials

### 3. تحديث GoogleDriveService
```dart
// في google_drive_service.dart
static const String _folderId = '1eQQ4tTHrtkWGYooG-9mmDJh1ULerTkqB';
// تحديث credentials في الدالة initialize()
```

## 🔧 كيفية الاستخدام

### 1. بدء النسخ الاحتياطية التلقائية
```dart
// في main.dart
await AutoBackupService.startAutoBackup();
```

### 2. نسخة احتياطية يدوية
```dart
final success = await AutoBackupService.performManualBackup();
```

### 3. استعادة من نسخة احتياطية
```dart
final result = await GoogleDriveService.restoreFromBackup(fileId);
```

### 4. الوصول لإعدادات النسخ الاحتياطية
```dart
Navigator.pushNamed(context, '/backup-settings');
```

## 📊 إحصائيات النسخ الاحتياطية

### البيانات المحفوظة:
- **العملاء** - جميع بيانات العملاء
- **الفواتير** - جميع الفواتير
- **التحصيلات** - جميع المدفوعات
- **منتجات الفواتير** - تفاصيل المنتجات
- **المستخدمين** - بيانات المستخدمين

### تنسيق النسخة الاحتياطية:
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0",
  "data": {
    "customers": [...],
    "invoices": [...],
    "collections": [...],
    "invoice_items": [...],
    "users": [...]
  },
  "summary": {
    "customers_count": 100,
    "invoices_count": 500,
    "collections_count": 300,
    "invoice_items_count": 1500,
    "users_count": 5
  }
}
```

## 🔮 المستقبل - Firebase

### ملاحظة مهمة:
```
Firebase سيتم تفعيله قريباً
```

### الخطط المستقبلية:
- إعادة تفعيل Firebase Authentication
- مزامنة مع Firestore
- نسخ احتياطية مزدوجة (محلي + Firebase)
- مزامنة في الوقت الفعلي

## 🛡️ الأمان

### حماية البيانات:
- **تشفير** - البيانات مشفرة في Google Drive
- **صلاحيات** - Service Account محدود الصلاحيات
- **نسخ متعددة** - 10 نسخ احتياطية كحد أدنى
- **استعادة آمنة** - تأكيد قبل الاستعادة

### أفضل الممارسات:
- مراجعة دورية للنسخ الاحتياطية
- اختبار الاستعادة بشكل دوري
- مراقبة مساحة التخزين
- تحديث credentials بشكل دوري

## 📞 الدعم

### في حالة المشاكل:
1. **فحص الاتصال بالإنترنت**
2. **التحقق من Google Drive API**
3. **مراجعة Service Account**
4. **فحص مساحة التخزين**

### رسائل الخطأ الشائعة:
- `❌ خطأ في تهيئة Google Drive` - مشكلة في credentials
- `⚠️ فشل في رفع النسخة الاحتياطية` - مشكلة في الاتصال
- `❌ خطأ في الاستعادة` - مشكلة في البيانات

## 🎯 الخلاصة

النظام الجديد يوفر:
- ✅ **أداء عالي** - قاعدة بيانات محلية سريعة
- ✅ **موثوقية** - نسخ احتياطية كل ثانية
- ✅ **أمان** - حماية متعددة المستويات
- ✅ **سهولة الاستخدام** - واجهة بسيطة وواضحة
- ✅ **استقلالية** - لا يحتاج إنترنت للعمل الأساسي

**Firebase سيتم إعادة تفعيله قريباً مع ميزات إضافية!** 🚀 