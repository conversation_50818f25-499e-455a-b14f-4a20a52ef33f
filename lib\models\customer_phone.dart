class CustomerPhone {
  final int? id;
  final int customerId;
  final String phone;
  final String phoneType;
  final bool isPrimary;
  final String? notes;
  final String? createdAt;

  CustomerPhone({
    this.id,
    required this.customerId,
    required this.phone,
    this.phoneType = 'الرئيسي',
    this.isPrimary = false,
    this.notes,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'phone': phone,
      'phone_type': phoneType,
      'is_primary': isPrimary ? 1 : 0,
      'notes': notes,
      'created_at': createdAt,
    };
  }

  factory CustomerPhone.fromMap(Map<String, dynamic> map) {
    return CustomerPhone(
      id: map['id'] as int?,
      customerId: map['customer_id'] as int,
      phone: map['phone'] as String,
      phoneType: map['phone_type'] as String? ?? 'الرئيسي',
      isPrimary: (map['is_primary'] as int?) == 1,
      notes: map['notes'] as String?,
      createdAt: map['created_at'] as String?,
    );
  }

  CustomerPhone copyWith({
    int? id,
    int? customerId,
    String? phone,
    String? phoneType,
    bool? isPrimary,
    String? notes,
    String? createdAt,
  }) {
    return CustomerPhone(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      phone: phone ?? this.phone,
      phoneType: phoneType ?? this.phoneType,
      isPrimary: isPrimary ?? this.isPrimary,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CustomerPhone(id: $id, customerId: $customerId, phone: $phone, phoneType: $phoneType, isPrimary: $isPrimary, notes: $notes, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerPhone &&
        other.id == id &&
        other.customerId == customerId &&
        other.phone == phone &&
        other.phoneType == phoneType &&
        other.isPrimary == isPrimary &&
        other.notes == notes &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        phone.hashCode ^
        phoneType.hashCode ^
        isPrimary.hashCode ^
        notes.hashCode ^
        createdAt.hashCode;
  }
} 