import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'lib/services/invoice_sharing_service.dart';

/// ملف اختبار سريع لميزة تفاصيل المدفوعات
/// Quick test file for payment details feature

void main() {
  runApp(const PaymentDetailsTestApp());
}

class PaymentDetailsTestApp extends StatelessWidget {
  const PaymentDetailsTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار تفاصيل المدفوعات',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Cairo',
      ),
      home: const PaymentDetailsTestScreen(),
    );
  }
}

class PaymentDetailsTestScreen extends StatefulWidget {
  const PaymentDetailsTestScreen({super.key});

  @override
  State<PaymentDetailsTestScreen> createState() => _PaymentDetailsTestScreenState();
}

class _PaymentDetailsTestScreenState extends State<PaymentDetailsTestScreen> {
  String _testResult = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار تفاصيل المدفوعات'),
        backgroundColor: const Color(0xFF008B8B),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'اختبار ميزة "المبلغ المدفوع فى كل فاتورة بتاريخه تفصيلى"',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // اختبار إنشاء رسالة فاتورة مع تفاصيل التحصيلات
            ElevatedButton.icon(
              onPressed: _testInvoiceMessage,
              icon: const Icon(Icons.receipt),
              label: const Text('اختبار رسالة الفاتورة مع تفاصيل التحصيلات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF008B8B),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار إنشاء كشف حساب شامل
            ElevatedButton.icon(
              onPressed: _testCustomerStatement,
              icon: const Icon(Icons.person),
              label: const Text('اختبار كشف الحساب الشامل للعميل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF008B8B),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // اختبار نسخ النص
            ElevatedButton.icon(
              onPressed: _testCopyToClipboard,
              icon: const Icon(Icons.copy),
              label: const Text('اختبار نسخ النص إلى الحافظة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // عرض النتائج
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResult.isEmpty ? 'اضغط على أحد الأزرار أعلاه لبدء الاختبار' : _testResult,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختبار إنشاء رسالة فاتورة مع تفاصيل التحصيلات
  Future<void> _testInvoiceMessage() async {
    setState(() {
      _testResult = 'جاري اختبار رسالة الفاتورة...\n';
    });

    try {
      // بيانات تجريبية للفاتورة
      final testInvoice = {
        'id': 1,
        'invoice_number': 'INV-2024-001',
        'date': '2024-01-15',
        'total_amount': 1500.0,
        'paid_amount': 800.0,
        'remaining_amount': 700.0,
        'customer_id': 1,
      };

      // بيانات تجريبية للعميل
      final testCustomer = {
        'id': 1,
        'name': 'أحمد محمد',
        'phone': '01125312343',
        'email': '<EMAIL>',
        'address': 'شارع النيل، طنطا',
        'governorate': 'الغربية',
      };

      // إنشاء الرسالة
      final message = await InvoiceSharingService.createInvoiceMessage(
        testInvoice,
        testCustomer,
      );

      setState(() {
        _testResult = '''
✅ تم إنشاء رسالة الفاتورة بنجاح!

📋 محتوى الرسالة:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
$message
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

ملاحظة: هذه رسالة تجريبية. في التطبيق الفعلي، سيتم جلب البيانات من قاعدة البيانات.
''';
      });
    } catch (e) {
      setState(() {
        _testResult = '''
❌ خطأ في إنشاء رسالة الفاتورة:
$e

ملاحظة: تأكد من أن قاعدة البيانات متاحة وأن الدوال تعمل بشكل صحيح.
''';
      });
    }
  }

  /// اختبار إنشاء كشف حساب شامل
  Future<void> _testCustomerStatement() async {
    setState(() {
      _testResult = 'جاري اختبار كشف الحساب الشامل...\n';
    });

    try {
      // بيانات تجريبية للعميل
      final testCustomer = {
        'id': 1,
        'name': 'أحمد محمد',
        'phone': '01125312343',
        'email': '<EMAIL>',
        'address': 'شارع النيل، طنطا',
        'governorate': 'الغربية',
      };

      // إنشاء كشف الحساب
      final statement = await InvoiceSharingService.createCustomerStatement(
        testCustomer,
      );

      setState(() {
        _testResult = '''
✅ تم إنشاء كشف الحساب الشامل بنجاح!

📋 محتوى كشف الحساب:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
$statement
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

ملاحظة: هذا كشف حساب تجريبي. في التطبيق الفعلي، سيتم جلب البيانات من قاعدة البيانات.
''';
      });
    } catch (e) {
      setState(() {
        _testResult = '''
❌ خطأ في إنشاء كشف الحساب الشامل:
$e

ملاحظة: تأكد من أن قاعدة البيانات متاحة وأن الدوال تعمل بشكل صحيح.
''';
      });
    }
  }

  /// اختبار نسخ النص إلى الحافظة
  Future<void> _testCopyToClipboard() async {
    setState(() {
      _testResult = 'جاري اختبار نسخ النص...\n';
    });

    try {
      final testText = '''
📋Atlas Medical Supplies
كشف حساب مفصل
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

مرحباً أحمد محمد،

📄 فاتورة رقم: INV-2024-001
📅 تاريخ الفاتورة: 2024-01-15
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 المبلغ الإجمالي: 1500.00 ج.م
💳 إجمالي المدفوع: 800.00 ج.م
⚠️ المبلغ المتبقي: 700.00 ج.م

📋 تفاصيل التحصيلات:
1. 💰 مبلغ: 500.00 ج.م
   📅 تاريخ الدفع: 2024-01-20
   👤 المحصل: محمد علي
   💳 طريقة الدفع: نقداً
   
2. 💰 مبلغ: 300.00 ج.م
   📅 تاريخ الدفع: 2024-01-25
   👤 المحصل: أحمد حسن
   💳 طريقة الدفع: فودافون كاش
   📝 ملاحظات: رقم العملية 12345

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
شكراً لتعاملكم معنا 🌟
📞 للاستفسار: 01125312343
📍 العنوان: [TANTA]
''';

      await Clipboard.setData(ClipboardData(text: testText));

      setState(() {
        _testResult = '''
✅ تم نسخ النص إلى الحافظة بنجاح!

📋 النص المنسوخ:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
$testText
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

يمكنك الآن لصق النص في أي تطبيق آخر.
''';
      });
    } catch (e) {
      setState(() {
        _testResult = '''
❌ خطأ في نسخ النص:
$e
''';
      });
    }
  }
} 
