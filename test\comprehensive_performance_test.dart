import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/database/database_helper.dart';
import 'package:atlas_medical_supplies/services/auth_service.dart';
import 'package:atlas_medical_supplies/services/performance_service.dart';
import 'package:atlas_medical_supplies/services/storage_service.dart';
import 'package:atlas_medical_supplies/services/auto_sync_service.dart';
import 'package:atlas_medical_supplies/services/auto_backup_service.dart';
import 'package:atlas_medical_supplies/services/backup_service.dart';
import 'package:atlas_medical_supplies/services/messaging_service.dart';
import 'package:atlas_medical_supplies/services/invoice_sharing_service.dart';
import 'package:atlas_medical_supplies/services/invoice_message_service.dart';
import 'package:atlas_medical_supplies/services/google_drive_service.dart';
import 'package:atlas_medical_supplies/services/local_backup_service.dart';
import 'package:atlas_medical_supplies/services/permissions_service.dart';
import 'dart:async';
import 'dart:io';

void main() {
  group('اختبار شامل للأداء والمشاكل', () {
    late DatabaseHelper databaseHelper;
    late Stopwatch stopwatch;

    setUpAll(() async {
      // تهيئة قاعدة البيانات للاختبار
      databaseHelper = DatabaseHelper();
      await databaseHelper.database;

      // إنشاء مستخدم افتراضي للاختبار
      await AuthService.createDefaultUser();
    });

    setUp(() {
      stopwatch = Stopwatch();
    });

    tearDownAll(() async {
      // تنظيف قاعدة البيانات بعد الاختبار
      final db = await databaseHelper.database;
      await db.close();
    });

    group('اختبارات قاعدة البيانات', () {
      test('اختبار سرعة فتح قاعدة البيانات', () async {
        stopwatch.start();
        final db = await databaseHelper.database;
        stopwatch.stop();

        print('⏱️ وقت فتح قاعدة البيانات: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      test('اختبار سرعة استعلام العملاء', () async {
        stopwatch.start();
        final customers = await databaseHelper.getCustomers();
        stopwatch.stop();

        print('⏱️ وقت استعلام العملاء: ${stopwatch.elapsedMilliseconds}ms');
        print('📊 عدد العملاء: ${customers.length}');
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      test('اختبار سرعة استعلام الفواتير', () async {
        stopwatch.start();
        final invoices = await databaseHelper.getInvoices();
        stopwatch.stop();

        print('⏱️ وقت استعلام الفواتير: ${stopwatch.elapsedMilliseconds}ms');
        print('📊 عدد الفواتير: ${invoices.length}');
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      test('اختبار سرعة إضافة عميل جديد', () async {
        stopwatch.start();
        final customerId = await databaseHelper.insertCustomer({
          'name': 'عميل اختبار الأداء',
          'address': 'عنوان اختبار',
          'notes': 'ملاحظات اختبار',
          'governorate': 'القاهرة',
          'area': 'وسط البلد',
        });
        stopwatch.stop();

        print('⏱️ وقت إضافة عميل جديد: ${stopwatch.elapsedMilliseconds}ms');
        expect(customerId, isNotNull);
        expect(stopwatch.elapsedMilliseconds, lessThan(200));
      });

      test('اختبار سرعة البحث في العملاء', () async {
        stopwatch.start();
        final customers = await databaseHelper.searchCustomers('اختبار');
        stopwatch.stop();

        print('⏱️ وقت البحث في العملاء: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(300));
      });
    });

    group('اختبارات الخدمات', () {
      test('اختبار سرعة خدمة المصادقة', () async {
        stopwatch.start();
        final users = await AuthService.getAllUsers();
        stopwatch.stop();

        print('⏱️ وقت جلب المستخدمين: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(300));
      });

      test('اختبار سرعة خدمة الأداء', () async {
        stopwatch.start();
        final isFirstLaunch = await PerformanceService.isFirstLaunch();
        final needsBackup = await PerformanceService.needsBackupRestore();
        stopwatch.stop();

        print('⏱️ وقت فحص الأداء: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(200));
      });

      test('اختبار سرعة خدمة التخزين', () async {
        stopwatch.start();
        final hasPermissions = await StorageService.hasStoragePermissions();
        stopwatch.stop();

        print('⏱️ وقت فحص أذونات التخزين: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });

    group('اختبارات المزامنة والنسخ الاحتياطي', () {
      test('اختبار سرعة خدمة المزامنة التلقائية', () async {
        stopwatch.start();
        final isEnabled = await AutoSyncService.isAutoSyncEnabled();
        stopwatch.stop();

        print(
          '⏱️ وقت فحص المزامنة التلقائية: ${stopwatch.elapsedMilliseconds}ms',
        );
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('اختبار سرعة خدمة النسخ الاحتياطي التلقائي', () async {
        stopwatch.start();
        final isEnabled = await AutoBackupService.isAutoBackupEnabled();
        stopwatch.stop();

        print(
          '⏱️ وقت فحص النسخ الاحتياطي التلقائي: ${stopwatch.elapsedMilliseconds}ms',
        );
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('اختبار سرعة خدمة النسخ الاحتياطي المحلي', () async {
        stopwatch.start();
        final backups = await LocalBackupService.getLocalBackups();
        stopwatch.stop();

        print(
          '⏱️ وقت جلب النسخ الاحتياطية المحلية: ${stopwatch.elapsedMilliseconds}ms',
        );
        print('📊 عدد النسخ الاحتياطية: ${backups.length}');
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });
    });

    group('اختبارات الرسائل والمشاركة', () {
      test('اختبار سرعة خدمة الرسائل', () async {
        stopwatch.start();
        final messages = await MessagingService.getMessages();
        stopwatch.stop();

        print('⏱️ وقت جلب الرسائل: ${stopwatch.elapsedMilliseconds}ms');
        print('📊 عدد الرسائل: ${messages.length}');
        expect(stopwatch.elapsedMilliseconds, lessThan(300));
      });

      test('اختبار سرعة خدمة مشاركة الفواتير', () async {
        stopwatch.start();
        final canShare = await InvoiceSharingService.canShareInvoice();
        stopwatch.stop();

        print(
          '⏱️ وقت فحص إمكانية المشاركة: ${stopwatch.elapsedMilliseconds}ms',
        );
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('اختبار سرعة خدمة رسائل الفواتير', () async {
        stopwatch.start();
        final messages = await InvoiceMessageService.getInvoiceMessages();
        stopwatch.stop();

        print('⏱️ وقت جلب رسائل الفواتير: ${stopwatch.elapsedMilliseconds}ms');
        print('📊 عدد رسائل الفواتير: ${messages.length}');
        expect(stopwatch.elapsedMilliseconds, lessThan(300));
      });
    });

    group('اختبارات الأذونات والصلاحيات', () {
      test('اختبار سرعة خدمة الأذونات', () async {
        stopwatch.start();
        final permissions = await PermissionsService.getPermissions();
        stopwatch.stop();

        print('⏱️ وقت جلب الأذونات: ${stopwatch.elapsedMilliseconds}ms');
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });

    group('اختبارات الأداء الشاملة', () {
      test('اختبار الأداء الشامل للتطبيق', () async {
        final results = <String, int>{};

        // اختبار قاعدة البيانات
        stopwatch.start();
        await databaseHelper.getCustomers();
        await databaseHelper.getInvoices();
        await databaseHelper.getCollections();
        stopwatch.stop();
        results['database'] = stopwatch.elapsedMilliseconds;

        // اختبار الخدمات
        stopwatch.reset();
        stopwatch.start();
        await AuthService.getAllUsers();
        await PerformanceService.isFirstLaunch();
        await StorageService.hasStoragePermissions();
        stopwatch.stop();
        results['services'] = stopwatch.elapsedMilliseconds;

        // اختبار المزامنة
        stopwatch.reset();
        stopwatch.start();
        await AutoSyncService.isAutoSyncEnabled();
        await AutoBackupService.isAutoBackupEnabled();
        await LocalBackupService.getLocalBackups();
        stopwatch.stop();
        results['sync'] = stopwatch.elapsedMilliseconds;

        // اختبار الرسائل
        stopwatch.reset();
        stopwatch.start();
        await MessagingService.getMessages();
        await InvoiceMessageService.getInvoiceMessages();
        stopwatch.stop();
        results['messaging'] = stopwatch.elapsedMilliseconds;

        print('📊 نتائج اختبار الأداء الشامل:');
        print('   قاعدة البيانات: ${results['database']}ms');
        print('   الخدمات: ${results['services']}ms');
        print('   المزامنة: ${results['sync']}ms');
        print('   الرسائل: ${results['messaging']}ms');

        final totalTime = results.values.reduce((a, b) => a + b);
        print('   المجموع: ${totalTime}ms');

        expect(totalTime, lessThan(2000));
      });
    });

    group('اختبارات اكتشاف المشاكل', () {
      test('فحص وجود مشاكل في قاعدة البيانات', () async {
        try {
          final db = await databaseHelper.database;

          // فحص الجداول
          final tables = await db.query(
            'sqlite_master',
            where: 'type = ?',
            whereArgs: ['table'],
          );
          print('📋 الجداول الموجودة: ${tables.length}');

          // فحص حجم قاعدة البيانات
          final dbFile = File(await databaseHelper.getDatabasePath());
          if (await dbFile.exists()) {
            final size = await dbFile.length();
            print(
              '💾 حجم قاعدة البيانات: ${(size / 1024 / 1024).toStringAsFixed(2)} MB',
            );

            if (size > 50 * 1024 * 1024) {
              // أكثر من 50 ميجابايت
              print('⚠️ تحذير: قاعدة البيانات كبيرة جداً');
            }
          }

          expect(tables.length, greaterThan(0));
        } catch (e) {
          print('❌ خطأ في فحص قاعدة البيانات: $e');
          fail('فشل في فحص قاعدة البيانات');
        }
      });

      test('فحص وجود مشاكل في الخدمات', () async {
        final services = [
          AuthService,
          PerformanceService,
          StorageService,
          AutoSyncService,
          AutoBackupService,
          MessagingService,
          InvoiceSharingService,
          InvoiceMessageService,
          PermissionsService,
        ];

        for (final service in services) {
          try {
            print('🔍 فحص خدمة: ${service.toString()}');
            // يمكن إضافة فحوصات محددة لكل خدمة هنا
          } catch (e) {
            print('❌ مشكلة في خدمة ${service.toString()}: $e');
          }
        }
      });

      test('فحص استهلاك الذاكرة', () async {
        // محاكاة استهلاك الذاكرة
        final List<String> testData = [];
        for (int i = 0; i < 1000; i++) {
          testData.add('بيانات اختبار $i');
        }

        print('🧠 تم إنشاء بيانات اختبار للذاكرة');
        expect(testData.length, equals(1000));
      });
    });
  });
}
