#!/usr/bin/env python3
"""
سكريبت لإنشاء أيقونة Atlas Medical Supplies الاحترافية
يحتاج إلى تثبيت: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_atlas_icon():
    # إنشاء صورة جديدة 1024x1024
    size = 1024
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # الألوان
    colors = {
        'primary': (64, 224, 208),      # #40E0D0
        'secondary': (32, 178, 170),    # #20B2AA
        'dark': (0, 139, 139),          # #008B8B
        'white': (255, 255, 255),       # #FFFFFF
        'shadow': (0, 0, 0, 76)         # #000000 with 30% opacity
    }
    
    # إنشاء خلفية دائرية بتدرج لوني
    center = size // 2
    radius = 480
    
    # رسم الدائرة الرئيسية
    for r in range(radius, 0, -1):
        # حساب اللون بناءً على المسافة من المركز
        ratio = r / radius
        if ratio > 0.7:
            color = colors['primary']
        elif ratio > 0.4:
            color = colors['secondary']
        else:
            color = colors['dark']
        
        # رسم دائرة بسمك 1 بكسل
        bbox = (center - r, center - r, center + r, center + r)
        draw.ellipse(bbox, fill=color)
    
    # إضافة ظل خفيف
    shadow_offset = 8
    shadow_bbox = (center - radius + shadow_offset, center - radius + shadow_offset,
                   center + radius + shadow_offset, center + radius + shadow_offset)
    draw.ellipse(shadow_bbox, fill=colors['shadow'])
    
    # رسم الصليب الطبي
    cross_width = 16
    cross_height = 240
    
    # الخط العمودي
    vertical_bbox = (center - cross_width//2, center - cross_height//2,
                     center + cross_width//2, center + cross_height//2)
    draw.rounded_rectangle(vertical_bbox, radius=8, fill=colors['white'])
    
    # الخط الأفقي
    horizontal_bbox = (center - cross_height//2, center - cross_width//2,
                       center + cross_height//2, center + cross_width//2)
    draw.rounded_rectangle(horizontal_bbox, radius=8, fill=colors['white'])
    
    # إضافة النص "ATLAS"
    try:
        # محاولة استخدام خط Arial
        font_large = ImageFont.truetype("arial.ttf", 120)
    except:
        # استخدام الخط الافتراضي إذا لم يتوفر Arial
        font_large = ImageFont.load_default()
    
    atlas_text = "ATLAS"
    atlas_bbox = draw.textbbox((0, 0), atlas_text, font=font_large)
    atlas_width = atlas_bbox[2] - atlas_bbox[0]
    atlas_x = center - atlas_width // 2
    atlas_y = center + 80
    
    # إضافة ظل للنص
    draw.text((atlas_x + 4, atlas_y + 4), atlas_text, font=font_large, fill=colors['shadow'])
    draw.text((atlas_x, atlas_y), atlas_text, font=font_large, fill=colors['white'])
    
    # إضافة النص "MEDICAL"
    try:
        font_small = ImageFont.truetype("arial.ttf", 48)
    except:
        font_small = ImageFont.load_default()
    
    medical_text = "MEDICAL"
    medical_bbox = draw.textbbox((0, 0), medical_text, font=font_small)
    medical_width = medical_bbox[2] - medical_bbox[0]
    medical_x = center - medical_width // 2
    medical_y = center + 200
    
    # إضافة ظل للنص
    draw.text((medical_x + 2, medical_y + 2), medical_text, font=font_small, fill=colors['shadow'])
    draw.text((medical_x, medical_y), medical_text, font=font_small, fill=colors['white'])
    
    # إضافة العناصر الزخرفية (دوائر صغيرة في الزوايا)
    decorative_radius = 20
    decorative_positions = [
        (200, 200),
        (824, 200),
        (200, 824),
        (824, 824)
    ]
    
    for pos in decorative_positions:
        bbox = (pos[0] - decorative_radius, pos[1] - decorative_radius,
                pos[0] + decorative_radius, pos[1] + decorative_radius)
        draw.ellipse(bbox, fill=colors['white'] + (76,))  # 30% opacity
    
    return img

def create_adaptive_icon():
    """إنشاء أيقونة تكيفية للأندرويد (بدون خلفية)"""
    size = 1024
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    
    # رسم الصليب الطبي فقط (بدون خلفية)
    cross_width = 16
    cross_height = 240
    
    # الخط العمودي
    vertical_bbox = (center - cross_width//2, center - cross_height//2,
                     center + cross_width//2, center + cross_height//2)
    draw.rounded_rectangle(vertical_bbox, radius=8, fill=(255, 255, 255, 255))
    
    # الخط الأفقي
    horizontal_bbox = (center - cross_height//2, center - cross_width//2,
                       center + cross_height//2, center + cross_width//2)
    draw.rounded_rectangle(horizontal_bbox, radius=8, fill=(255, 255, 255, 255))
    
    # إضافة النص "ATLAS" فقط
    try:
        font_large = ImageFont.truetype("arial.ttf", 120)
    except:
        font_large = ImageFont.load_default()
    
    atlas_text = "ATLAS"
    atlas_bbox = draw.textbbox((0, 0), atlas_text, font=font_large)
    atlas_width = atlas_bbox[2] - atlas_bbox[0]
    atlas_x = center - atlas_width // 2
    atlas_y = center + 80
    
    draw.text((atlas_x, atlas_y), atlas_text, font=font_large, fill=(255, 255, 255, 255))
    
    return img

def main():
    print("🎨 إنشاء أيقونة Atlas Medical Supplies...")
    
    # إنشاء مجلد assets/images إذا لم يكن موجوداً
    assets_dir = "assets/images"
    os.makedirs(assets_dir, exist_ok=True)
    
    # إنشاء الأيقونة الرئيسية
    icon = create_atlas_icon()
    icon_path = os.path.join(assets_dir, "atlas_icon.png")
    icon.save(icon_path, "PNG")
    print(f"✅ تم إنشاء الأيقونة الرئيسية: {icon_path}")
    
    # إنشاء الأيقونة التكيفية
    adaptive_icon = create_adaptive_icon()
    adaptive_icon_path = os.path.join(assets_dir, "atlas_icon_adaptive.png")
    adaptive_icon.save(adaptive_icon_path, "PNG")
    print(f"✅ تم إنشاء الأيقونة التكيفية: {adaptive_icon_path}")
    
    print("🎉 تم إنشاء جميع الأيقونات بنجاح!")
    print("📱 يمكنك الآن تشغيل: flutter pub get && flutter pub run flutter_launcher_icons")

if __name__ == "__main__":
    main() 