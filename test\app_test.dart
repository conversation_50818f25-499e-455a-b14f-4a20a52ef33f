import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/main.dart';
import 'package:atlas_medical_supplies/screens/login_screen.dart';
import 'package:atlas_medical_supplies/services/auth_service.dart';
import 'package:atlas_medical_supplies/database/database_helper.dart';

void main() {
  group('أطلس للمستلزمات الطبية - اختبارات التطبيق', () {
    testWidgets('اختبار شاشة البداية', (WidgetTester tester) async {
      // بناء التطبيق
      await tester.pumpWidget(const AtlasMedicalApp());

      // التحقق من وجود شعار التطبيق
      expect(find.text('أطلس للمستلزمات الطبية'), findsOneWidget);
      expect(find.text('نظام إدارة العملاء والفواتير'), findsOneWidget);
      expect(find.byIcon(Icons.medical_services), findsOneWidget);
    });

    testWidgets('اختبار شاشة تسجيل الدخول', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      // التحقق من وجود حقول الإدخال
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('رقم الهاتف'), findsOneWidget);
      expect(find.text('كلمة المرور'), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('اختبار قاعدة البيانات', (WidgetTester tester) async {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      // اختبار إنشاء الجداول
      expect(db.isOpen, isTrue);

      // اختبار إدراج مستخدم
      final userId = await dbHelper.insertUser({
        'name': 'مستخدم تجريبي',
        'email': '<EMAIL>',
        'password': '123456',
        'role': 'بائع',
      });
      expect(userId, isNotNull);

      // اختبار البحث عن المستخدم
      final user = await dbHelper.getUserByEmail('<EMAIL>');
      expect(user, isNotNull);
      expect(user!['name'], 'مستخدم تجريبي');

      await db.close();
    });

    testWidgets('اختبار خدمة المصادقة', (WidgetTester tester) async {
      // اختبار تسجيل الدخول
      final result = await AuthService.login('01125312343', '123456');
      expect(result, isNotNull);
      expect(result!['success'], isTrue);

      // اختبار التحقق من حالة تسجيل الدخول
      final isLoggedIn = await AuthService.isLoggedIn();
      expect(isLoggedIn, isTrue);

      // اختبار الحصول على بيانات المستخدم
      final currentUser = await AuthService.getCurrentUser();
      expect(currentUser, isNotNull);
      expect(currentUser!['email'], '<EMAIL>');

      // اختبار تسجيل الخروج
      await AuthService.logout();
      final isLoggedOut = await AuthService.isLoggedIn();
      expect(isLoggedOut, isFalse);
    });

    testWidgets('اختبار إدارة العملاء', (WidgetTester tester) async {
      final dbHelper = DatabaseHelper();

      // إدراج عميل تجريبي
      final customerId = await dbHelper.insertCustomer({
        'name': 'عميل تجريبي',
        'phone': '01234567890',
        'address': 'عنوان تجريبي',
        'governorate': 'القاهرة',
        'area': 'المعادي',
      });
      expect(customerId, isNotNull);

      // البحث عن العميل
      final customer = await dbHelper.getCustomer(customerId);
      expect(customer, isNotNull);
      expect(customer!['name'], 'عميل تجريبي');

      // تحديث بيانات العميل
      await dbHelper.updateCustomer(customerId, {'phone': '09876543210'});
      final updatedCustomer = await dbHelper.getCustomer(customerId);
      expect(updatedCustomer!['phone'], '09876543210');

      // حذف العميل
      await dbHelper.deleteCustomer(customerId);
      final deletedCustomer = await dbHelper.getCustomer(customerId);
      expect(deletedCustomer, isNull);
    });

    testWidgets('اختبار إدارة الفواتير', (WidgetTester tester) async {
      final dbHelper = DatabaseHelper();

      // إنشاء عميل للاختبار
      final customerId = await dbHelper.insertCustomer({
        'name': 'عميل فواتير',
        'phone': '01234567890',
      });

      // إنشاء فاتورة
      final invoiceId = await dbHelper.insertInvoice({
        'customer_id': customerId,
        'invoice_number': 'INV-001',
        'date': DateTime.now().toIso8601String(),
        'total_amount': 1000.0,
        'paid_amount': 0.0,
        'remaining_amount': 1000.0,
        'created_by': 1,
      });
      expect(invoiceId, isNotNull);

      // البحث عن الفاتورة
      final invoices = await dbHelper.getInvoices();
      final invoice = invoices.firstWhere((inv) => inv['id'] == invoiceId);
      expect(invoice, isNotNull);
      expect(invoice['total_amount'], 1000.0);

      // تحديث الفاتورة
      await dbHelper.updateInvoice(invoiceId, {
        'paid_amount': 500.0,
        'remaining_amount': 500.0,
      });
      final updatedInvoices = await dbHelper.getInvoices();
      final updatedInvoice = updatedInvoices.firstWhere(
        (inv) => inv['id'] == invoiceId,
      );
      expect(updatedInvoice['remaining_amount'], 500.0);

      // حذف الفاتورة
      await dbHelper.deleteInvoice(invoiceId);
      final remainingInvoices = await dbHelper.getInvoices();
      final deletedInvoice = remainingInvoices.where(
        (inv) => inv['id'] == invoiceId,
      );
      expect(deletedInvoice.isEmpty, isTrue);

      // تنظيف البيانات
      await dbHelper.deleteCustomer(customerId);
    });

    testWidgets('اختبار إدارة التحصيل', (WidgetTester tester) async {
      final dbHelper = DatabaseHelper();

      // إنشاء عميل وفاتورة للاختبار
      final customerId = await dbHelper.insertCustomer({
        'name': 'عميل تحصيل',
        'phone': '01234567890',
      });

      final invoiceId = await dbHelper.insertInvoice({
        'customer_id': customerId,
        'invoice_number': 'INV-002',
        'date': DateTime.now().toIso8601String(),
        'total_amount': 2000.0,
        'paid_amount': 0.0,
        'remaining_amount': 2000.0,
        'created_by': 1,
      });

      // تسجيل تحصيل
      final collectionId = await dbHelper.insertCollection({
        'customer_id': customerId,
        'invoice_id': invoiceId,
        'amount': 1000.0,
        'date': DateTime.now().toIso8601String(),
        'collector_name': 'محصل تجريبي',
        'notes': 'تحصيل تجريبي',
      });
      expect(collectionId, isNotNull);

      // البحث عن التحصيل
      final collections = await dbHelper.getCollections();
      final collection = collections.firstWhere(
        (col) => col['id'] == collectionId,
      );
      expect(collection, isNotNull);
      expect(collection['amount'], 1000.0);

      // تحديث التحصيل (لا توجد دالة updateCollection، سنتخطى هذا الاختبار)
      // await dbHelper.updateCollection(collectionId, {
      //   'amount': 1500.0,
      //   'notes': 'تحصيل محدث',
      // });

      // حذف التحصيل (لا توجد دالة deleteCollection، سنتخطى هذا الاختبار)
      // await dbHelper.deleteCollection(collectionId);

      // تنظيف البيانات
      await dbHelper.deleteInvoice(invoiceId);
      await dbHelper.deleteCustomer(customerId);
    });

    testWidgets('اختبار الإحصائيات', (WidgetTester tester) async {
      final dbHelper = DatabaseHelper();

      // الحصول على إحصائيات لوحة التحكم
      final stats = await dbHelper.getDashboardStats();
      expect(stats, isNotNull);
      expect(stats.containsKey('customers_count'), isTrue);
      expect(stats.containsKey('invoices_count'), isTrue);
      expect(stats.containsKey('total_sales'), isTrue);
      expect(stats.containsKey('total_collections'), isTrue);

      // الحصول على إحصائيات المستخدمين
      final usersStats = await dbHelper.getUsersStats();
      expect(usersStats, isNotNull);
      expect(usersStats, isA<List>());
    });
  });
}
