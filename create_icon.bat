@echo off
echo 🎨 إنشاء أيقونة Atlas Medical Supplies الاحترافية...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود Pillow
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت مكتبة Pillow...
    pip install Pillow
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Pillow
        pause
        exit /b 1
    )
)

echo 🎨 تشغيل سكريبت إنشاء الأيقونة...
python create_atlas_icon.py

if errorlevel 1 (
    echo ❌ فشل في إنشاء الأيقونة
    pause
    exit /b 1
)

echo.
echo 📱 إنشاء أيقونات التطبيق...
flutter pub get
flutter pub run flutter_launcher_icons

if errorlevel 1 (
    echo ❌ فشل في إنشاء أيقونات التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم إنشاء أيقونة التطبيق بنجاح!
echo 🎉 يمكنك الآن بناء التطبيق وستظهر الأيقونة الجديدة
pause 