@echo off
chcp 65001 >nul
title إنشاء أيقونات التشغيل - أطلس للمستلزمات الطبية

echo.
echo ========================================
echo    إنشاء أيقونات التشغيل
echo    أطلس للمستلزمات الطبية
echo ========================================
echo.

echo جاري إنشاء أيقونات التشغيل...
echo.

REM إنشاء اختصار التشغيل التفاعلي
echo [InternetShortcut] > "🚀 تشغيل التطبيق.url"
echo URL=file:///%CD%/run_app.bat >> "🚀 تشغيل التطبيق.url"
echo IconFile=%CD%/android/app/src/main/res/mipmap-hdpi/ic_launcher.png >> "🚀 تشغيل التطبيق.url"
echo IconIndex=0 >> "🚀 تشغيل التطبيق.url"

REM إنشاء اختصار التشغيل السريع
echo [InternetShortcut] > "⚡ تشغيل سريع.url"
echo URL=file:///%CD%/START.bat >> "⚡ تشغيل سريع.url"
echo IconFile=%CD%/android/app/src/main/res/mipmap-hdpi/ic_launcher.png >> "⚡ تشغيل سريع.url"
echo IconIndex=0 >> "⚡ تشغيل سريع.url"

echo ✅ تم إنشاء أيقونات التشغيل بنجاح!
echo.
echo 📁 الأيقونات المنشأة:
echo    • 🚀 تشغيل التطبيق.url
echo    • ⚡ تشغيل سريع.url
echo.
echo 💡 يمكنك الآن النقر على أي من الأيقونات لتشغيل التطبيق
echo.

pause 