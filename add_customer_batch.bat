@echo off
chcp 65001 >nul
echo ========================================
echo    إضافة عميل شركة النيل
echo ========================================
echo.

echo 🚀 بدء عملية إضافة العميل...
echo.

REM تحديد مسار قاعدة البيانات
set DB_PATH=%LOCALAPPDATA%\com.example.atlas_medical_supplies\databases\atlas_medical.db

REM التحقق من وجود قاعدة البيانات
if not exist "%DB_PATH%" (
    echo ❌ قاعدة البيانات غير موجودة في:
    echo    %DB_PATH%
    echo.
    echo يرجى تشغيل التطبيق أولاً لإنشاء قاعدة البيانات
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على قاعدة البيانات
echo.

REM بيانات العميل
set CUSTOMER_NAME=شركة النيل
set CUSTOMER_PHONE=01110473536
set CUSTOMER_ADDRESS=أبو حمص
set CUSTOMER_GOVERNORATE=البحيرة
set CUSTOMER_AREA=أبو حمص
set CUSTOMER_NOTES=تم الإضافة بتاريخ %date%

echo 📋 بيانات العميل:
echo    الاسم: %CUSTOMER_NAME%
echo    الهاتف: %CUSTOMER_PHONE%
echo    المحافظة: %CUSTOMER_GOVERNORATE%
echo    المنطقة: %CUSTOMER_AREA%
echo    العنوان: %CUSTOMER_ADDRESS%
echo.

REM التحقق من وجود SQLite
sqlite3 --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  SQLite غير مثبت أو غير متاح في PATH
    echo.
    echo 💡 يمكنك إضافة العميل من خلال التطبيق مباشرة
    echo    أو تثبيت SQLite من الموقع الرسمي
    echo.
    pause
    exit /b 1
)

echo 🔍 التحقق من وجود العميل مسبقاً...
sqlite3 "%DB_PATH%" "SELECT id, name FROM customers WHERE phone = '%CUSTOMER_PHONE%';"

if %errorlevel% equ 0 (
    echo.
    echo ⚠️  تحذير: العميل موجود بالفعل في قاعدة البيانات
    echo.
) else (
    echo.
    echo ➕ إضافة العميل الجديد...
    
    REM إضافة العميل
    sqlite3 "%DB_PATH%" "INSERT INTO customers (name, phone, address, governorate, area, notes, created_at) VALUES ('%CUSTOMER_NAME%', '%CUSTOMER_PHONE%', '%CUSTOMER_ADDRESS%', '%CUSTOMER_GOVERNORATE%', '%CUSTOMER_AREA%', '%CUSTOMER_NOTES%', datetime('now'));"
    
    if %errorlevel% equ 0 (
        echo ✅ تم إضافة العميل بنجاح!
        echo.
        echo 📊 تفاصيل العميل المضاف:
        sqlite3 "%DB_PATH%" "SELECT id, name, phone, governorate, area, created_at FROM customers WHERE phone = '%CUSTOMER_PHONE%';"
        echo.
        echo 🎉 تم إضافة عميل شركة النيل بنجاح!
    ) else (
        echo ❌ فشل في إضافة العميل
    )
)

echo.
echo 💡 ملاحظة: يمكنك أيضاً إضافة العميل من خلال التطبيق مباشرة
echo.
pause 