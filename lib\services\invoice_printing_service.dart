import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../database/database_helper.dart';

class InvoicePrintingService {
  /// طباعة الفاتورة كملف PDF
  static Future<String?> printInvoiceAsPDF(
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    try {
      // طلب صلاحيات الكتابة
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('مطلوب صلاحية الكتابة للطباعة');
      }

      // جلب تفاصيل التحصيلات
      List<Map<String, dynamic>> collections = [];
      if (invoice['id'] != null) {
        final dbHelper = DatabaseHelper();
        collections = await dbHelper.getCollectionsByInvoice(invoice['id']);
      }

      // إنشاء PDF
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Container(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // رأس الفاتورة
                  _buildHeader(),
                  pw.SizedBox(height: 20),

                  // معلومات العميل
                  _buildCustomerInfo(customer),
                  pw.SizedBox(height: 20),

                  // تفاصيل الفاتورة
                  _buildInvoiceDetails(invoice),
                  pw.SizedBox(height: 20),

                  // تفاصيل التحصيلات
                  if (collections.isNotEmpty) ...[
                    _buildCollectionsTable(collections),
                    pw.SizedBox(height: 20),
                  ],

                  // ملخص المبالغ
                  _buildAmountSummary(invoice),
                  pw.SizedBox(height: 30),

                  // التوقيع
                  _buildSignature(invoice),
                ],
              ),
            );
          },
        ),
      );

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'invoice_${invoice['invoice_number']}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return file.path;
    } catch (e) {
      print('خطأ في طباعة الفاتورة: $e');
      return null;
    }
  }

  /// بناء رأس الفاتورة
  static pw.Widget _buildHeader() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.blue, width: 2),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'InvoFast',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                'كشف حساب فاتورة',
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text('فاتورة رقم:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                'تاريخ الطباعة: ${DateTime.now().toString().substring(0, 10)}',
                style: pw.TextStyle(fontSize: 12, color: PdfColors.grey),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومات العميل
  static pw.Widget _buildCustomerInfo(Map<String, dynamic> customer) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات العميل',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Text(
                'الاسم: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(customer['name'] ?? 'غير محدد'),
            ],
          ),
          if (customer['primary_phone'] != null) ...[
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text(
                  'الهاتف: ',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
                pw.Text(customer['primary_phone']),
              ],
            ),
          ],
          if (customer['address'] != null) ...[
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Text(
                  'العنوان: ',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
                pw.Text(customer['address']),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء تفاصيل الفاتورة
  static pw.Widget _buildInvoiceDetails(Map<String, dynamic> invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'تفاصيل الفاتورة',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 15),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.grey100),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'رقم الفاتورة',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(invoice['invoice_number'] ?? ''),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'التاريخ',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(invoice['date'] ?? ''),
                  ),
                ],
              ),
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.grey100),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'المبلغ الإجمالي',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '${invoice['total_amount']?.toStringAsFixed(2)} ج.م',
                    ),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'المبلغ المدفوع',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '${invoice['paid_amount']?.toStringAsFixed(2)} ج.م',
                    ),
                  ),
                ],
              ),
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.grey100),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'المبلغ المتبقي',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '${invoice['remaining_amount']?.toStringAsFixed(2)} ج.م',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء جدول التحصيلات
  static pw.Widget _buildCollectionsTable(
    List<Map<String, dynamic>> collections,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'تفاصيل التحصيلات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 15),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // رأس الجدول
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.grey100),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'التاريخ',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'المبلغ',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'المحصل',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      'طريقة الدفع',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ),
                ],
              ),
              // بيانات التحصيلات
              ...collections
                  .map(
                    (collection) => pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(collection['date'] ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${collection['amount']?.toStringAsFixed(2)} ج.م',
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(collection['collector_name'] ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(collection['payment_method'] ?? ''),
                        ),
                      ],
                    ),
                  )
                  .toList(),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء ملخص المبالغ
  static pw.Widget _buildAmountSummary(Map<String, dynamic> invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'إجمالي المدفوع:',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.Text(
            '${invoice['paid_amount']?.toStringAsFixed(2)} ج.م',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التوقيع
  static pw.Widget _buildSignature(Map<String, dynamic> invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'المحصل:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(invoice['created_by_name'] ?? 'غير محدد'),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'شكراً لتعاملكم معنا',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.Text('للاستفسار: 01125312343'),
            ],
          ),
        ],
      ),
    );
  }

  /// عرض خيارات الطباعة
  static Future<void> showPrintOptions(
    BuildContext context,
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.print, color: Color(0xFF4A90E2)),
            const SizedBox(width: 8),
            Text('طباعة الفاتورة ${invoice['invoice_number']}'),
          ],
        ),
        content: const Text('اختر طريقة الطباعة:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              await _generateAndSharePDF(context, invoice, customer);
            },
            icon: const Icon(Icons.picture_as_pdf),
            label: const Text('إنشاء PDF'),
          ),
        ],
      ),
    );
  }

  /// إنشاء ومشاركة PDF
  static Future<void> _generateAndSharePDF(
    BuildContext context,
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء الفاتورة...'),
            ],
          ),
        ),
      );

      final filePath = await printInvoiceAsPDF(invoice, customer);

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      if (filePath != null) {
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء الفاتورة بنجاح: $filePath'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'فتح',
              onPressed: () {
                // هنا يمكن إضافة كود لفتح الملف
              },
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في إنشاء الفاتورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
      );
    }
  }
}
