import 'package:flutter/material.dart';
import '../models/customer_phone.dart';
import '../database/database_helper.dart';

class CustomerPhonesWidget extends StatefulWidget {
  final int customerId;
  final String customerName;

  const CustomerPhonesWidget({
    Key? key,
    required this.customerId,
    required this.customerName,
  }) : super(key: key);

  @override
  State<CustomerPhonesWidget> createState() => _CustomerPhonesWidgetState();
}

class _CustomerPhonesWidgetState extends State<CustomerPhonesWidget> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<CustomerPhone> _phones = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPhones();
  }

  Future<void> _loadPhones() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final phonesData = await _dbHelper.getCustomerPhones(widget.customerId);
      setState(() {
        _phones = phonesData
            .map((data) => CustomerPhone.fromMap(data))
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في تحميل أرقام الهواتف: $e')));
    }
  }

  Future<void> _addPhone() async {
    final phoneController = TextEditingController();
    final typeController = TextEditingController(text: 'الرئيسي');
    final notesController = TextEditingController();
    bool isPrimary = false;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.phone, color: Color(0xFF4A90E2)),
              const SizedBox(width: 8),
              const Text('إضافة رقم هاتف جديد'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف *',
                    hintText: 'مثال: 01110473536',
                    prefixIcon: Icon(Icons.phone, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  onChanged: (value) {
                    // تحسين تنسيق الرقم تلقائياً
                    if (value.length == 11 && value.startsWith('01')) {
                      // تنسيق رقم مصري
                      final formatted =
                          '${value.substring(0, 3)}-${value.substring(3, 7)}-${value.substring(7)}';
                      phoneController.text = formatted;
                      phoneController.selection = TextSelection.fromPosition(
                        TextPosition(offset: formatted.length),
                      );
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: typeController,
                  decoration: const InputDecoration(
                    labelText: 'نوع الهاتف',
                    hintText: 'مثال: الرئيسي، العمل، المنزل',
                    prefixIcon: Icon(Icons.category, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    hintText: 'ملاحظات إضافية (اختياري)',
                    prefixIcon: Icon(Icons.note, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber[300]!),
                  ),
                  child: CheckboxListTile(
                    title: const Text(
                      'رقم الهاتف الأساسي',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: const Text(
                      'سيتم استخدام هذا الرقم كرقم رئيسي للعميل',
                      style: TextStyle(fontSize: 12),
                    ),
                    value: isPrimary,
                    onChanged: (value) {
                      setDialogState(() {
                        isPrimary = value ?? false;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final phoneNumber = phoneController.text.trim();
                if (phoneNumber.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال رقم الهاتف'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // التحقق من صحة تنسيق الرقم
                final cleanNumber = phoneNumber.replaceAll(
                  RegExp(r'[^\d]'),
                  '',
                );
                if (cleanNumber.length < 10 || cleanNumber.length > 11) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال رقم هاتف صحيح (10-11 رقم)'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                try {
                  final phoneData = {
                    'customer_id': widget.customerId,
                    'phone': cleanNumber,
                    'phone_type': typeController.text.trim(),
                    'is_primary': isPrimary ? 1 : 0,
                    'notes': notesController.text.trim().isEmpty
                        ? null
                        : notesController.text.trim(),
                  };

                  await _dbHelper.insertCustomerPhone(phoneData);

                  if (isPrimary) {
                    // البحث عن الرقم المضاف وتعيينه كأساسي
                    final phones = await _dbHelper.getCustomerPhones(
                      widget.customerId,
                    );
                    final newPhone = phones.last;
                    await _dbHelper.setPrimaryPhone(
                      widget.customerId,
                      newPhone['id'],
                    );
                  }

                  Navigator.of(context).pop();
                  _loadPhones();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isPrimary
                            ? 'تم إضافة رقم الهاتف الأساسي بنجاح'
                            : 'تم إضافة رقم الهاتف بنجاح',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة رقم الهاتف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A90E2),
                foregroundColor: Colors.white,
              ),
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _editPhone(CustomerPhone phone) async {
    final phoneController = TextEditingController(text: phone.phone);
    final typeController = TextEditingController(text: phone.phoneType);
    final notesController = TextEditingController(text: phone.notes ?? '');

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل رقم الهاتف'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: phoneController,
                decoration: const InputDecoration(labelText: 'رقم الهاتف'),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: typeController,
                decoration: const InputDecoration(labelText: 'نوع الهاتف'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: notesController,
                decoration: const InputDecoration(labelText: 'ملاحظات'),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (phoneController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('يرجى إدخال رقم الهاتف')),
                );
                return;
              }

              try {
                final phoneData = {
                  'phone': phoneController.text.trim(),
                  'phone_type': typeController.text.trim(),
                  'notes': notesController.text.trim().isEmpty
                      ? null
                      : notesController.text.trim(),
                };

                await _dbHelper.updateCustomerPhone(phone.id!, phoneData);
                Navigator.of(context).pop();
                _loadPhones();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم تحديث رقم الهاتف بنجاح')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('خطأ في تحديث رقم الهاتف: $e')),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  Future<void> _deletePhone(CustomerPhone phone) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف رقم الهاتف ${phone.phone}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteCustomerPhone(phone.id!);
        _loadPhones();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف رقم الهاتف بنجاح')),
        );
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حذف رقم الهاتف: $e')));
      }
    }
  }

  Future<void> _setPrimaryPhone(CustomerPhone phone) async {
    try {
      await _dbHelper.setPrimaryPhone(widget.customerId, phone.id!);
      _loadPhones();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تعيين رقم الهاتف كأساسي')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تعيين رقم الهاتف الأساسي: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('أرقام هواتف ${widget.customerName}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addPhone,
            tooltip: 'إضافة رقم هاتف جديد',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _phones.isEmpty
          ? Container(
              padding: const EdgeInsets.all(32),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.phone_disabled, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد أرقام هواتف',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'اضغط على + لإضافة رقم هاتف جديد',
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          : ListView.builder(
              itemCount: _phones.length,
              itemBuilder: (context, index) {
                final phone = _phones[index];
                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: phone.isPrimary ? Colors.amber[50] : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: phone.isPrimary
                          ? Colors.amber[300]!
                          : Colors.grey[300]!,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: phone.isPrimary ? Colors.amber : Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        phone.isPrimary ? Icons.star : Icons.phone,
                        color: phone.isPrimary ? Colors.white : Colors.blue,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      phone.phone,
                      style: TextStyle(
                        fontWeight: phone.isPrimary
                            ? FontWeight.bold
                            : FontWeight.normal,
                        fontSize: 16,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'النوع: ${phone.phoneType}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        if (phone.notes != null && phone.notes!.isNotEmpty)
                          Text(
                            'ملاحظات: ${phone.notes}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        if (phone.isPrimary)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'الرقم الأساسي',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            _editPhone(phone);
                            break;
                          case 'primary':
                            if (!phone.isPrimary) {
                              _setPrimaryPhone(phone);
                            }
                            break;
                          case 'delete':
                            _deletePhone(phone);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        if (!phone.isPrimary)
                          const PopupMenuItem(
                            value: 'primary',
                            child: Row(
                              children: [
                                Icon(Icons.star),
                                SizedBox(width: 8),
                                Text('تعيين كأساسي'),
                              ],
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
