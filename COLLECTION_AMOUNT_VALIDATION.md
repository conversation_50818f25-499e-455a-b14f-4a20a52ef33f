# 💰 ميزة التحقق من مبلغ التحصيل

## 📋 نظرة عامة

تم إضافة ميزة التحقق من مبلغ التحصيل لضمان عدم تجاوز المبلغ المحصل لقيمة الفاتورة المتبقية. هذه الميزة تمنع الأخطاء المحاسبية وتضمن دقة البيانات.

## 🚀 الميزات المضافة

### 1. **التحقق التلقائي من المبلغ**
- التحقق من المبلغ المدخل عند الإدخال
- التحقق من المبلغ عند تغيير الفاتورة المختارة
- التحقق النهائي قبل الحفظ

### 2. **رسائل تحذير واضحة**
- رسائل خطأ عند تجاوز المبلغ
- رسائل تحذير أثناء الإدخال
- عرض المبلغ المتبقي في الفاتورة

### 3. **عرض المعلومات المساعدة**
- عرض المبلغ المتبقي في الفاتورة المختارة
- تحديث المعلومات تلقائياً
- تصميم واضح ومفهوم

## 🔧 التفاصيل التقنية

### 1. **التحقق في حقل المبلغ**
```dart
validator: (value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال المبلغ';
  }
  if (double.tryParse(value) == null) {
    return 'يرجى إدخال رقم صحيح';
  }
  if (double.parse(value) <= 0) {
    return 'يجب أن يكون المبلغ أكبر من صفر';
  }
  
  // التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
  if (_selectedInvoiceId != null) {
    final selectedInvoice = _customerInvoices.firstWhere(
      (inv) => inv['id'] == _selectedInvoiceId,
      orElse: () => {},
    );
    if (selectedInvoice.isNotEmpty) {
      final remainingAmount = selectedInvoice['remaining_amount'] ?? 0.0;
      final enteredAmount = double.parse(value);
      if (enteredAmount > remainingAmount) {
        return 'لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (${remainingAmount.toStringAsFixed(2)} ج.م)';
      }
    }
  }
  
  return null;
},
```

### 2. **التحقق عند تغيير الفاتورة**
```dart
onChanged: (value) {
  setState(() {
    _selectedInvoiceId = value;
  });
  
  // التحقق من المبلغ المدخل عند تغيير الفاتورة
  if (value != null && _amountController.text.isNotEmpty) {
    final selectedInvoice = _customerInvoices.firstWhere(
      (inv) => inv['id'] == value,
      orElse: () => {},
    );
    if (selectedInvoice.isNotEmpty) {
      final remainingAmount = selectedInvoice['remaining_amount'] ?? 0.0;
      final enteredAmount = double.tryParse(_amountController.text) ?? 0.0;
      if (enteredAmount > remainingAmount) {
        // إظهار تحذير للمستخدم
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تحذير: المبلغ المدخل أعلى من المتبقي في الفاتورة (${remainingAmount.toStringAsFixed(2)} ج.م)'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    }
  }
},
```

### 3. **التحقق عند إدخال المبلغ**
```dart
onChanged: (value) {
  // التحقق من المبلغ عند الإدخال
  if (value.isNotEmpty && _selectedInvoiceId != null) {
    final selectedInvoice = _customerInvoices.firstWhere(
      (inv) => inv['id'] == _selectedInvoiceId,
      orElse: () => {},
    );
    if (selectedInvoice.isNotEmpty) {
      final remainingAmount = selectedInvoice['remaining_amount'] ?? 0.0;
      final enteredAmount = double.tryParse(value) ?? 0.0;
      if (enteredAmount > remainingAmount) {
        // إظهار تحذير للمستخدم
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تحذير: المبلغ أعلى من المتبقي في الفاتورة (${remainingAmount.toStringAsFixed(2)} ج.م)'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        });
      }
    }
  }
},
```

### 4. **التحقق النهائي قبل الحفظ**
```dart
// التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
if (_selectedInvoiceId != null) {
  final selectedInvoice = _customerInvoices.firstWhere(
    (inv) => inv['id'] == _selectedInvoiceId,
    orElse: () => {},
  );
  if (selectedInvoice.isNotEmpty) {
    final remainingAmount = selectedInvoice['remaining_amount'] ?? 0.0;
    if (amount > remainingAmount) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (${remainingAmount.toStringAsFixed(2)} ج.م)'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }
  }
}
```

### 5. **عرض المبلغ المتبقي**
```dart
// عرض المبلغ المتبقي في الفاتورة المختارة
if (_selectedInvoiceId != null)
  Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.blue.shade50,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.blue.shade200),
    ),
    child: Row(
      children: [
        Icon(Icons.info_outline, color: Colors.blue.shade700),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'المبلغ المتبقي في الفاتورة: ${_getSelectedInvoiceRemainingAmount().toStringAsFixed(2)} ج.م',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    ),
  ),
```

## 🎯 سيناريوهات الاستخدام

### 1. **سيناريو صحيح**
- المستخدم يختار فاتورة متبقى فيها 500 ج.م
- يدخل مبلغ 300 ج.م
- النظام يسمح بالحفظ بنجاح

### 2. **سيناريو خطأ**
- المستخدم يختار فاتورة متبقى فيها 500 ج.م
- يدخل مبلغ 600 ج.م
- النظام يعرض رسالة خطأ: "لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (500.00 ج.م)"

### 3. **سيناريو تحذير**
- المستخدم يختار فاتورة متبقى فيها 500 ج.م
- يدخل مبلغ 600 ج.م أثناء الكتابة
- النظام يعرض تحذير برتقالي: "تحذير: المبلغ أعلى من المتبقي في الفاتورة (500.00 ج.م)"

## 📱 واجهة المستخدم

### 1. **عرض المبلغ المتبقي**
- مربع أزرق فاتح يعرض المبلغ المتبقي
- أيقونة معلومات
- نص واضح ومقروء

### 2. **رسائل التحذير**
- رسائل برتقالية للتحذير
- رسائل حمراء للخطأ
- مدة عرض مناسبة (2-3 ثواني)

### 3. **التحقق التلقائي**
- تحديث فوري عند تغيير الفاتورة
- تحقق أثناء الكتابة
- منع الحفظ عند الخطأ

## ⚠️ ملاحظات مهمة

### 1. **التحقق المتعدد المستويات**
- تحقق في حقل الإدخال
- تحقق عند تغيير الفاتورة
- تحقق نهائي قبل الحفظ

### 2. **تجربة المستخدم**
- رسائل واضحة ومفهومة
- تحذيرات فورية
- منع الأخطاء المحاسبية

### 3. **الأداء**
- تحقق سريع وفعال
- لا يؤثر على سرعة التطبيق
- تحديث فوري للواجهة

## 🧪 اختبار الميزة

### 1. **اختبار السيناريو الصحيح**
```dart
// اختبار إدخال مبلغ صحيح
final remainingAmount = 500.0;
final enteredAmount = 300.0;
assert(enteredAmount <= remainingAmount); // يجب أن ينجح
```

### 2. **اختبار السيناريو الخاطئ**
```dart
// اختبار إدخال مبلغ خاطئ
final remainingAmount = 500.0;
final enteredAmount = 600.0;
assert(enteredAmount <= remainingAmount); // يجب أن يفشل
```

### 3. **اختبار واجهة المستخدم**
- اختبار عرض المبلغ المتبقي
- اختبار رسائل التحذير
- اختبار منع الحفظ

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | إضافة التحقق من المبلغ | `collections_screen.dart` |
| 2024-01-15 | إضافة رسائل التحذير | `collections_screen.dart` |
| 2024-01-15 | إضافة عرض المبلغ المتبقي | `collections_screen.dart` |

## 🔮 التطويرات المستقبلية

### 1. **ميزات إضافية**
- اقتراح المبلغ المتبقي بالكامل
- تقسيم المبلغ على عدة فواتير
- حساب العمولة تلقائياً

### 2. **تحسينات**
- تحقق أكثر دقة
- رسائل أكثر تفصيلاً
- إحصائيات التحصيل

### 3. **أمان**
- تسجيل محاولات تجاوز المبلغ
- تنبيهات للمدير
- حماية من التلاعب 