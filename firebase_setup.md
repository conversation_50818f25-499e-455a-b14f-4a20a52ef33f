# إعداد Firebase Firestore للتطبيق

## الخطوات المطلوبة:

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انق<PERSON> على "إنشاء مشروع"
3. أدخل اسم المشروع: "Atlas Medical Supplies"
4. اختر "لا" لـ Google Analytics (اختياري)
5. انقر "إنشاء المشروع"

### 2. إضافة تطبيق Android
1. في لوحة التحكم، انقر على أيقونة Android
2. أدخل package name: `com.example.atlas_medical_supplies`
3. انقر "تسجيل التطبيق"
4. حمل ملف `google-services.json`
5. ضعه في مجلد `android/app/`

### 3. إضافة تطبيق Web
1. انقر على أيقونة Web
2. أدخل اسم التطبيق: "Atlas Medical Supplies"
3. انقر "تسجيل التطبيق"
4. انسخ إعدادات Firebase

### 4. تفعيل Firestore
1. في القائمة الجانبية، اختر "Firestore Database"
2. انقر "إنشاء قاعدة بيانات"
3. اختر "بدء في وضع الاختبار"
4. اختر موقع قاعدة البيانات (مثلاً: europe-west1)

### 5. إعداد قواعد الأمان
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للمستخدمين المسجلين
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 6. تفعيل المصادقة
1. اختر "Authentication" من القائمة
2. انقر "Get started"
3. فعّل "Email/Password"
4. فعّل "Phone" (اختياري)

## الملفات المطلوبة:
- `google-services.json` (لـ Android)
- `firebase_options.dart` (سيتم إنشاؤه تلقائياً) 