import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/customer_phone.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'atlas_medical.db');
    return await openDatabase(
      path,
      version: 12,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول المستخدمين
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع')),
        permissions TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        notes TEXT,
        governorate TEXT,
        area TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // جدول أرقام الهواتف للعملاء
    await db.execute('''
      CREATE TABLE customer_phones (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        phone TEXT NOT NULL,
        phone_type TEXT DEFAULT 'الرئيسي',
        is_primary INTEGER DEFAULT 0,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // جدول الفواتير
    await db.execute('''
      CREATE TABLE invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        invoice_number TEXT UNIQUE NOT NULL,
        date TEXT NOT NULL,
        total_amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0,
        remaining_amount REAL NOT NULL,
        notes TEXT,
        created_by INTEGER NOT NULL,
        customer_name TEXT,
        created_by_name TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    ''');

    // جدول التحصيل
    await db.execute('''
      CREATE TABLE collections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        invoice_id INTEGER,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        collector_name TEXT NOT NULL,
        notes TEXT,
        customer_name TEXT,
        payment_method TEXT DEFAULT "نقداً",
        created_by INTEGER,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (invoice_id) REFERENCES invoices (id)
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        unit_price REAL,
        category TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // إنشاء الفهارس لتحسين الأداء
    await _createIndexes(db);

    // إدراج مستخدم مدير افتراضي
    await db.insert('users', {
      'name': 'MOHAMED FAYED',
      'phone': '01125312343',
      'password': '123456', // في التطبيق الحقيقي يجب تشفير كلمة المرور
      'role': 'مدير',
    });

    // إدراج منتجات افتراضية
    await db.insert('products', {
      'name': 'باراسيتامول 500 مجم',
      'description': 'مسكن للألم وخافض للحرارة',
      'unit_price': 15.0,
      'category': 'مسكنات',
    });

    await db.insert('products', {
      'name': 'أموكسيسيلين 500 مجم',
      'description': 'مضاد حيوي واسع الطيف',
      'unit_price': 25.0,
      'category': 'مضادات حيوية',
    });

    await db.insert('products', {
      'name': 'فيتامين سي 1000 مجم',
      'description': 'مكمل غذائي لتقوية المناعة',
      'unit_price': 30.0,
      'category': 'فيتامينات',
    });

    await db.insert('products', {
      'name': 'أوميغا 3',
      'description': 'أحماض دهنية أساسية للقلب والدماغ',
      'unit_price': 45.0,
      'category': 'مكملات غذائية',
    });

    await db.insert('products', {
      'name': 'كريم مرطب للبشرة',
      'description': 'كريم مرطب للبشرة الجافة',
      'unit_price': 35.0,
      'category': 'عناية بالبشرة',
    });

    // إنشاء عملاء افتراضيين
    await _createDefaultCustomers(db);
  }

  // إنشاء فهارس لتحسين الأداء
  Future<void> _createIndexes(Database db) async {
    // فهارس للعملاء
    await db.execute('CREATE INDEX idx_customers_name ON customers(name)');
    await db.execute(
      'CREATE INDEX idx_customers_governorate ON customers(governorate)',
    );

    // فهارس لأرقام هواتف العملاء
    await db.execute(
      'CREATE INDEX idx_customer_phones_customer_id ON customer_phones(customer_id)',
    );
    await db.execute(
      'CREATE INDEX idx_customer_phones_phone ON customer_phones(phone)',
    );
    await db.execute(
      'CREATE INDEX idx_customer_phones_is_primary ON customer_phones(is_primary)',
    );

    // فهارس للفواتير
    await db.execute(
      'CREATE INDEX idx_invoices_customer_id ON invoices(customer_id)',
    );
    await db.execute('CREATE INDEX idx_invoices_date ON invoices(date)');
    await db.execute(
      'CREATE INDEX idx_invoices_created_by ON invoices(created_by)',
    );
    await db.execute(
      'CREATE INDEX idx_invoices_invoice_number ON invoices(invoice_number)',
    );

    // فهارس للتحصيل
    await db.execute(
      'CREATE INDEX idx_collections_customer_id ON collections(customer_id)',
    );
    await db.execute(
      'CREATE INDEX idx_collections_invoice_id ON collections(invoice_id)',
    );
    await db.execute('CREATE INDEX idx_collections_date ON collections(date)');

    // فهارس للمنتجات
    await db.execute('CREATE INDEX idx_products_name ON products(name)');
    await db.execute(
      'CREATE INDEX idx_products_category ON products(category)',
    );

    // فهارس للمستخدمين
    await db.execute('CREATE INDEX idx_users_phone ON users(phone)');
    await db.execute('CREATE INDEX idx_users_role ON users(role)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // إضافة الأعمدة الجديدة لجدول الفواتير
      try {
        await db.execute('ALTER TABLE invoices ADD COLUMN customer_name TEXT');
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN created_by_name TEXT',
        );
        await db.execute('ALTER TABLE invoices ADD COLUMN notes TEXT');
        print('✅ تم تحديث جدول الفواتير بنجاح');
      } catch (e) {
        print('⚠️ الأعمدة موجودة بالفعل في جدول الفواتير: $e');
      }

      // إضافة الأعمدة الجديدة لجدول التحصيل
      try {
        await db.execute(
          'ALTER TABLE collections ADD COLUMN customer_name TEXT',
        );
        await db.execute(
          'ALTER TABLE collections ADD COLUMN payment_method TEXT DEFAULT "نقداً"',
        );
        await db.execute(
          'ALTER TABLE collections ADD COLUMN created_by INTEGER',
        );
        print('✅ تم تحديث جدول التحصيل بنجاح');
      } catch (e) {
        print('⚠️ الأعمدة موجودة بالفعل في جدول التحصيل: $e');
      }
    }

    if (oldVersion < 3) {
      // إضافة عمود المحافظة لجدول العملاء
      try {
        await db.execute('ALTER TABLE customers ADD COLUMN governorate TEXT');
        await db.execute('ALTER TABLE customers ADD COLUMN area TEXT');
        print('✅ تم تحديث جدول العملاء بنجاح');
      } catch (e) {
        print('⚠️ الأعمدة موجودة بالفعل في جدول العملاء: $e');
      }
    }

    if (oldVersion < 4) {
      // إضافة عمود is_active لجدول المنتجات
      try {
        await db.execute(
          'ALTER TABLE products ADD COLUMN is_active INTEGER DEFAULT 1',
        );
        print('✅ تم تحديث جدول المنتجات بنجاح');
      } catch (e) {
        print('⚠️ العمود موجود بالفعل في جدول المنتجات: $e');
      }
    }

    if (oldVersion < 4) {
      // تحديث قيود الدور لتشمل "بائع"
      try {
        // إعادة إنشاء جدول المستخدمين مع القيود الجديدة
        await db.execute('''
          CREATE TABLE users_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع')),
            permissions TEXT,
            is_active INTEGER DEFAULT 1,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        ''');

        // نسخ البيانات من الجدول القديم
        await db.execute('''
          INSERT INTO users_new (id, name, phone, password, role, permissions, is_active, created_at)
          SELECT id, name, phone, password, role, permissions, is_active, created_at
          FROM users
        ''');

        // حذف الجدول القديم وإعادة تسمية الجدول الجديد
        await db.execute('DROP TABLE users');
        await db.execute('ALTER TABLE users_new RENAME TO users');

        print('✅ تم تحديث قيود الدور بنجاح');
      } catch (e) {
        print('⚠️ خطأ في تحديث قيود الدور: $e');
      }
    }

    if (oldVersion < 5) {
      // إضافة الأعمدة المفقودة لجدول الفواتير
      try {
        await db.execute('ALTER TABLE invoices ADD COLUMN customer_name TEXT');
        print('✅ تم إضافة عمود customer_name لجدول الفواتير');
      } catch (e) {
        print('⚠️ عمود customer_name موجود بالفعل في جدول الفواتير: $e');
      }
    }

    if (oldVersion < 6) {
      // إضافة جدول المنتجات
      try {
        await db.execute('''
          CREATE TABLE products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            category TEXT,
            unit_price REAL,
            is_active INTEGER DEFAULT 1,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        ''');
        print('✅ تم إضافة جدول المنتجات بنجاح');
      } catch (e) {
        print('⚠️ جدول المنتجات موجود بالفعل: $e');
      }

      // إضافة جدول عناصر الفاتورة
      try {
        await db.execute('''
          CREATE TABLE invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_name TEXT NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
          )
        ''');
        print('✅ تم إضافة جدول عناصر الفاتورة بنجاح');
      } catch (e) {
        print('⚠️ جدول عناصر الفاتورة موجود بالفعل: $e');
      }

      try {
        await db.execute(
          'ALTER TABLE invoices ADD COLUMN created_by_name TEXT',
        );
        print('✅ تم إضافة عمود created_by_name لجدول الفواتير');
      } catch (e) {
        print('⚠️ عمود created_by_name موجود بالفعل في جدول الفواتير: $e');
      }

      // إضافة الأعمدة المفقودة لجدول التحصيل
      try {
        await db.execute(
          'ALTER TABLE collections ADD COLUMN customer_name TEXT',
        );
        print('✅ تم إضافة عمود customer_name لجدول التحصيل');
      } catch (e) {
        print('⚠️ عمود customer_name موجود بالفعل في جدول التحصيل: $e');
      }

      try {
        await db.execute(
          'ALTER TABLE collections ADD COLUMN payment_method TEXT DEFAULT "نقداً"',
        );
        print('✅ تم إضافة عمود payment_method لجدول التحصيل');
      } catch (e) {
        print('⚠️ عمود payment_method موجود بالفعل في جدول التحصيل: $e');
      }

      try {
        await db.execute(
          'ALTER TABLE collections ADD COLUMN created_by INTEGER',
        );
        print('✅ تم إضافة عمود created_by لجدول التحصيل');
      } catch (e) {
        print('⚠️ عمود created_by موجود بالفعل في جدول التحصيل: $e');
      }
    }

    if (oldVersion < 7) {
      // تحديث جدول المنتجات لجعل unit_price اختياري
      try {
        // إعادة إنشاء جدول المنتجات مع unit_price اختياري
        await db.execute('''
          CREATE TABLE products_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            category TEXT,
            unit_price REAL,
            is_active INTEGER DEFAULT 1,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        ''');

        // نسخ البيانات من الجدول القديم
        await db.execute('''
          INSERT INTO products_new (id, name, description, category, unit_price, is_active, created_at)
          SELECT id, name, description, category, unit_price, is_active, created_at
          FROM products
        ''');

        // حذف الجدول القديم وإعادة تسمية الجدول الجديد
        await db.execute('DROP TABLE products');
        await db.execute('ALTER TABLE products_new RENAME TO products');

        print('✅ تم تحديث جدول المنتجات بنجاح');
      } catch (e) {
        print('⚠️ خطأ في تحديث جدول المنتجات: $e');
      }
    }

    if (oldVersion < 8) {
      // إضافة عمود notes لجدول الفواتير إذا لم يكن موجوداً
      try {
        await db.execute('ALTER TABLE invoices ADD COLUMN notes TEXT');
        print('✅ تم إضافة عمود notes لجدول الفواتير');
      } catch (e) {
        print('⚠️ عمود notes موجود بالفعل في جدول الفواتير: $e');
      }
    }

    if (oldVersion < 11) {
      // إضافة جدول أرقام الهواتف للعملاء
      try {
        await db.execute('''
          CREATE TABLE customer_phones (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            phone TEXT NOT NULL,
            phone_type TEXT DEFAULT 'الرئيسي',
            is_primary INTEGER DEFAULT 0,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
          )
        ''');
        print('✅ تم إضافة جدول أرقام الهواتف للعملاء');

        // نقل أرقام الهواتف الموجودة من جدول العملاء إلى الجدول الجديد
        await db.execute('''
          INSERT INTO customer_phones (customer_id, phone, phone_type, is_primary, notes)
          SELECT id, phone, 'الرئيسي', 1, 'رقم الهاتف الأساسي'
          FROM customers 
          WHERE phone IS NOT NULL AND phone != ''
        ''');
        print('✅ تم نقل أرقام الهواتف الموجودة إلى الجدول الجديد');
      } catch (e) {
        print('⚠️ خطأ في إضافة جدول أرقام الهواتف: $e');
      }
    }

    if (oldVersion < 12) {
      // حذف عمود 'phone' من جدول 'customers'
      try {
        // SQLite لا يدعم حذف الأعمدة مباشرة في الإصدارات القديمة،
        // لذا نحتاج إلى إعادة إنشاء الجدول.
        // أولاً، إعادة تسمية الجدول القديم
        await db.execute('ALTER TABLE customers RENAME TO customers_old');

        // إنشاء الجدول الجديد بدون عمود 'phone'
        await db.execute('''
          CREATE TABLE customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            address TEXT,
            notes TEXT,
            governorate TEXT,
            area TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        ''');

        // نسخ البيانات من الجدول القديم إلى الجدول الجديد
        await db.execute('''
          INSERT INTO customers (id, name, address, notes, governorate, area, created_at)
          SELECT id, name, address, notes, governorate, area, created_at
          FROM customers_old
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE customers_old');
        print('✅ تم حذف عمود الهاتف من جدول العملاء بنجاح');
      } catch (e) {
        print('⚠️ خطأ في حذف عمود الهاتف من جدول العملاء: $e');
      }
    }
  }

  // دوال المستخدمين
  Future<int> insertUser(Map<String, dynamic> user) async {
    final db = await database;
    return await db.insert('users', user);
  }

  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    return await db.query('users', orderBy: 'name ASC');
  }

  Future<Map<String, dynamic>?> getUserByEmail(String email) async {
    final db = await database;
    final result = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<Map<String, dynamic>?> getUserByPhone(String phone) async {
    final db = await database;
    final result = await db.query(
      'users',
      where: 'phone = ?',
      whereArgs: [phone],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<int> updateUser(int id, Map<String, dynamic> user) async {
    final db = await database;
    return await db.update('users', user, where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  // دوال إدارة الصلاحيات
  Future<void> updateUserPermissions(
    int userId,
    List<String> permissions,
  ) async {
    final db = await database;
    final permissionsString = permissions.join(',');
    await db.update(
      'users',
      {'permissions': permissionsString},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<List<String>> getUserPermissions(int userId) async {
    final db = await database;
    final result = await db.query(
      'users',
      columns: ['permissions'],
      where: 'id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (result.isNotEmpty && result.first['permissions'] != null) {
      final permissionsString = result.first['permissions'] as String;
      return permissionsString.split(',').where((p) => p.isNotEmpty).toList();
    }
    return [];
  }

  Future<void> updateUserRole(int userId, String role) async {
    final db = await database;
    await db.update(
      'users',
      {'role': role},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<void> toggleUserStatus(int userId, bool isActive) async {
    final db = await database;
    await db.update(
      'users',
      {'is_active': isActive ? 1 : 0},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<List<Map<String, dynamic>>> getActiveUsers() async {
    final db = await database;
    return await db.query(
      'users',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
  }

  // إعادة تهيئة قاعدة البيانات
  Future<void> resetDatabase() async {
    final db = await database;
    await db.close();
    await databaseFactory.deleteDatabase(
      await getDatabasesPath() + '/atlas_medical.db',
    );
    await database; // إعادة إنشاء قاعدة البيانات
  }

  // إعادة إنشاء جدول invoice_items فقط
  Future<void> recreateInvoiceItemsTable() async {
    try {
      final db = await database;
      print('🔄 إعادة إنشاء جدول invoice_items...');

      // حذف الجدول إذا كان موجوداً
      await db.execute('DROP TABLE IF EXISTS invoice_items');

      // إنشاء الجدول من جديد
      await db.execute('''
        CREATE TABLE invoice_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          product_name TEXT NOT NULL,
          quantity REAL NOT NULL,
          unit_price REAL NOT NULL,
          total_price REAL NOT NULL,
          notes TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )
      ''');

      print('✅ تم إعادة إنشاء جدول invoice_items بنجاح');
    } catch (e) {
      print('❌ خطأ في إعادة إنشاء جدول invoice_items: $e');
      rethrow;
    }
  }

  // دوال العملاء
  Future<int> insertCustomer(Map<String, dynamic> customer) async {
    final db = await database;
    return await db.insert('customers', customer);
  }

  Future<List<Map<String, dynamic>>> getCustomers() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT c.*, cp.phone as primary_phone
      FROM customers c
      LEFT JOIN customer_phones cp ON c.id = cp.customer_id AND cp.is_primary = 1
      ORDER BY c.name ASC
    ''');
  }

  Future<Map<String, dynamic>?> getCustomer(int id) async {
    final db = await database;
    final result = await db.rawQuery(
      '''
      SELECT c.*, cp.phone as primary_phone
      FROM customers c
      LEFT JOIN customer_phones cp ON c.id = cp.customer_id AND cp.is_primary = 1
      WHERE c.id = ?
      LIMIT 1
    ''',
      [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<Map<String, dynamic>?> getCustomerByPhone(String phone) async {
    final db = await database;
    final result = await db.rawQuery(
      '''
      SELECT c.*, cp.phone as primary_phone
      FROM customers c
      INNER JOIN customer_phones cp ON c.id = cp.customer_id
      WHERE cp.phone = ?
      LIMIT 1
    ''',
      [phone],
    );
    return result.isNotEmpty ? result.first : null;
  }

  // دالة محسنة للحصول على الأرقام الموجودة في قاعدة البيانات
  Future<List<Map<String, dynamic>>> getExistingPhones(
    List<String> phoneNumbers,
  ) async {
    if (phoneNumbers.isEmpty) return [];

    final db = await database;
    final placeholders = List.filled(phoneNumbers.length, '?').join(',');
    final result = await db.rawQuery('''
      SELECT cp.phone, cp.customer_id
      FROM customer_phones cp
      WHERE cp.phone IN ($placeholders)
    ''', phoneNumbers);
    return result;
  }

  // دالة محسنة لإضافة أرقام الهواتف في عملية واحدة
  Future<void> insertCustomerPhonesBatch(
    int customerId,
    List<CustomerPhone> phones,
  ) async {
    if (phones.isEmpty) return;

    final db = await database;
    await db.transaction((txn) async {
      for (final phone in phones) {
        final phoneData = {
          'customer_id': customerId,
          'phone': phone.phone,
          'phone_type': phone.phoneType,
          'is_primary': phone.isPrimary ? 1 : 0,
          'notes': phone.notes,
        };
        await txn.insert('customer_phones', phoneData);
      }
    });
  }

  // دالة محسنة لتحديث أرقام الهواتف في عملية واحدة
  Future<void> updateCustomerPhonesBatch(
    int customerId,
    List<CustomerPhone> phones,
  ) async {
    final db = await database;
    await db.transaction((txn) async {
      // حذف جميع الهواتف الموجودة لهذا العميل
      await txn.delete(
        'customer_phones',
        where: 'customer_id = ?',
        whereArgs: [customerId],
      );

      // إدراج جميع الهواتف الجديدة
      for (final phone in phones) {
        final phoneData = {
          'customer_id': customerId,
          'phone': phone.phone,
          'phone_type': phone.phoneType,
          'is_primary': phone.isPrimary ? 1 : 0,
          'notes': phone.notes,
        };
        await txn.insert('customer_phones', phoneData);
      }
    });
  }

  Future<int> updateCustomer(int id, Map<String, dynamic> customer) async {
    final db = await database;
    return await db.update(
      'customers',
      customer,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> deleteCustomer(int id) async {
    final db = await database;
    return await db.delete('customers', where: 'id = ?', whereArgs: [id]);
  }

  // دوال أرقام الهواتف للعملاء
  Future<int> insertCustomerPhone(Map<String, dynamic> phoneData) async {
    final db = await database;
    return await db.insert('customer_phones', phoneData);
  }

  Future<List<Map<String, dynamic>>> getCustomerPhones(int customerId) async {
    final db = await database;
    return await db.query(
      'customer_phones',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'is_primary DESC, created_at ASC',
    );
  }

  // الحصول على جميع أرقام هواتف العملاء
  Future<List<Map<String, dynamic>>> getAllCustomerPhones() async {
    final db = await database;
    return await db.query(
      'customer_phones',
      orderBy: 'customer_id ASC, is_primary DESC, created_at ASC',
    );
  }

  Future<Map<String, dynamic>?> getPrimaryCustomerPhone(int customerId) async {
    final db = await database;
    final result = await db.query(
      'customer_phones',
      where: 'customer_id = ? AND is_primary = 1',
      whereArgs: [customerId],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<int> updateCustomerPhone(
    int phoneId,
    Map<String, dynamic> phoneData,
  ) async {
    final db = await database;
    return await db.update(
      'customer_phones',
      phoneData,
      where: 'id = ?',
      whereArgs: [phoneId],
    );
  }

  Future<int> deleteCustomerPhone(int phoneId) async {
    final db = await database;
    return await db.delete(
      'customer_phones',
      where: 'id = ?',
      whereArgs: [phoneId],
    );
  }

  Future<void> setPrimaryPhone(int customerId, int phoneId) async {
    final db = await database;
    await db.transaction((txn) async {
      // إلغاء تعيين جميع الأرقام كأساسية
      await txn.update(
        'customer_phones',
        {'is_primary': 0},
        where: 'customer_id = ?',
        whereArgs: [customerId],
      );

      // تعيين الرقم المحدد كأساسي
      await txn.update(
        'customer_phones',
        {'is_primary': 1},
        where: 'id = ?',
        whereArgs: [phoneId],
      );
    });
  }

  Future<List<Map<String, dynamic>>> searchCustomersByPhone(
    String phone,
  ) async {
    final db = await database;
    return await db.rawQuery(
      '''
      SELECT DISTINCT c.*, cp.phone as primary_phone
      FROM customers c
      LEFT JOIN customer_phones cp ON c.id = cp.customer_id AND cp.is_primary = 1
      WHERE cp.phone LIKE ? OR c.phone LIKE ?
      ORDER BY c.name ASC
    ''',
      ['%$phone%', '%$phone%'],
    );
  }

  // دوال العملاء حسب المحافظة
  Future<List<Map<String, dynamic>>> getCustomersByGovernorate(
    String governorate,
  ) async {
    final db = await database;
    return await db.query(
      'customers',
      where: 'governorate = ?',
      whereArgs: [governorate],
      orderBy: 'name ASC',
    );
  }

  Future<List<Map<String, dynamic>>> getCustomersByGovernorateWithPhones(
    String governorate,
  ) async {
    final db = await database;
    return await db.rawQuery(
      '''
      SELECT c.*, cp.phone as primary_phone
      FROM customers c
      LEFT JOIN customer_phones cp ON c.id = cp.customer_id AND cp.is_primary = 1
      WHERE c.governorate = ?
      ORDER BY c.name ASC
    ''',
      [governorate],
    );
  }

  Future<List<String>> getGovernorates() async {
    final db = await database;
    final result = await db.query(
      'customers',
      columns: ['DISTINCT governorate'],
      where: 'governorate IS NOT NULL AND governorate != ""',
      orderBy: 'governorate ASC',
    );
    return result.map((row) => row['governorate'] as String).toList();
  }

  Future<Map<String, int>> getCustomerCountByGovernorate() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT governorate, COUNT(*) as count 
      FROM customers 
      WHERE governorate IS NOT NULL AND governorate != "" 
      GROUP BY governorate 
      ORDER BY governorate ASC
    ''');

    Map<String, int> counts = {};
    for (var row in result) {
      counts[row['governorate'] as String] = row['count'] as int;
    }
    return counts;
  }

  // دوال الفواتير
  Future<int> insertInvoice(Map<String, dynamic> invoice) async {
    try {
      final db = await database;
      print('🔄 إدراج فاتورة جديدة: $invoice');

      // إذا لم يتم توفير customer_name، قم بجلبها من جدول العملاء
      if (invoice['customer_name'] == null && invoice['customer_id'] != null) {
        final customer = await db.query(
          'customers',
          columns: ['name'],
          where: 'id = ?',
          whereArgs: [invoice['customer_id']],
          limit: 1,
        );
        if (customer.isNotEmpty) {
          invoice['customer_name'] = customer.first['name'];
        }
      }

      // إذا لم يتم توفير created_by_name، قم بجلبها من جدول المستخدمين
      if (invoice['created_by_name'] == null && invoice['created_by'] != null) {
        final user = await db.query(
          'users',
          columns: ['name'],
          where: 'id = ?',
          whereArgs: [invoice['created_by']],
          limit: 1,
        );
        if (user.isNotEmpty) {
          invoice['created_by_name'] = user.first['name'];
        }
      }

      final result = await db.insert('invoices', invoice);
      print('✅ تم إدراج الفاتورة بنجاح، المعرف: $result');
      return result;
    } catch (e) {
      print('❌ خطأ في إدراج الفاتورة: $e');
      print('❌ بيانات الفاتورة: $invoice');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getInvoices() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT i.*, c.name as customer_name, u.name as created_by_name
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      ORDER BY i.date DESC
    ''');
  }

  Future<List<Map<String, dynamic>>> getInvoicesByCustomer(
    int customerId,
  ) async {
    final db = await database;
    return await db.rawQuery(
      '''
      SELECT i.*, c.name as customer_name, u.name as created_by_name
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      WHERE i.customer_id = ?
      ORDER BY i.date DESC
    ''',
      [customerId],
    );
  }

  Future<List<Map<String, dynamic>>> getAllInvoices() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT i.*, c.name as customer_name, c.governorate, u.name as created_by_name
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      ORDER BY i.date DESC
    ''');
  }

  Future<int> updateInvoice(int id, Map<String, dynamic> invoice) async {
    final db = await database;
    return await db.update(
      'invoices',
      invoice,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> deleteInvoice(int id) async {
    final db = await database;
    return await db.delete('invoices', where: 'id = ?', whereArgs: [id]);
  }

  // دالة للحصول على الفواتير حسب التاريخ
  Future<List<Map<String, dynamic>>> getInvoicesByDate(DateTime date) async {
    final db = await database;
    final dateString =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return await db.query(
      'invoices',
      where: 'date = ?',
      whereArgs: [dateString],
      orderBy: 'invoice_number ASC',
    );
  }

  // دالة للحصول على فاتورة حسب رقم الفاتورة
  Future<Map<String, dynamic>?> getInvoiceByNumber(String invoiceNumber) async {
    final db = await database;
    final results = await db.query(
      'invoices',
      where: 'invoice_number = ?',
      whereArgs: [invoiceNumber],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // دوال التحصيل
  Future<int> insertCollection(Map<String, dynamic> collection) async {
    final db = await database;

    // إذا لم يتم توفير customer_name، قم بجلبها من جدول العملاء
    if (collection['customer_name'] == null &&
        collection['customer_id'] != null) {
      final customer = await db.query(
        'customers',
        columns: ['name'],
        where: 'id = ?',
        whereArgs: [collection['customer_id']],
        limit: 1,
      );
      if (customer.isNotEmpty) {
        collection['customer_name'] = customer.first['name'];
      }
    }

    // إضافة payment_method إذا لم يكن موجوداً
    if (collection['payment_method'] == null) {
      collection['payment_method'] = 'نقداً';
    }

    // إضافة collector_name إذا لم يكن موجوداً
    if (collection['collector_name'] == null) {
      collection['collector_name'] = 'نظام تلقائي';
    }

    return await db.insert('collections', collection);
  }

  Future<List<Map<String, dynamic>>> getCollections() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT c.*, cu.name as customer_name
      FROM collections c
      LEFT JOIN customers cu ON c.customer_id = cu.id
      ORDER BY c.date DESC
    ''');
  }

  Future<List<Map<String, dynamic>>> getAllCollections() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT c.*, cu.name as customer_name
      FROM collections c
      LEFT JOIN customers cu ON c.customer_id = cu.id
      ORDER BY c.date DESC
    ''');
  }

  Future<List<Map<String, dynamic>>> getCollectionsByCustomer(
    int customerId,
  ) async {
    final db = await database;
    return await db.query(
      'collections',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'date DESC',
    );
  }

  Future<Map<String, dynamic>?> getCollection(int id) async {
    final db = await database;
    final results = await db.query(
      'collections',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<int> updateCollection(int id, Map<String, dynamic> data) async {
    final db = await database;
    return await db.update(
      'collections',
      data,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> deleteCollection(int id) async {
    final db = await database;
    return await db.delete('collections', where: 'id = ?', whereArgs: [id]);
  }

  Future<List<Map<String, dynamic>>> getCollectionsByInvoice(
    int invoiceId,
  ) async {
    final db = await database;
    return await db.query(
      'collections',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'date DESC',
    );
  }

  // إحصائيات للوحة التحكم
  Future<Map<String, dynamic>> getDashboardStats() async {
    final db = await database;

    final productsCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM products WHERE is_active = 1',
    );
    final customersCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customers',
    );
    final invoicesCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices',
    );
    final totalSales = await db.rawQuery(
      'SELECT SUM(total_amount) as total FROM invoices',
    );
    final totalCollections = await db.rawQuery(
      'SELECT SUM(amount) as total FROM collections',
    );

    return {
      'products_count': productsCount.first['count'] ?? 0,
      'customers_count': customersCount.first['count'] ?? 0,
      'invoices_count': invoicesCount.first['count'] ?? 0,
      'total_sales': totalSales.first['total'] ?? 0.0,
      'total_collections': totalCollections.first['total'] ?? 0.0,
    };
  }

  Future<List<Map<String, dynamic>>> getUsersStats() async {
    final db = await database;
    return await db.rawQuery('''
      SELECT 
        u.name,
        COUNT(i.id) as invoices_count,
        COALESCE(SUM(i.total_amount), 0) as total_sales,
        COALESCE(SUM(i.paid_amount), 0) as total_collections
      FROM users u
      LEFT JOIN invoices i ON u.id = i.created_by
      GROUP BY u.id, u.name
      ORDER BY total_sales DESC
    ''');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // حذف جميع البيانات (للاستعادة من النسخ الاحتياطية)
  Future<void> clearAllData() async {
    final db = await database;

    // حذف البيانات بترتيب صحيح لتجنب مشاكل المفاتيح الأجنبية
    await db.delete('collections');
    await db.delete('invoices');
    await db.delete('customers');

    // لا نحذف المستخدمين لتجنب مشاكل تسجيل الدخول
    // await db.delete('users');
  }

  // دوال إدارة عناصر الفواتير
  Future<List<Map<String, dynamic>>> getInvoiceItems(int invoiceId) async {
    final db = await database;
    return await db.query(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'id ASC',
    );
  }

  Future<int> insertInvoiceItem(Map<String, dynamic> itemData) async {
    try {
      final db = await database;
      print('🔄 إدراج عنصر فاتورة: $itemData');

      // التحقق من وجود الأعمدة المطلوبة
      final requiredColumns = [
        'invoice_id',
        'product_name',
        'quantity',
        'unit_price',
        'total_price',
      ];
      for (final column in requiredColumns) {
        if (!itemData.containsKey(column)) {
          throw Exception('العمود المطلوب مفقود: $column');
        }
      }

      // التحقق من صحة البيانات
      if (itemData['invoice_id'] == null) {
        throw Exception('معرف الفاتورة مطلوب');
      }
      if (itemData['product_name'] == null ||
          itemData['product_name'].toString().isEmpty) {
        throw Exception('اسم المنتج مطلوب');
      }
      if (itemData['quantity'] == null || itemData['quantity'] <= 0) {
        throw Exception('الكمية يجب أن تكون أكبر من صفر');
      }
      if (itemData['unit_price'] == null || itemData['unit_price'] <= 0) {
        throw Exception('سعر الوحدة يجب أن يكون أكبر من صفر');
      }
      if (itemData['total_price'] == null || itemData['total_price'] <= 0) {
        throw Exception('السعر الإجمالي يجب أن يكون أكبر من صفر');
      }

      // إضافة عمود notes إذا لم يكن موجوداً
      if (!itemData.containsKey('notes')) {
        itemData['notes'] = null;
      }

      // التحقق من هيكل الجدول
      try {
        final tableInfo = await db.rawQuery("PRAGMA table_info(invoice_items)");
        print('🔍 هيكل جدول invoice_items: $tableInfo');

        // التحقق من وجود الجدول
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_items'",
        );
        print('🔍 جداول قاعدة البيانات: $tables');

        if (tables.isEmpty) {
          throw Exception('جدول invoice_items غير موجود!');
        }
      } catch (e) {
        print('⚠️ خطأ في التحقق من هيكل الجدول: $e');
      }

      // طباعة البيانات النهائية قبل الإدراج
      print('🔄 البيانات النهائية للإدراج: $itemData');
      print('🔄 أنواع البيانات:');
      print(
        '  - invoice_id: ${itemData['invoice_id']} (${itemData['invoice_id'].runtimeType})',
      );
      print(
        '  - product_name: ${itemData['product_name']} (${itemData['product_name'].runtimeType})',
      );
      print(
        '  - quantity: ${itemData['quantity']} (${itemData['quantity'].runtimeType})',
      );
      print(
        '  - unit_price: ${itemData['unit_price']} (${itemData['unit_price'].runtimeType})',
      );
      print(
        '  - total_price: ${itemData['total_price']} (${itemData['total_price'].runtimeType})',
      );
      print(
        '  - notes: ${itemData['notes']} (${itemData['notes'].runtimeType})',
      );

      final result = await db.insert('invoice_items', itemData);
      print('✅ تم إدراج عنصر الفاتورة بنجاح، المعرف: $result');
      return result;
    } catch (e) {
      print('❌ خطأ في إدراج عنصر الفاتورة: $e');
      print('❌ بيانات عنصر الفاتورة: $itemData');
      print('❌ نوع الخطأ: ${e.runtimeType}');
      print('❌ تفاصيل الخطأ: ${e.toString()}');

      // محاولة الحصول على مزيد من التفاصيل عن الخطأ
      if (e.toString().contains('no column named')) {
        print('❌ يبدو أن هناك عمود مفقود في الجدول');
        // محاولة إعادة إنشاء الجدول
        try {
          final db = await database;
          await db.execute('DROP TABLE IF EXISTS invoice_items');
          await db.execute('''
            CREATE TABLE invoice_items (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              invoice_id INTEGER NOT NULL,
              product_name TEXT NOT NULL,
              quantity REAL NOT NULL,
              unit_price REAL NOT NULL,
              total_price REAL NOT NULL,
              notes TEXT,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
            )
          ''');
          print('✅ تم إعادة إنشاء جدول invoice_items');
        } catch (recreateError) {
          print('❌ خطأ في إعادة إنشاء الجدول: $recreateError');
        }
      }

      rethrow;
    }
  }

  Future<int> updateInvoiceItem(int id, Map<String, dynamic> itemData) async {
    final db = await database;
    return await db.update(
      'invoice_items',
      itemData,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> deleteInvoiceItem(int id) async {
    final db = await database;
    return await db.delete('invoice_items', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteInvoiceItems(int invoiceId) async {
    final db = await database;
    return await db.delete(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
    );
  }

  // دالة لجلب جميع عناصر الفواتير
  Future<List<Map<String, dynamic>>> getAllInvoiceItems() async {
    final db = await database;
    return await db.query('invoice_items', orderBy: 'created_at DESC');
  }

  // دوال إدارة المنتجات
  Future<List<Map<String, dynamic>>> getProducts() async {
    final db = await database;
    return await db.query(
      'products',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
  }

  Future<int> insertProduct(Map<String, dynamic> productData) async {
    try {
      final db = await database;
      print('🔄 إدراج منتج جديد: $productData');
      final result = await db.insert('products', productData);
      print('✅ تم إدراج المنتج بنجاح، المعرف: $result');
      return result;
    } catch (e) {
      print('❌ خطأ في إدراج المنتج: $e');
      print('❌ بيانات المنتج: $productData');
      rethrow;
    }
  }

  Future<int> updateProduct(int id, Map<String, dynamic> productData) async {
    final db = await database;
    return await db.update(
      'products',
      productData,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> deleteProduct(int id) async {
    final db = await database;
    return await db.update(
      'products',
      {'is_active': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<Map<String, dynamic>?> getProduct(int id) async {
    final db = await database;
    final results = await db.query(
      'products',
      where: 'id = ? AND is_active = ?',
      whereArgs: [id, 1],
    );
    return results.isNotEmpty ? results.first : null;
  }

  // دوال الفواتير حسب المحافظات
  Future<List<Map<String, dynamic>>> getInvoicesByGovernorate(
    String governorate,
  ) async {
    final db = await database;
    return await db.rawQuery(
      '''
      SELECT i.*, c.name as customer_name, c.governorate, u.name as created_by_name
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      WHERE c.governorate = ?
      ORDER BY c.name ASC, i.date DESC
    ''',
      [governorate],
    );
  }

  Future<Map<String, List<Map<String, dynamic>>>>
  getInvoicesByGovernorateGroupedByCustomer(String governorate) async {
    final db = await database;
    final results = await db.rawQuery(
      '''
      SELECT i.*, c.name as customer_name, c.governorate, u.name as created_by_name
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      WHERE c.governorate = ?
      ORDER BY c.name ASC, i.date DESC, i.created_at DESC
    ''',
      [governorate],
    );

    // تجميع الفواتير حسب العميل مع ترتيبها حسب التاريخ
    final Map<String, List<Map<String, dynamic>>> groupedInvoices = {};

    for (final invoice in results) {
      final customerName = invoice['customer_name'] as String? ?? 'غير محدد';
      if (!groupedInvoices.containsKey(customerName)) {
        groupedInvoices[customerName] = [];
      }
      groupedInvoices[customerName]!.add(invoice);
    }

    // ترتيب الفواتير لكل عميل حسب التاريخ (الأحدث أولاً)
    for (final customerInvoices in groupedInvoices.values) {
      customerInvoices.sort((a, b) {
        final dateA = DateTime.tryParse(a['date'] ?? '') ?? DateTime(1900);
        final dateB = DateTime.tryParse(b['date'] ?? '') ?? DateTime(1900);
        return dateB.compareTo(dateA); // الأحدث أولاً
      });
    }

    return groupedInvoices;
  }

  Future<Map<String, int>> getInvoiceCountByGovernorate() async {
    final db = await database;
    final results = await db.rawQuery('''
      SELECT c.governorate, COUNT(i.id) as count
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      WHERE c.governorate IS NOT NULL
      GROUP BY c.governorate
      ORDER BY count DESC
    ''');

    final Map<String, int> counts = {};
    for (final row in results) {
      final governorate = row['governorate'] as String?;
      final count = row['count'] as int?;
      if (governorate != null && count != null) {
        counts[governorate] = count;
      }
    }
    return counts;
  }

  Future<List<String>> getGovernoratesWithInvoices() async {
    final db = await database;
    final results = await db.rawQuery('''
      SELECT DISTINCT c.governorate
      FROM invoices i
      LEFT JOIN customers c ON i.customer_id = c.id
      WHERE c.governorate IS NOT NULL
      ORDER BY c.governorate ASC
    ''');

    return results.map((row) => row['governorate'] as String).toList();
  }

  // إنشاء عملاء افتراضيين
  Future<void> _createDefaultCustomers(Database db) async {
    try {
      // قائمة العملاء الافتراضيين
      final defaultCustomers = [
        {
          'name': 'صيدلية النور',
          'address': 'شارع الجمهورية - وسط البلد',
          'notes': 'صيدلية رئيسية - عميل مميز',
          'governorate': 'القاهرة',
          'area': 'وسط البلد',
        },
        {
          'name': 'صيدلية الشفاء',
          'address': 'شارع النيل - المعادي',
          'notes': 'صيدلية حديثة - طلبات منتظمة',
          'governorate': 'القاهرة',
          'area': 'المعادي',
        },
        {
          'name': 'صيدلية الأمل',
          'address': 'شارع الهرم - الجيزة',
          'notes': 'صيدلية عائلية - عميل قديم',
          'governorate': 'الجيزة',
          'area': 'الهرم',
        },
        {
          'name': 'صيدلية الحياة',
          'address': 'شارع التحرير - الإسكندرية',
          'notes': 'صيدلية كبيرة - طلبات كثيرة',
          'governorate': 'الإسكندرية',
          'area': 'وسط المدينة',
        },
        {
          'name': 'صيدلية السلام',
          'address': 'شارع البحر - بورسعيد',
          'notes': 'صيدلية ساحلية - عميل جديد',
          'governorate': 'بورسعيد',
          'area': 'وسط المدينة',
        },
        {
          'name': 'صيدلية المستقبل',
          'address': 'شارع الجامعة - المنصورة',
          'notes': 'صيدلية جامعية - طلبات طلابية',
          'governorate': 'الدقهلية',
          'area': 'المنصورة',
        },
        {
          'name': 'صيدلية العائلة',
          'address': 'شارع السلام - طنطا',
          'notes': 'صيدلية عائلية - عميل موثوق',
          'governorate': 'الغربية',
          'area': 'طنطا',
        },
        {
          'name': 'صيدلية الصحة',
          'address': 'شارع النصر - أسيوط',
          'notes': 'صيدلية حديثة - طلبات متنوعة',
          'governorate': 'أسيوط',
          'area': 'أسيوط',
        },
        {
          'name': 'صيدلية الأمانة',
          'address': 'شارع الحرية - سوهاج',
          'notes': 'صيدلية قديمة - عميل دائم',
          'governorate': 'سوهاج',
          'area': 'سوهاج',
        },
        {
          'name': 'صيدلية الرعاية',
          'address': 'شارع النهضة - قنا',
          'notes': 'صيدلية متخصصة - طلبات خاصة',
          'governorate': 'قنا',
          'area': 'قنا',
        },
      ];

      // إضافة العملاء وأرقام هواتفهم
      for (int i = 0; i < defaultCustomers.length; i++) {
        final customer = defaultCustomers[i];
        
        // إضافة العميل
        final customerId = await db.insert('customers', customer);
        
        // إضافة رقم الهاتف الرئيسي
        final phoneNumber = '01${(100000000 + i).toString()}'; // أرقام هواتف افتراضية
        await db.insert('customer_phones', {
          'customer_id': customerId,
          'phone': phoneNumber,
          'phone_type': 'الرئيسي',
          'is_primary': 1,
          'notes': 'رقم الهاتف الرئيسي',
        });
      }

      print('✅ تم إنشاء ${defaultCustomers.length} عميل افتراضي بنجاح');
    } catch (e) {
      print('❌ خطأ في إنشاء العملاء الافتراضيين: $e');
    }
  }
}
