# سكريبت إضافة عميل شركة النيل
Write-Host "🚀 بدء إضافة عميل شركة النيل..." -ForegroundColor Green

# التحقق من وجود قاعدة البيانات
$dbPath = "$env:LOCALAPPDATA\com.example.atlas_medical_supplies\databases\atlas_medical.db"

if (-not (Test-Path $dbPath)) {
    Write-Host "❌ قاعدة البيانات غير موجودة في: $dbPath" -ForegroundColor Red
    Write-Host "يرجى تشغيل التطبيق أولاً لإنشاء قاعدة البيانات" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ تم العثور على قاعدة البيانات" -ForegroundColor Green

# بيانات العميل
$customerName = "شركة النيل"
$customerPhone = "01110473536"
$customerAddress = "أبو حمص"
$customerGovernorate = "البحيرة"
$customerArea = "أبو حمص"
$customerNotes = "تم الإضافة بتاريخ $(Get-Date -Format 'yyyy-MM-dd')"

Write-Host "📋 بيانات العميل:" -ForegroundColor Cyan
Write-Host "   الاسم: $customerName" -ForegroundColor White
Write-Host "   الهاتف: $customerPhone" -ForegroundColor White
Write-Host "   المحافظة: $customerGovernorate" -ForegroundColor White
Write-Host "   المنطقة: $customerArea" -ForegroundColor White
Write-Host "   العنوان: $customerAddress" -ForegroundColor White

# استخدام SQLite لإضافة العميل
try {
    # التحقق من وجود العميل مسبقاً
    $checkQuery = "SELECT id, name FROM customers WHERE phone = '$customerPhone'"
    $existingCustomer = sqlite3 $dbPath "$checkQuery"
    
    if ($existingCustomer) {
        Write-Host "⚠️  تحذير: العميل موجود بالفعل في قاعدة البيانات" -ForegroundColor Yellow
        Write-Host "   معرف العميل: $existingCustomer" -ForegroundColor White
        exit 0
    }
    
    # إضافة العميل الجديد
    $insertQuery = @"
INSERT INTO customers (name, phone, address, governorate, area, notes, created_at)
VALUES ('$customerName', '$customerPhone', '$customerAddress', '$customerGovernorate', '$customerArea', '$customerNotes', datetime('now'));
"@
    
    sqlite3 $dbPath "$insertQuery"
    
    # الحصول على معرف العميل المضاف
    $getCustomerQuery = "SELECT id, name, phone, address, governorate, area, notes, created_at FROM customers WHERE phone = '$customerPhone'"
    $addedCustomer = sqlite3 $dbPath "$getCustomerQuery"
    
    Write-Host "✅ تم إضافة العميل بنجاح!" -ForegroundColor Green
    Write-Host "   معرف العميل: $addedCustomer" -ForegroundColor White
    
    Write-Host "🎉 تم إضافة عميل شركة النيل بنجاح إلى قاعدة البيانات!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ خطأ في إضافة العميل: $_" -ForegroundColor Red
    Write-Host "يرجى التأكد من تثبيت SQLite أو استخدام التطبيق لإضافة العميل" -ForegroundColor Yellow
}

Write-Host "`n💡 ملاحظة: يمكنك أيضاً إضافة العميل من خلال التطبيق مباشرة" -ForegroundColor Cyan 