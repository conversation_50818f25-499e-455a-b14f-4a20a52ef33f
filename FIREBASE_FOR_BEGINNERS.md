# 🔥 ربط Firebase للمبتدئين - خطوة بخطوة

## 🎯 ما هو Firebase؟
Firebase هو خدمة من Google لتخزين البيانات في السحابة (الإنترنت) بدلاً من الهاتف فقط.

## 📋 الخطوات المطلوبة منك:

### 1️⃣ إنشاء حساب Google (إذا لم يكن لديك)
1. اذهب إلى: https://accounts.google.com/
2. أنشئ حساب جديد أو استخدم حسابك الحالي

### 2️⃣ إنشاء مشروع Firebase
1. **افتح المتصفح** (Chrome أو Edge)
2. **اذهب إلى:** https://console.firebase.google.com/
3. **سجل دخول** بحساب Google
4. **انقر على "إنشاء مشروع"** أو "Create project"
5. **أدخل اسم المشروع:** `atlas-medical-supplies`
6. **انقر "Continue"**
7. **أوقف Google Analytics** (اختياري - انقر "Don't enable")
8. **انقر "Create project"**

### 3️⃣ إضافة تطبيق Android
1. **في Firebase Console، انقر على أيقونة Android** 📱
2. **أدخل Package name:** `com.atlas.medical.atlas_medical_supplies`
3. **أدخل App nickname:** `Atlas Medical Supplies`
4. **انقر "Register app"**
5. **سيتم تحميل ملف `google-services.json`**
6. **احفظ الملف** في مجلد: `D:\atlas\atlas_medical_supplies\android\app\google-services.json`
7. **انقر "Next"** ثم "Continue to console"

### 4️⃣ تفعيل Authentication (المصادقة)
1. **في Firebase Console، انقر على "Authentication"** من القائمة الجانبية
2. **انقر على "Get started"**
3. **انقر على "Email/Password"**
4. **فعّل "Email/Password"** (أزل العلامة من Disable)
5. **انقر "Save"**

### 5️⃣ إنشاء قاعدة البيانات
1. **في Firebase Console، انقر على "Firestore Database"**
2. **انقر على "Create database"**
3. **اختر "Start in test mode"** (للاختبار)
4. **اختر موقع قاعدة البيانات:** `us-central1` (أو أي موقع قريب)
5. **انقر "Done"**

### 6️⃣ الحصول على الإعدادات
1. **في Firebase Console، انقر على ⚙️ (إعدادات المشروع)**
2. **اختر "General" tab**
3. **انزل إلى "Your apps" section**
4. **انقر على تطبيق Android**
5. **انسخ هذه المعلومات:**
   - `apiKey` (مثل: AIzaSyC1234567890...)
   - `appId` (مثل: 1:123456789012:android:abc123...)
   - `messagingSenderId` (مثل: 123456789012)
   - `projectId` (مثل: atlas-medical-supplies)

### 7️⃣ تحديث ملف الإعدادات
1. **افتح ملف:** `D:\atlas\atlas_medical_supplies\lib\firebase_options.dart`
2. **ابحث عن هذا السطر:**
   ```dart
   static const FirebaseOptions android = FirebaseOptions(
   ```
3. **استبدل القيم بالقيم الحقيقية:**
   ```dart
   static const FirebaseOptions android = FirebaseOptions(
     apiKey: 'YOUR-ACTUAL-API-KEY-HERE',           // ← ضع apiKey هنا
     appId: 'YOUR-ACTUAL-APP-ID-HERE',             // ← ضع appId هنا
     messagingSenderId: 'YOUR-ACTUAL-SENDER-ID',   // ← ضع messagingSenderId هنا
     projectId: 'atlas-medical-supplies',          // ← تأكد من هذا
     storageBucket: 'atlas-medical-supplies.appspot.com',
   );
   ```

### 8️⃣ اختبار الربط
1. **افتح Command Prompt** أو PowerShell
2. **اذهب إلى مجلد التطبيق:**
   ```bash
   cd D:\atlas\atlas_medical_supplies
   ```
3. **نظف التطبيق:**
   ```bash
   flutter clean
   ```
4. **أعد تحميل المكتبات:**
   ```bash
   flutter pub get
   ```
5. **شغل التطبيق:**
   ```bash
   flutter run
   ```

### 9️⃣ تسجيل الدخول
1. **في التطبيق، استخدم:**
   - **رقم الهاتف:** `01125312343`
   - **كلمة المرور:** `123456`

### 🔟 التحقق من النجاح
1. **اذهب إلى Firebase Console**
2. **انقر على "Firestore Database"**
3. **ستجد collections جديدة:** `users`, `customers`, `invoices`, `collections`
4. **انقر على "Authentication"**
5. **ستجد المستخدم الجديد**

## ✅ علامات النجاح:
- ✅ التطبيق يعمل بدون أخطاء
- ✅ تسجيل الدخول يعمل
- ✅ البيانات تظهر في Firestore
- ✅ المستخدم يظهر في Authentication

## 🚨 إذا واجهت مشاكل:
1. **تأكد من صحة الإعدادات**
2. **تحقق من وجود google-services.json**
3. **أعد تشغيل التطبيق**
4. **تحقق من Firebase Console**

## 📞 للمساعدة:
- راجع الملفات الأخرى في المجلد
- تأكد من اتباع الخطوات بدقة
- لا تتردد في السؤال إذا واجهت أي مشكلة

---

**🎉 بعد اتباع هذه الخطوات، ستحصل على تطبيق كامل مع قاعدة بيانات سحابية!** 