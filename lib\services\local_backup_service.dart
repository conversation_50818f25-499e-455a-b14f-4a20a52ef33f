import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../database/database_helper.dart';

class LocalBackupService {
  static Timer? _backupTimer;
  static bool _isInitialized = false;

  // تهيئة خدمة النسخ الاحتياطي
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // بدء النسخ الاحتياطي التلقائي كل ثانية
      _startAutoBackup();
      _isInitialized = true;
      
      print('✅ تم تهيئة خدمة النسخ الاحتياطي المحلي بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة خدمة النسخ الاحتياطي: $e');
    }
  }

  // بدء النسخ الاحتياطي التلقائي
  static void _startAutoBackup() {
    _backupTimer?.cancel();
    _backupTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _createBackup();
    });
  }

  // إيقاف النسخ الاحتياطي التلقائي
  static void stopAutoBackup() {
    _backupTimer?.cancel();
    _backupTimer = null;
    _isInitialized = false;
  }

  // إنشاء نسخة احتياطية
  static Future<void> _createBackup() async {
    try {
      final dbHelper = DatabaseHelper();
      
      // جمع البيانات من قاعدة البيانات المحلية
      final backupData = await _collectBackupData(dbHelper);
      
      // حفظ النسخة الاحتياطية محلياً
      await _saveLocalBackup(backupData);
      
      print('✅ تم إنشاء النسخة الاحتياطية بنجاح');
    } catch (e) {
      print('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
    }
  }

  // جمع بيانات النسخة الاحتياطية
  static Future<Map<String, dynamic>> _collectBackupData(DatabaseHelper dbHelper) async {
    final customers = await dbHelper.getCustomers();
    final invoices = await dbHelper.getInvoices();
    final collections = await dbHelper.getCollections();
    final users = await dbHelper.getUsers();

    return {
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0',
      'data': {
        'customers': customers,
        'invoices': invoices,
        'collections': collections,
        'users': users,
      },
      'statistics': {
        'customers_count': customers.length,
        'invoices_count': invoices.length,
        'collections_count': collections.length,
        'users_count': users.length,
      },
    };
  }

  // حفظ النسخة الاحتياطية محلياً
  static Future<String> _saveLocalBackup(Map<String, dynamic> backupData) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupDir = Directory(path.join(directory.path, 'backups'));
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final backupFileName = 'atlas_backup_$timestamp.json';
    final backupPath = path.join(backupDir.path, backupFileName);

    final backupFile = File(backupPath);
    await backupFile.writeAsString(json.encode(backupData));

    // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
    await _cleanOldBackups(backupDir);

    return backupPath;
  }

  // حذف النسخ الاحتياطية القديمة
  static Future<void> _cleanOldBackups(Directory backupDir) async {
    try {
      final files = await backupDir.list().toList();
      final backupFiles = files.where((file) => 
        file is File && file.path.endsWith('.json')
      ).toList();

      if (backupFiles.length > 10) {
        backupFiles.sort((a, b) => a.statSync().modified.compareTo(b.statSync().modified));
        
        for (int i = 0; i < backupFiles.length - 10; i++) {
          await backupFiles[i].delete();
        }
      }
    } catch (e) {
      print('❌ خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }

  // استعادة النسخة الاحتياطية
  static Future<Map<String, dynamic>> restoreFromBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      final backupContent = await backupFile.readAsString();
      final backupData = json.decode(backupContent);

      final dbHelper = DatabaseHelper();
      
      // مسح البيانات الحالية
      await dbHelper.clearAllData();
      
      // استعادة البيانات
      final data = backupData['data'];
      
      // استعادة العملاء
      for (var customer in data['customers']) {
        await dbHelper.insertCustomer(customer);
      }
      
      // استعادة الفواتير
      for (var invoice in data['invoices']) {
        await dbHelper.insertInvoice(invoice);
      }
      
      // استعادة التحصيلات
      for (var collection in data['collections']) {
        await dbHelper.insertCollection(collection);
      }
      
      // استعادة المستخدمين
      for (var user in data['users']) {
        await dbHelper.insertUser(user);
      }

      return {
        'success': true,
        'message': 'تمت استعادة النسخة الاحتياطية بنجاح',
        'restored_customers': data['customers'].length,
        'restored_invoices': data['invoices'].length,
        'restored_collections': data['collections'].length,
        'restored_users': data['users'].length,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في استعادة النسخة الاحتياطية: $e',
      };
    }
  }

  // الحصول على قائمة النسخ الاحتياطية المحلية
  static Future<List<Map<String, dynamic>>> getLocalBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(directory.path, 'backups'));
      
      if (!await backupDir.exists()) {
        return [];
      }

      final files = await backupDir.list().toList();
      final backupFiles = files.where((file) => 
        file is File && file.path.endsWith('.json')
      ).toList();

      final backups = <Map<String, dynamic>>[];
      
      for (var file in backupFiles) {
        try {
          final fileContent = await File(file.path).readAsString();
          final data = json.decode(fileContent);
          
          backups.add({
            'path': file.path,
            'name': path.basename(file.path),
            'timestamp': data['timestamp'],
            'statistics': data['statistics'],
            'size': await File(file.path).length(),
          });
        } catch (e) {
          print('❌ خطأ في قراءة ملف النسخة الاحتياطية: ${file.path}');
        }
      }

      // ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));
      
      return backups;
    } catch (e) {
      print('❌ خطأ في الحصول على النسخ الاحتياطية المحلية: $e');
      return [];
    }
  }

  // إنشاء نسخة احتياطية يدوية
  static Future<Map<String, dynamic>> createManualBackup() async {
    try {
      final dbHelper = DatabaseHelper();
      final backupData = await _collectBackupData(dbHelper);
      final localBackupPath = await _saveLocalBackup(backupData);

      return {
        'success': true,
        'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
        'local_path': localBackupPath,
        'statistics': backupData['statistics'],
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إنشاء النسخة الاحتياطية: $e',
      };
    }
  }

  // الحصول على إحصائيات النسخ الاحتياطي
  static Future<Map<String, dynamic>> getBackupStatistics() async {
    try {
      final backups = await getLocalBackups();
      final totalSize = backups.fold<int>(0, (sum, backup) => sum + (backup['size'] as int));
      
      return {
        'total_backups': backups.length,
        'total_size_bytes': totalSize,
        'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
        'latest_backup': backups.isNotEmpty ? backups.first['timestamp'] : null,
        'auto_backup_enabled': _isInitialized,
      };
    } catch (e) {
      return {
        'total_backups': 0,
        'total_size_bytes': 0,
        'total_size_mb': '0.00',
        'latest_backup': null,
        'auto_backup_enabled': _isInitialized,
        'error': e.toString(),
      };
    }
  }

  // حذف نسخة احتياطية
  static Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('❌ خطأ في حذف النسخة الاحتياطية: $e');
      return false;
    }
  }

  // تصدير نسخة احتياطية إلى ملف خارجي
  static Future<String?> exportBackup(String backupPath, String exportPath) async {
    try {
      final sourceFile = File(backupPath);
      final targetFile = File(exportPath);
      
      if (await sourceFile.exists()) {
        await sourceFile.copy(targetFile.path);
        return exportPath;
      }
      return null;
    } catch (e) {
      print('❌ خطأ في تصدير النسخة الاحتياطية: $e');
      return null;
    }
  }

  // استيراد نسخة احتياطية من ملف خارجي
  static Future<Map<String, dynamic>> importBackup(String importPath) async {
    try {
      final result = await restoreFromBackup(importPath);
      if (result['success']) {
        result['message'] = 'تم استيراد النسخة الاحتياطية بنجاح';
      }
      return result;
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في استيراد النسخة الاحتياطية: $e',
      };
    }
  }
} 
