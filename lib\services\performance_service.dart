import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PerformanceService {
  static const String _firstLaunchKey = 'first_launch';
  static const String _lastBackupKey = 'last_backup_date';

  // التحقق من أول تشغيل
  static Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirst = prefs.getBool(_firstLaunchKey) ?? true;
      
      if (isFirst) {
        await prefs.setBool(_firstLaunchKey, false);
      }
      
      return isFirst;
    } catch (e) {
      print('❌ خطأ في التحقق من أول تشغيل: $e');
      return false;
    }
  }

  // التحقق من الحاجة لاستعادة النسخة الاحتياطية
  static Future<bool> needsBackupRestore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackup = prefs.getString(_lastBackupKey);
      
      // إذا لم تكن هناك نسخة احتياطية سابقة، نحتاج للاستعادة
      return lastBackup == null;
    } catch (e) {
      print('❌ خطأ في التحقق من النسخة الاحتياطية: $e');
      return false;
    }
  }

  // تحديث تاريخ آخر نسخة احتياطية
  static Future<void> updateLastBackupDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().toIso8601String();
      await prefs.setString(_lastBackupKey, now);
    } catch (e) {
      print('❌ خطأ في تحديث تاريخ النسخة الاحتياطية: $e');
    }
  }

  // تحسين أداء الصور
  static Widget buildOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      filterQuality: FilterQuality.medium,
    );
  }

  // تحسين أداء النصوص الطويلة
  static Widget buildOptimizedText({
    required String text,
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      // تحسين الأداء للنصوص الطويلة
      softWrap: true,
    );
  }

  // تحسين أداء القوائم
  static Widget buildOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool addAutomaticKeepAlives = false,
    bool addRepaintBoundaries = false,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      padding: padding,
      physics: physics,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      // تحسين الأداء
      cacheExtent: 1000,
    );
  }

  // تحسين أداء البطاقات
  static Widget buildOptimizedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return Card(
      margin: margin,
      color: color,
      elevation: elevation,
      shape: shape,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );
  }

  // تنظيف الذاكرة المؤقتة
  static void clearCache() {
    // يمكن إضافة منطق تنظيف الذاكرة المؤقتة هنا
    print('🧹 تم تنظيف الذاكرة المؤقتة');
  }

  // تحسين أداء قاعدة البيانات
  static Future<void> optimizeDatabase() async {
    try {
      // يمكن إضافة منطق تحسين قاعدة البيانات هنا
      print('⚡ تم تحسين قاعدة البيانات');
    } catch (e) {
      print('❌ خطأ في تحسين قاعدة البيانات: $e');
    }
  }
} 
