# 👥 إضافة العملاء الافتراضيين

## 🎯 الهدف
إضافة عملاء افتراضيين إلى قاعدة البيانات بحيث يتم إنشاؤهم تلقائياً عند تثبيت التطبيق لأول مرة.

## ✅ الميزات المضافة

### 1. إنشاء عملاء افتراضيين
تم إضافة دالة `_createDefaultCustomers()` في `database_helper.dart` التي تنشئ 10 عملاء افتراضيين:

#### قائمة العملاء الافتراضيين:
1. **صيدلية النور** - القاهرة، وسط البلد
2. **صيدلية الشفاء** - القاهرة، المعادي
3. **صيدلية الأمل** - الجيزة، الهرم
4. **صيدلية الحياة** - الإسكندرية، وسط المدينة
5. **صيدلية السلام** - بورسعيد، وسط المدينة
6. **صيدلية المستقبل** - الدقهلية، المنصورة
7. **صيدلية العائلة** - الغربية، طنطا
8. **صيدلية الصحة** - أسيوط، أسيوط
9. **صيدلية الأمانة** - سوهاج، سوهاج
10. **صيدلية الرعاية** - قنا، قنا

### 2. أرقام الهواتف الافتراضية
كل عميل يحصل على رقم هاتف افتراضي:
- صيدلية النور: 01100000000
- صيدلية الشفاء: 01100000001
- صيدلية الأمل: 01100000002
- وهكذا...

### 3. بيانات شاملة
كل عميل يحتوي على:
- **الاسم**: اسم الصيدلية
- **العنوان**: عنوان مفصل
- **الملاحظات**: وصف العميل ونوعه
- **المحافظة**: محافظة العميل
- **المنطقة**: المنطقة داخل المحافظة
- **رقم الهاتف**: رقم هاتف رئيسي

## 🔧 التنفيذ التقني

### 1. دالة إنشاء العملاء
```dart
Future<void> _createDefaultCustomers(Database db) async {
  // قائمة العملاء الافتراضيين
  final defaultCustomers = [
    {
      'name': 'صيدلية النور',
      'address': 'شارع الجمهورية - وسط البلد',
      'notes': 'صيدلية رئيسية - عميل مميز',
      'governorate': 'القاهرة',
      'area': 'وسط البلد',
    },
    // ... باقي العملاء
  ];

  // إضافة العملاء وأرقام هواتفهم
  for (int i = 0; i < defaultCustomers.length; i++) {
    final customer = defaultCustomers[i];
    final customerId = await db.insert('customers', customer);
    
    final phoneNumber = '01${(100000000 + i).toString()}';
    await db.insert('customer_phones', {
      'customer_id': customerId,
      'phone': phoneNumber,
      'phone_type': 'الرئيسي',
      'is_primary': 1,
      'notes': 'رقم الهاتف الرئيسي',
    });
  }
}
```

### 2. استدعاء الدالة
تم استدعاء الدالة في `_onCreate()` بعد إنشاء المنتجات الافتراضية:
```dart
// إنشاء عملاء افتراضيين
await _createDefaultCustomers(db);
```

## 📱 النتيجة
✅ عند تثبيت التطبيق لأول مرة، سيتم إنشاء 10 عملاء افتراضيين
✅ كل عميل له رقم هاتف رئيسي
✅ العملاء موزعون على محافظات مختلفة
✅ البيانات جاهزة للاستخدام الفوري

## 🔄 كيفية الاختبار
1. احذف التطبيق من الجهاز
2. أعد تثبيت التطبيق
3. سجل الدخول كمدير
4. اذهب إلى قائمة العملاء
5. ستجد 10 عملاء افتراضيين جاهزين

## 🛠️ تخصيص العملاء
يمكنك تعديل قائمة العملاء الافتراضيين في دالة `_createDefaultCustomers()`:
- إضافة عملاء جدد
- تغيير البيانات
- إضافة محافظات جديدة
- تعديل أرقام الهواتف

## 📊 إحصائيات العملاء الافتراضيين
- **عدد العملاء**: 10 عملاء
- **عدد المحافظات**: 8 محافظات
- **نوع العملاء**: صيدليات
- **أرقام الهواتف**: 10 أرقام فريدة 