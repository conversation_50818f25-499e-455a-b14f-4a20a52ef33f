# سكريبت تنظيف وتحسين قاعدة البيانات
Write-Host "🗄️ بدء تنظيف وتحسين قاعدة البيانات..." -ForegroundColor Green

# التحقق من وجود Flutter
if (!(Get-Command flutter -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Flutter غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    exit 1
}

# تنظيف المشروع
Write-Host "🧹 تنظيف المشروع..." -ForegroundColor Yellow
flutter clean

# حذف مجلد build
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "✅ تم حذف مجلد build" -ForegroundColor Green
}

# حذف مجلد .dart_tool
if (Test-Path ".dart_tool") {
    Remove-Item -Recurse -Force ".dart_tool"
    Write-Host "✅ تم حذف مجلد .dart_tool" -ForegroundColor Green
}

# تحميل التبعيات
Write-Host "📦 تحميل التبعيات..." -ForegroundColor Yellow
flutter pub get

# تحليل الكود
Write-Host "🔍 تحليل الكود..." -ForegroundColor Yellow
flutter analyze

# بناء التطبيق في وضع التطوير
Write-Host "🔨 بناء التطبيق..." -ForegroundColor Yellow
flutter build apk --debug

Write-Host "✅ تم تنظيف وتحسين قاعدة البيانات بنجاح!" -ForegroundColor Green
Write-Host "🚀 يمكنك الآن تشغيل التطبيق باستخدام: flutter run" -ForegroundColor Cyan

# نصائح إضافية
Write-Host "💡 نصائح لتحسين الأداء:" -ForegroundColor Magenta
Write-Host "   - تأكد من إغلاق التطبيقات غير الضرورية" -ForegroundColor White
Write-Host "   - استخدم وضع الطيران عند الاختبار" -ForegroundColor White
Write-Host "   - راقب استهلاك الذاكرة" -ForegroundColor White
Write-Host "   - اختبر على أجهزة مختلفة" -ForegroundColor White 