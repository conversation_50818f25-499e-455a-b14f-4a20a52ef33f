import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('اختبار بسيط للتطبيق', (WidgetTester tester) async {
    // بناء واجهة بسيطة
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.medical_services,
                  size: 64,
                  color: Color(0xFF4A90E2),
                ),
                SizedBox(height: 16),
                Text(
                  'أطلس للمستلزمات الطبية',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4A90E2),
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'نظام إدارة العملاء والفواتير',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // التحقق من وجود العناصر
    expect(find.byIcon(Icons.medical_services), findsOneWidget);
    expect(find.text('أطلس للمستلزمات الطبية'), findsOneWidget);
    expect(find.text('نظام إدارة العملاء والفواتير'), findsOneWidget);
  });

  test('اختبار الألوان', () {
    const primaryColor = Color(0xFF4A90E2);
    const backgroundColor = Color(0xFFE0FFFF);

    expect(primaryColor, isA<Color>());
    expect(backgroundColor, isA<Color>());
  });
}
