@echo off
chcp 65001 >nul
title أطلس للمستلزمات الطبية - تشغيل التطبيق

echo.
echo ========================================
echo    أطلس للمستلزمات الطبية
echo    نظام إدارة العملاء والفواتير
echo ========================================
echo.

echo جاري التحقق من Flutter...
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Flutter غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Flutter أولاً
    pause
    exit /b 1
)

echo ✅ Flutter متوفر
echo.

echo جاري الانتقال إلى مجلد المشروع...
cd /d "%~dp0"
echo ✅ تم الانتقال إلى مجلد المشروع
echo.

echo جاري تحميل التبعيات...
flutter pub get
if errorlevel 1 (
    echo ❌ خطأ في تحميل التبعيات
    pause
    exit /b 1
)
echo ✅ تم تحميل التبعيات بنجاح
echo.

echo ========================================
echo    اختر منصة التشغيل:
echo ========================================
echo.
echo 1. تشغيل على الويب (Chrome) - الأسرع
echo 2. تشغيل على Windows
echo 3. تشغيل على Android (إذا كان متوفر)
echo 4. خروج
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🌐 جاري تشغيل التطبيق على Chrome...
    echo.
    echo ⚠️  ملاحظة: إذا لم يفتح المتصفح تلقائياً،
    echo    انتظر قليلاً ثم افتح المتصفح يدوياً
    echo.
    flutter run -d chrome
) else if "%choice%"=="2" (
    echo.
    echo 🖥️  جاري تشغيل التطبيق على Windows...
    echo.
    flutter run -d windows
) else if "%choice%"=="3" (
    echo.
    echo 📱 جاري تشغيل التطبيق على Android...
    echo.
    echo ⚠️  تأكد من وجود جهاز Android متصل أو محاكي نشط
    echo.
    flutter run
) else if "%choice%"=="4" (
    echo.
    echo 👋 شكراً لاستخدام التطبيق
    echo.
    exit /b 0
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    بيانات تسجيل الدخول:
echo ========================================
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🔑 كلمة المرور: 123456
echo.
echo ========================================
echo.

pause 