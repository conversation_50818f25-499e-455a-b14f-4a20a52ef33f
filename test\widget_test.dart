// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/main.dart';

void main() {
  testWidgets('اختبار شاشة البداية', (WidgetTester tester) async {
    // بناء التطبيق
    await tester.pumpWidget(const AtlasMedicalApp());

    // التحقق من وجود شعار التطبيق
    expect(find.text('أطلس للمستلزمات الطبية'), findsOneWidget);
    expect(find.text('نظام إدارة العملاء والفواتير'), findsOneWidget);
    expect(find.byIcon(Icons.medical_services), findsOneWidget);
  });

  testWidgets('اختبار شاشة تسجيل الدخول', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('أطلس للمستلزمات الطبية'),
                Text('نظام إدارة العملاء والفواتير'),
                Icon(Icons.medical_services),
              ],
            ),
          ),
        ),
      ),
    );

    // التحقق من وجود العناصر
    expect(find.text('أطلس للمستلزمات الطبية'), findsOneWidget);
    expect(find.text('نظام إدارة العملاء والفواتير'), findsOneWidget);
    expect(find.byIcon(Icons.medical_services), findsOneWidget);
  });
}
