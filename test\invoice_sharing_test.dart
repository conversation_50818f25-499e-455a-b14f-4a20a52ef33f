import 'package:flutter_test/flutter_test.dart';
import 'package:atlas_medical_supplies/services/invoice_sharing_service.dart';

void main() {
  group('InvoiceSharingService Tests', () {
    test('تنسيق رقم الهاتف - رقم مصري عادي', () {
      final result = InvoiceSharingService.formatPhoneNumber('**********');
      expect(result, equals('**********'));
    });

    test('تنسيق رقم الهاتف - رقم مع رموز', () {
      final result = InvoiceSharingService.formatPhoneNumber('+20 123 456 789');
      expect(result, equals('2**********'));
    });

    test('تنسيق رقم الهاتف - رقم بدون صفر', () {
      final result = InvoiceSharingService.formatPhoneNumber('**********');
      expect(result, equals('**********'));
    });

    test('تنسيق رقم الهاتف - رقم قصير جداً', () {
      expect(
        () => InvoiceSharingService.formatPhoneNumber('123'),
        throwsException,
      );
    });

    test('إنشاء رسالة الفاتورة - فاتورة مدفوعة', () {
      final invoice = {
        'invoice_number': 'INV-2024-001',
        'date': '2024-01-15',
        'total_amount': 1000.0,
        'paid_amount': 1000.0,
        'remaining_amount': 0.0,
      };

      final customer = {'name': 'أحمد محمد'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('أحمد محمد'));
      expect(message, contains('INV-2024-001'));
      expect(message, contains('1000.00 ج.م'));
      expect(message, contains('✅ تم السداد بالكامل'));
      expect(message, contains('كشف حساب'));
    });

    test('إنشاء رسالة الفاتورة - فاتورة غير مدفوعة', () {
      final invoice = {
        'invoice_number': 'INV-2024-002',
        'date': '2024-01-16',
        'total_amount': 1500.0,
        'paid_amount': 500.0,
        'remaining_amount': 1000.0,
      };

      final customer = {'name': 'فاطمة علي'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('فاطمة علي'));
      expect(message, contains('INV-2024-002'));
      expect(message, contains('1500.00 ج.م'));
      expect(message, contains('500.00 ج.م'));
      expect(message, contains('⚠️ المبلغ المتبقي: 1000.00 ج.م'));
      expect(message, contains('كشف حساب'));
    });

    test('إنشاء رسالة الفاتورة - قيم فارغة', () {
      final invoice = {
        'invoice_number': 'INV-2024-003',
        'date': '2024-01-17',
        'total_amount': null,
        'paid_amount': null,
        'remaining_amount': null,
      };

      final customer = {'name': 'محمد أحمد'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('محمد أحمد'));
      expect(message, contains('INV-2024-003'));
      expect(message, contains('0.00 ج.م'));
      expect(message, contains('✅ تم السداد بالكامل'));
    });
  });

  group('URL Generation Tests', () {
    test('إنشاء رابط واتساب', () {
      final phone = '2**********';
      final message = 'مرحباً، هذه رسالة تجريبية';

      // اختبار إنشاء الرابط (بدون فتحه فعلياً)
      final whatsappUrl =
          'https://wa.me/$phone?text=${Uri.encodeComponent(message)}';

      expect(whatsappUrl, contains('wa.me'));
      expect(whatsappUrl, contains(phone));
      expect(whatsappUrl, contains(Uri.encodeComponent(message)));
    });

    test('إنشاء رابط SMS', () {
      final phone = '2**********';
      final message = 'مرحباً، هذه رسالة تجريبية';

      final smsUrl = 'sms:$phone?body=${Uri.encodeComponent(message)}';

      expect(smsUrl, contains('sms:'));
      expect(smsUrl, contains(phone));
      expect(smsUrl, contains('body='));
    });

    test('إنشاء رابط البريد الإلكتروني', () {
      final email = '<EMAIL>';
      final subject = 'فاتورة جديدة';
      final message = 'مرحباً، هذه رسالة تجريبية';

      final emailUrl =
          'mailto:$email?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(message)}';

      expect(emailUrl, contains('mailto:'));
      expect(emailUrl, contains(email));
      expect(emailUrl, contains('subject='));
      expect(emailUrl, contains('body='));
    });
  });

  group('Error Handling Tests', () {
    test('رقم هاتف فارغ', () {
      expect(
        () => InvoiceSharingService.formatPhoneNumber(''),
        throwsException,
      );
    });

    test('رقم هاتف يحتوي على أحرف غير رقمية فقط', () {
      expect(
        () => InvoiceSharingService.formatPhoneNumber('abc'),
        throwsException,
      );
    });

    test('رقم هاتف قصير جداً', () {
      expect(
        () => InvoiceSharingService.formatPhoneNumber('123'),
        throwsException,
      );
    });

    test('رقم هاتف طويل جداً', () {
      final result = InvoiceSharingService.formatPhoneNumber(
        '0**********123456789',
      );
      expect(result, isNotEmpty);
    });
  });

  group('Message Format Tests', () {
    test('تنسيق الرسالة مع أرقام عشرية', () {
      final invoice = {
        'invoice_number': 'INV-2024-004',
        'date': '2024-01-18',
        'total_amount': 1234.56,
        'paid_amount': 567.89,
        'remaining_amount': 666.67,
      };

      final customer = {'name': 'سارة أحمد'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('1234.56 ج.م'));
      expect(message, contains('567.89 ج.م'));
      expect(message, contains('666.67 ج.م'));
    });

    test('تنسيق الرسالة مع أرقام صحيحة', () {
      final invoice = {
        'invoice_number': 'INV-2024-005',
        'date': '2024-01-19',
        'total_amount': 1000.0,
        'paid_amount': 1000.0,
        'remaining_amount': 0.0,
      };

      final customer = {'name': 'علي محمد'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('1000.00 ج.م'));
      expect(message, contains('✅ تم السداد بالكامل'));
    });

    test('تنسيق الرسالة مع أسماء عربية', () {
      final invoice = {
        'invoice_number': 'INV-2024-006',
        'date': '2024-01-20',
        'total_amount': 500.0,
        'paid_amount': 0.0,
        'remaining_amount': 500.0,
      };

      final customer = {'name': 'عبدالله بن محمد'};

      final message = InvoiceSharingService.createInvoiceMessage(
        invoice,
        customer,
      );

      expect(message, contains('عبدالله بن محمد'));
      expect(message, contains('⚠️ المبلغ المتبقي: 500.00 ج.م'));
    });
  });
}
