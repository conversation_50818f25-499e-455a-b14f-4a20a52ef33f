' إنشاء أيقونة تشغيل في مجلد المشروع
' أطلس للمستلزمات الطبية

Set WshShell = CreateObject("WScript.Shell")
Set FSO = CreateObject("Scripting.FileSystemObject")

' مسار المجلد الحالي
currentPath = WshShell.CurrentDirectory

' إنشاء اختصار في نفس المجلد
Set oShellLink = WshShell.CreateShortcut(currentPath & "\🚀 تشغيل التطبيق.lnk")

' مسار الملف المستهدف
oShellLink.TargetPath = currentPath & "\run_app.bat"

' مجلد العمل
oShellLink.WorkingDirectory = currentPath

' وصف الاختصار
oShellLink.Description = "تشغيل تطبيق أطلس للمستلزمات الطبية"

' أيقونة الاختصار (استخدام أيقونة Flutter)
iconPath = currentPath & "\android\app\src\main\res\mipmap-hdpi\ic_launcher.png"
If FSO.FileExists(iconPath) Then
    oShellLink.IconLocation = iconPath
Else
    ' استخدام أيقونة افتراضية إذا لم توجد الأيقونة المخصصة
    oShellLink.IconLocation = "%SystemRoot%\System32\shell32.dll,21"
End If

' حفظ الاختصار
oShellLink.Save

' إنشاء اختصار سريع أيضاً
Set oShellLink2 = WshShell.CreateShortcut(currentPath & "\⚡ تشغيل سريع.lnk")
oShellLink2.TargetPath = currentPath & "\START.bat"
oShellLink2.WorkingDirectory = currentPath
oShellLink2.Description = "تشغيل سريع للتطبيق على Chrome"
If FSO.FileExists(iconPath) Then
    oShellLink2.IconLocation = iconPath
Else
    oShellLink2.IconLocation = "%SystemRoot%\System32\shell32.dll,21"
End If
oShellLink2.Save

MsgBox "تم إنشاء أيقونات التشغيل في مجلد المشروع بنجاح!" & vbCrLf & vbCrLf & _
       "📁 الملفات المنشأة:" & vbCrLf & _
       "• 🚀 تشغيل التطبيق.lnk" & vbCrLf & _
       "• ⚡ تشغيل سريع.lnk" & vbCrLf & vbCrLf & _
       "يمكنك الآن النقر على أي من الأيقونات لتشغيل التطبيق!", 64, "تم إنشاء الأيقونات" 