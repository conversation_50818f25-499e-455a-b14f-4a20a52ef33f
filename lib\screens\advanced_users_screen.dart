import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/permissions_service.dart';
import '../widgets/atlas_logo.dart';

class AdvancedUsersScreen extends StatefulWidget {
  const AdvancedUsersScreen({super.key});

  @override
  State<AdvancedUsersScreen> createState() => _AdvancedUsersScreenState();
}

class _AdvancedUsersScreenState extends State<AdvancedUsersScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<Map<String, dynamic>> _users = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  Future<void> _checkAdminAccess() async {
    final isAdmin = await AuthService.isAdmin();
    if (!isAdmin) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ليس لديك صلاحية للوصول لهذه الصفحة'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final users = await _dbHelper.getUsers();
      setState(() {
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المستخدمين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> get _filteredUsers {
    if (_searchQuery.isEmpty) return _users;
    return _users.where((user) {
      final name = user['name']?.toString().toLowerCase() ?? '';
      final phone = user['phone']?.toString().toLowerCase() ?? '';
      final role = user['role']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();
      return name.contains(query) ||
          phone.contains(query) ||
          role.contains(query);
    }).toList();
  }

  Future<void> _showAddEditUserDialog([Map<String, dynamic>? user]) async {
    final nameController = TextEditingController(text: user?['name'] ?? '');
    final phoneController = TextEditingController(text: user?['phone'] ?? '');
    final passwordController = TextEditingController();
    String selectedRole = user?['role'] ?? 'مستخدم';
    bool isActive = user?['is_active'] != 0;

    final roles = ['مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع'];

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(user == null ? 'إضافة مستخدم جديد' : 'تعديل المستخدم'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المستخدم',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: phoneController,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                if (user == null) ...[
                  TextFormField(
                    controller: passwordController,
                    obscureText: true,
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                DropdownButtonFormField<String>(
                  value: selectedRole,
                  decoration: const InputDecoration(
                    labelText: 'الدور',
                    border: OutlineInputBorder(),
                  ),
                  items: roles
                      .map(
                        (role) =>
                            DropdownMenuItem(value: role, child: Text(role)),
                      )
                      .toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      selectedRole = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Checkbox(
                      value: isActive,
                      onChanged: (value) {
                        setDialogState(() {
                          isActive = value!;
                        });
                      },
                    ),
                    const Text('نشط'),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.isEmpty ||
                    phoneController.text.isEmpty ||
                    (user == null && passwordController.text.isEmpty)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء جميع الحقول المطلوبة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                try {
                  if (user == null) {
                    // إضافة مستخدم جديد
                    final newUser = {
                      'name': nameController.text,
                      'phone': phoneController.text,
                      'password': passwordController.text,
                      'role': selectedRole,
                      'is_active': isActive ? 1 : 0,
                    };

                    final userId = await _dbHelper.insertUser(newUser);

                    // تعيين الصلاحيات الافتراضية حسب الدور
                    final defaultPermissions =
                        PermissionsService.getDefaultPermissionsByRole(
                          selectedRole,
                        );
                    await _dbHelper.updateUserPermissions(
                      userId,
                      defaultPermissions,
                    );

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إضافة المستخدم بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } else {
                    // تعديل المستخدم
                    final updatedUser = {
                      'name': nameController.text,
                      'phone': phoneController.text,
                      'role': selectedRole,
                      'is_active': isActive ? 1 : 0,
                    };

                    if (passwordController.text.isNotEmpty) {
                      updatedUser['password'] = passwordController.text;
                    }

                    await _dbHelper.updateUser(user['id'], updatedUser);

                    // تحديث الصلاحيات إذا تغير الدور
                    if (user['role'] != selectedRole) {
                      final defaultPermissions =
                          PermissionsService.getDefaultPermissionsByRole(
                            selectedRole,
                          );
                      await _dbHelper.updateUserPermissions(
                        user['id'],
                        defaultPermissions,
                      );
                    }

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم تحديث المستخدم بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  }

                  Navigator.of(context).pop();
                  _loadUsers();
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: Text(user == null ? 'إضافة' : 'تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showPermissionsDialog(Map<String, dynamic> user) async {
    final allPermissions = PermissionsService.getAllAvailablePermissions();
    List<String> userPermissions = await _dbHelper.getUserPermissions(
      user['id'],
    );

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('صلاحيات ${user['name']}'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              itemCount: allPermissions.length,
              itemBuilder: (context, index) {
                final permissionKey = allPermissions.keys.elementAt(index);
                final permissionName = allPermissions[permissionKey]!;
                final isChecked = userPermissions.contains(permissionKey);

                return CheckboxListTile(
                  title: Text(permissionName),
                  value: isChecked,
                  onChanged: (value) {
                    setDialogState(() {
                      if (value!) {
                        userPermissions.add(permissionKey);
                      } else {
                        userPermissions.remove(permissionKey);
                      }
                    });
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await _dbHelper.updateUserPermissions(
                    user['id'],
                    userPermissions,
                  );
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تحديث الصلاحيات بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                  Navigator.of(context).pop();
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteUser(int id, String name) async {
    final currentUserId = await AuthService.getCurrentUserId();
    if (currentUserId == id) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن حذف حسابك الشخصي'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستخدم "$name"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteUser(id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المستخدم بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        _loadUsers();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'مدير':
        return Colors.red;
      case 'مبيعات':
        return Colors.blue;
      case 'محاسب':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: Text(
          'إدارة المستخدمين المتقدمة',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadUsers),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدم...',
                prefixIcon: const Icon(Icons.search, color: Color(0xFF4A90E2)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4A90E2)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFF4A90E2),
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // قائمة المستخدمين
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
                  )
                : _filteredUsers.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? 'لا يوجد مستخدمين'
                              : 'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadUsers,
                    color: const Color(0xFF4A90E2),
                    child: ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom:
                            80, // إضافة مساحة في الأسفل لتجنب تداخل FloatingActionButton
                      ),
                      itemCount: _filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = _filteredUsers[index];
                        final isCurrentUser =
                            user['id'] == AuthService.getCurrentUserId();
                        final isActive = user['is_active'] != 0;

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(16),
                            leading: CircleAvatar(
                              backgroundColor: _getRoleColor(user['role']),
                              child: Text(
                                user['name'].substring(0, 1),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    user['name'],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                if (!isActive)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                      'غير نشط',
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('📱 ${user['phone']}'),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getRoleColor(
                                      user['role'],
                                    ).withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    user['role'],
                                    style: TextStyle(
                                      color: _getRoleColor(user['role']),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            trailing: PopupMenuButton<String>(
                              onSelected: (value) async {
                                switch (value) {
                                  case 'edit':
                                    _showAddEditUserDialog(user);
                                    break;
                                  case 'permissions':
                                    _showPermissionsDialog(user);
                                    break;
                                  case 'toggle_status':
                                    await _dbHelper.toggleUserStatus(
                                      user['id'],
                                      !isActive,
                                    );
                                    _loadUsers();
                                    break;
                                  case 'delete':
                                    if (!isCurrentUser) {
                                      _deleteUser(user['id'], user['name']);
                                    }
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('تعديل')),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'permissions',
                                  child: Row(
                                    children: [
                                      Icon(Icons.security),
                                      SizedBox(width: 8),
                                      Expanded(child: Text('الصلاحيات')),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'toggle_status',
                                  child: Row(
                                    children: [
                                      Icon(
                                        isActive
                                            ? Icons.block
                                            : Icons.check_circle,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          isActive ? 'إلغاء التفعيل' : 'تفعيل',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (!isCurrentUser)
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            'حذف',
                                            style: TextStyle(color: Colors.red),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      // تم إزالة FloatingActionButton حسب طلب المستخدم
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () => _showAddEditUserDialog(),
      //   backgroundColor: const Color(0xFF4A90E2),
      //   child: const Icon(Icons.add, color: Colors.white),
      // ),
    );
  }
}
