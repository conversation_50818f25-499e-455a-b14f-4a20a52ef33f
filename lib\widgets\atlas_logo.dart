import 'package:flutter/material.dart';

class AtlasLogo extends StatelessWidget {
  final double width;
  final double height;
  final double fontSize;
  final double subtitleFontSize;
  final bool showSubtitle;
  final bool showIcon;

  const AtlasLogo({
    super.key,
    this.width = 200,
    this.height = 120,
    this.fontSize = 42,
    this.subtitleFontSize = 12,
    this.showSubtitle = true,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4A90E2), Color(0xFF20B2AA), Color(0xFF008B8B)],
        ),
        borderRadius: BorderRadius.circular(width * 0.1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: width * 0.125,
            offset: Offset(0, height * 0.125),
          ),
        ],
      ),
      child: Stack(
        children: [
          // خلفية الشعار
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(width * 0.1),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.1),
                    Colors.transparent,
                    Colors.black.withOpacity(0.1),
                  ],
                ),
              ),
            ),
          ),
          // كلمة ATLAS
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'ATLAS',
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                    letterSpacing: fontSize * 0.1,
                    shadows: [
                      Shadow(
                        offset: Offset(fontSize * 0.05, fontSize * 0.05),
                        blurRadius: fontSize * 0.1,
                        color: Colors.black26,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: height * 0.067),
                Container(
                  width: width * 0.3,
                  height: height * 0.025,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(height * 0.0125),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: height * 0.02,
                        offset: Offset(0, height * 0.01),
                      ),
                    ],
                  ),
                ),
                if (showSubtitle) ...[
                  SizedBox(height: height * 0.067),
                  Text(
                    'MEDICAL SUPPLIES',
                    style: TextStyle(
                      fontSize: subtitleFontSize,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      letterSpacing: subtitleFontSize * 0.2,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // تم حذف أيقونة الطب الصغيرة
        ],
      ),
    );
  }
}

// شعار صغير للـ AppBar
class AtlasLogoSmall extends StatelessWidget {
  const AtlasLogoSmall({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      height: 40,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [Color(0xFF4A90E2), Color(0xFF20B2AA)],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Center(
        child: Text(
          'ATLAS',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w900,
            color: Colors.white,
            letterSpacing: 2,
            shadows: [
              Shadow(
                offset: Offset(1, 1),
                blurRadius: 2,
                color: Colors.black26,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
