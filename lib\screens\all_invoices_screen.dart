import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/messaging_service.dart';
import '../widgets/collection_dialog.dart';

class AllInvoicesScreen extends StatefulWidget {
  const AllInvoicesScreen({super.key});

  @override
  State<AllInvoicesScreen> createState() => _AllInvoicesScreenState();
}

class _AllInvoicesScreenState extends State<AllInvoicesScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Map<String, dynamic>> _invoices = [];
  List<Map<String, dynamic>> _filteredInvoices = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedTimeFilter = 'all'; // all, today, week, twoWeeks, month

  final Map<String, String> _timeFilterLabels = {
    'all': 'الكل',
    'today': 'اليوم',
    'week': 'الأسبوع',
    'twoWeeks': 'الأسبوعين',
    'month': 'الشهر',
  };

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final invoices = await _databaseHelper.getAllInvoices();
      setState(() {
        _invoices = invoices;
        _filterInvoices();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الفواتير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterInvoices() {
    List<Map<String, dynamic>> filtered = List.from(_invoices);

    // تطبيق التصفية الزمنية
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    switch (_selectedTimeFilter) {
      case 'today':
        filtered = filtered.where((invoice) {
          final invoiceDate = DateTime.parse(invoice['date']);
          final invoiceDay = DateTime(invoiceDate.year, invoiceDate.month, invoiceDate.day);
          return invoiceDay.isAtSameMomentAs(today);
        }).toList();
        break;
      case 'week':
        final weekAgo = today.subtract(const Duration(days: 7));
        filtered = filtered.where((invoice) {
          final invoiceDate = DateTime.parse(invoice['date']);
          return invoiceDate.isAfter(weekAgo) || invoiceDate.isAtSameMomentAs(weekAgo);
        }).toList();
        break;
      case 'twoWeeks':
        final twoWeeksAgo = today.subtract(const Duration(days: 14));
        filtered = filtered.where((invoice) {
          final invoiceDate = DateTime.parse(invoice['date']);
          return invoiceDate.isAfter(twoWeeksAgo) || invoiceDate.isAtSameMomentAs(twoWeeksAgo);
        }).toList();
        break;
      case 'month':
        final monthAgo = DateTime(now.year, now.month - 1, now.day);
        filtered = filtered.where((invoice) {
          final invoiceDate = DateTime.parse(invoice['date']);
          return invoiceDate.isAfter(monthAgo) || invoiceDate.isAtSameMomentAs(monthAgo);
        }).toList();
        break;
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        final customerName = invoice['customer_name']?.toString().toLowerCase() ?? '';
        final invoiceNumber = invoice['invoice_number']?.toString().toLowerCase() ?? '';
        final governorate = invoice['governorate']?.toString().toLowerCase() ?? '';
        
        return customerName.contains(query) || 
               invoiceNumber.contains(query) || 
               governorate.contains(query);
      }).toList();
    }

    setState(() {
      _filteredInvoices = filtered;
    });
  }

  void _onTimeFilterChanged(String filter) {
    setState(() {
      _selectedTimeFilter = filter;
    });
    _filterInvoices();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _filterInvoices();
  }

  void _viewInvoiceDetails(Map<String, dynamic> invoice) {
    Navigator.pushNamed(context, '/invoice-details', arguments: invoice);
  }

  Future<void> _sendInvoiceViaWhatsApp(Map<String, dynamic> invoice) async {
    try {
      // الحصول على بيانات العميل
      final customer = await _databaseHelper.getCustomer(
        invoice['customer_id'],
      );
      if (customer == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن العثور على بيانات العميل'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // إرسال عبر واتساب مباشرة
      final phone = customer['primary_phone']?.toString().trim();
      if (phone == null || phone.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يوجد رقم هاتف للعميل'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // استخدام خدمة الإرسال الجديدة
      final success = await MessagingService.sendCustomMessage(
        phone: phone,
        message: 'فاتورة رقم: ${invoice['invoice_number']}\nالعميل: ${customer['name']}\nالمبلغ: ${invoice['total_amount']} ج.م',
        title: 'إرسال الفاتورة',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال الفاتورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إرسال الفاتورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addCollection(Map<String, dynamic> invoice) async {
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CollectionDialog(
        invoice: invoice,
        remainingAmount: remainingAmount,
      ),
    );

    if (result != null) {
      try {
        await _databaseHelper.insertCollection(result);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة التحصيل بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        _loadInvoices(); // إعادة تحميل البيانات
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة التحصيل: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('جميع الفواتير'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInvoices,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التصفية الزمني
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'البحث في الفواتير...',
                    prefixIcon: const Icon(Icons.search, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF4A90E2)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                ),
                const SizedBox(height: 12),
                // أزرار التصفية الزمنية
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _timeFilterLabels.entries.map((entry) {
                      final isSelected = _selectedTimeFilter == entry.key;
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(entry.value),
                          selected: isSelected,
                          onSelected: (_) => _onTimeFilterChanged(entry.key),
                          selectedColor: const Color(0xFF4A90E2),
                          checkmarkColor: Colors.white,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),

          // قائمة الفواتير
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
                  )
                : _filteredInvoices.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _searchQuery.isEmpty
                                  ? Icons.receipt_outlined
                                  : Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'لا توجد فواتير'
                                  : 'لا توجد نتائج للبحث',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (_searchQuery.isEmpty) ...[
                              const SizedBox(height: 8),
                              const Text(
                                'قم بإضافة فواتير جديدة',
                                style: TextStyle(fontSize: 14, color: Colors.grey),
                              ),
                            ],
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadInvoices,
                        color: const Color(0xFF4A90E2),
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredInvoices.length,
                          itemBuilder: (context, index) {
                            final invoice = _filteredInvoices[index];
                            final isPaid = (invoice['remaining_amount'] ?? 0.0) <= 0;
                            final totalAmount = invoice['total_amount'] ?? 0.0;
                            final paidAmount = invoice['paid_amount'] ?? 0.0;
                            final remainingAmount = invoice['remaining_amount'] ?? 0.0;

                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: isPaid ? Colors.green : const Color(0xFF4A90E2),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            isPaid ? Icons.check : Icons.receipt,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      'فاتورة رقم: ${invoice['invoice_number']}',
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.bold,
                                                        color: Color(0xFF2C3E50),
                                                      ),
                                                    ),
                                                  ),
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: isPaid ? Colors.green : Colors.orange,
                                                      borderRadius: BorderRadius.circular(12),
                                                    ),
                                                    child: Text(
                                                      isPaid ? 'مدفوع' : 'غير مدفوع',
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                'العميل: ${invoice['customer_name']}',
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                              Text(
                                                'المحافظة: ${invoice['governorate']}',
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                              Text(
                                                'التاريخ: ${DateFormat('yyyy/MM/dd').format(DateTime.parse(invoice['date']))}',
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              color: const Color(0xFF4A90E2).withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Text(
                                                  'الإجمالي',
                                                  style: TextStyle(fontSize: 10, color: Colors.grey),
                                                ),
                                                const SizedBox(height: 2),
                                                Text(
                                                  '${totalAmount.toStringAsFixed(0)} ج.م',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Color(0xFF4A90E2),
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                  textAlign: TextAlign.center,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.green.withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Text(
                                                  'المدفوع',
                                                  style: TextStyle(fontSize: 10, color: Colors.grey),
                                                ),
                                                const SizedBox(height: 2),
                                                Text(
                                                  '${paidAmount.toStringAsFixed(0)} ج.م',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.green,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                  textAlign: TextAlign.center,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.red.withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Text(
                                                  'المتبقي',
                                                  style: TextStyle(fontSize: 10, color: Colors.grey),
                                                ),
                                                const SizedBox(height: 2),
                                                Text(
                                                  '${remainingAmount.toStringAsFixed(0)} ج.م',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.red,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                  textAlign: TextAlign.center,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: OutlinedButton.icon(
                                            onPressed: () => _viewInvoiceDetails(invoice),
                                            icon: const Icon(Icons.visibility, size: 16),
                                            label: const Text('عرض', style: TextStyle(fontSize: 12)),
                                            style: OutlinedButton.styleFrom(
                                              foregroundColor: const Color(0xFF4A90E2),
                                              side: const BorderSide(color: Color(0xFF4A90E2)),
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: OutlinedButton.icon(
                                            onPressed: () => _sendInvoiceViaWhatsApp(invoice),
                                            icon: const Icon(Icons.message, size: 16),
                                            label: const Text('واتساب', style: TextStyle(fontSize: 12)),
                                            style: OutlinedButton.styleFrom(
                                              foregroundColor: Colors.green,
                                              side: const BorderSide(color: Colors.green),
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: OutlinedButton.icon(
                                            onPressed: () => _addCollection(invoice),
                                            icon: const Icon(Icons.payment, size: 16),
                                            label: const Text('تحصيل', style: TextStyle(fontSize: 12)),
                                            style: OutlinedButton.styleFrom(
                                              foregroundColor: Colors.orange,
                                              side: const BorderSide(color: Colors.orange),
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }
} 