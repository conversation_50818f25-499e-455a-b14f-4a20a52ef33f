import 'package:flutter/material.dart';
import '../database/database_helper.dart';

class AdvancedReportsService {
  /// تقرير المبيعات الشهرية
  static Future<Map<String, dynamic>> getMonthlySalesReport() async {
    try {
      final dbHelper = DatabaseHelper();
      final invoices = await dbHelper.getAllInvoices();
      
      // تجميع البيانات حسب الشهر
      Map<String, double> monthlySales = {};
      Map<String, double> monthlyPaid = {};
      Map<String, double> monthlyRemaining = {};
      Map<String, int> monthlyInvoices = {};
      
      for (final invoice in invoices) {
        final date = DateTime.parse(invoice['date']);
        final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
        
        final totalAmount = invoice['total_amount'] ?? 0.0;
        final paidAmount = invoice['paid_amount'] ?? 0.0;
        final remainingAmount = invoice['remaining_amount'] ?? 0.0;
        
        monthlySales[monthKey] = (monthlySales[monthKey] ?? 0) + totalAmount;
        monthlyPaid[monthKey] = (monthlyPaid[monthKey] ?? 0) + paidAmount;
        monthlyRemaining[monthKey] = (monthlyRemaining[monthKey] ?? 0) + remainingAmount;
        monthlyInvoices[monthKey] = (monthlyInvoices[monthKey] ?? 0) + 1;
      }
      
      return {
        'monthlySales': monthlySales,
        'monthlyPaid': monthlyPaid,
        'monthlyRemaining': monthlyRemaining,
        'monthlyInvoices': monthlyInvoices,
      };
    } catch (e) {
      print('خطأ في تقرير المبيعات الشهرية: $e');
      return {};
    }
  }

  /// تقرير العملاء الأكثر نشاطاً
  static Future<List<Map<String, dynamic>>> getTopCustomersReport() async {
    try {
      final dbHelper = DatabaseHelper();
      final invoices = await dbHelper.getAllInvoices();
      
      // تجميع البيانات حسب العميل
      Map<String, Map<String, dynamic>> customerStats = {};
      
      for (final invoice in invoices) {
        final customerName = invoice['customer_name'] ?? 'غير محدد';
        
        if (!customerStats.containsKey(customerName)) {
          customerStats[customerName] = {
            'name': customerName,
            'totalInvoices': 0,
            'totalAmount': 0.0,
            'totalPaid': 0.0,
            'totalRemaining': 0.0,
            'lastInvoiceDate': '',
          };
        }
        
        customerStats[customerName]!['totalInvoices']++;
        customerStats[customerName]!['totalAmount'] += invoice['total_amount'] ?? 0.0;
        customerStats[customerName]!['totalPaid'] += invoice['paid_amount'] ?? 0.0;
        customerStats[customerName]!['totalRemaining'] += invoice['remaining_amount'] ?? 0.0;
        
        final invoiceDate = invoice['date'];
        if (invoiceDate.isNotEmpty) {
          customerStats[customerName]!['lastInvoiceDate'] = invoiceDate;
        }
      }
      
      // ترتيب العملاء حسب إجمالي المبيعات
      final sortedCustomers = customerStats.values.toList()
        ..sort((a, b) => (b['totalAmount'] as double).compareTo(a['totalAmount'] as double));
      
      return sortedCustomers.take(10).toList(); // أعلى 10 عملاء
    } catch (e) {
      print('خطأ في تقرير العملاء الأكثر نشاطاً: $e');
      return [];
    }
  }

  /// تقرير المنتجات الأكثر مبيعاً
  static Future<List<Map<String, dynamic>>> getTopProductsReport() async {
    try {
      final dbHelper = DatabaseHelper();
      final invoices = await dbHelper.getAllInvoices();
      
      // تجميع بيانات المنتجات
      Map<String, Map<String, dynamic>> productStats = {};
      
      for (final invoice in invoices) {
        // هنا نحتاج لجلب تفاصيل الفاتورة (المنتجات)
        // هذا مثال مبسط - في التطبيق الحقيقي نحتاج لجلب invoice_items
        final invoiceItems = await dbHelper.getInvoiceItems(invoice['id']);
        
        for (final item in invoiceItems) {
          final productName = item['product_name'] ?? 'غير محدد';
          final quantity = item['quantity'] ?? 0;
          final price = item['price'] ?? 0.0;
          final total = quantity * price;
          
          if (!productStats.containsKey(productName)) {
            productStats[productName] = {
              'name': productName,
              'totalQuantity': 0,
              'totalRevenue': 0.0,
              'averagePrice': 0.0,
              'invoiceCount': 0,
            };
          }
          
          productStats[productName]!['totalQuantity'] += quantity;
          productStats[productName]!['totalRevenue'] += total;
          productStats[productName]!['invoiceCount']++;
        }
      }
      
      // حساب متوسط السعر
      for (final product in productStats.values) {
        if (product['totalQuantity'] > 0) {
          product['averagePrice'] = product['totalRevenue'] / product['totalQuantity'];
        }
      }
      
      // ترتيب المنتجات حسب الإيرادات
      final sortedProducts = productStats.values.toList()
        ..sort((a, b) => (b['totalRevenue'] as double).compareTo(a['totalRevenue'] as double));
      
      return sortedProducts.take(10).toList(); // أعلى 10 منتجات
    } catch (e) {
      print('خطأ في تقرير المنتجات الأكثر مبيعاً: $e');
      return [];
    }
  }

  /// تقرير التحصيلات
  static Future<Map<String, dynamic>> getCollectionsReport() async {
    try {
      final dbHelper = DatabaseHelper();
      final collections = await dbHelper.getAllCollections();
      
      // تجميع البيانات
      Map<String, double> paymentMethodStats = {};
      Map<String, double> collectorStats = {};
      Map<String, double> dailyCollections = {};
      
      double totalCollections = 0;
      
      for (final collection in collections) {
        final amount = collection['amount'] ?? 0.0;
        final paymentMethod = collection['payment_method'] ?? 'غير محدد';
        final collectorName = collection['collector_name'] ?? 'غير محدد';
        final date = collection['date'] ?? '';
        
        totalCollections += amount;
        
        // إحصائيات طريقة الدفع
        paymentMethodStats[paymentMethod] = (paymentMethodStats[paymentMethod] ?? 0) + amount;
        
        // إحصائيات المحصلين
        collectorStats[collectorName] = (collectorStats[collectorName] ?? 0) + amount;
        
        // إحصائيات يومية
        if (date.isNotEmpty) {
          dailyCollections[date] = (dailyCollections[date] ?? 0) + amount;
        }
      }
      
      return {
        'totalCollections': totalCollections,
        'paymentMethodStats': paymentMethodStats,
        'collectorStats': collectorStats,
        'dailyCollections': dailyCollections,
        'totalCount': collections.length,
      };
    } catch (e) {
      print('خطأ في تقرير التحصيلات: $e');
      return {};
    }
  }

  /// تقرير الأداء العام
  static Future<Map<String, dynamic>> getPerformanceReport() async {
    try {
      final dbHelper = DatabaseHelper();
      
      // جلب جميع البيانات
      final customers = await dbHelper.getCustomers();
      final products = await dbHelper.getProducts();
      final invoices = await dbHelper.getAllInvoices();
      final collections = await dbHelper.getAllCollections();
      
      // حساب الإحصائيات
      final totalCustomers = customers.length;
      final totalProducts = products.length;
      final totalInvoices = invoices.length;
      final totalCollections = collections.length;
      
      double totalSales = 0;
      double totalPaid = 0;
      double totalRemaining = 0;
      
      for (final invoice in invoices) {
        totalSales += invoice['total_amount'] ?? 0;
        totalPaid += invoice['paid_amount'] ?? 0;
        totalRemaining += invoice['remaining_amount'] ?? 0;
      }
      
      double totalCollectionsAmount = 0;
      for (final collection in collections) {
        totalCollectionsAmount += collection['amount'] ?? 0;
      }
      
      // حساب معدلات التحويل
      final paymentRate = totalSales > 0 ? (totalPaid / totalSales) * 100 : 0;
      final collectionRate = totalSales > 0 ? (totalCollectionsAmount / totalSales) * 100 : 0;
      
      // حساب متوسط الفاتورة
      final averageInvoiceAmount = totalInvoices > 0 ? totalSales / totalInvoices : 0;
      
      // حساب متوسط التحصيل
      final averageCollectionAmount = totalCollections > 0 ? totalCollectionsAmount / totalCollections : 0;
      
      return {
        'totalCustomers': totalCustomers,
        'totalProducts': totalProducts,
        'totalInvoices': totalInvoices,
        'totalCollections': totalCollections,
        'totalSales': totalSales,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
        'totalCollectionsAmount': totalCollectionsAmount,
        'paymentRate': paymentRate,
        'collectionRate': collectionRate,
        'averageInvoiceAmount': averageInvoiceAmount,
        'averageCollectionAmount': averageCollectionAmount,
      };
    } catch (e) {
      print('خطأ في تقرير الأداء العام: $e');
      return {};
    }
  }

  /// عرض تقرير متقدم
  static Future<void> showAdvancedReport(
    BuildContext context,
    String reportType,
  ) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري تحضير التقرير...'),
            ],
          ),
        ),
      );

      Map<String, dynamic> reportData = {};
      String reportTitle = '';
      
      switch (reportType) {
        case 'monthly_sales':
          reportData = await getMonthlySalesReport();
          reportTitle = 'تقرير المبيعات الشهرية';
          break;
        case 'top_customers':
          final customers = await getTopCustomersReport();
          reportData = {'customers': customers};
          reportTitle = 'أفضل العملاء';
          break;
        case 'top_products':
          final products = await getTopProductsReport();
          reportData = {'products': products};
          reportTitle = 'أفضل المنتجات';
          break;
        case 'collections':
          reportData = await getCollectionsReport();
          reportTitle = 'تقرير التحصيلات';
          break;
        case 'performance':
          reportData = await getPerformanceReport();
          reportTitle = 'تقرير الأداء العام';
          break;
      }
      
      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();
      
      // عرض التقرير
      _showReportDialog(context, reportTitle, reportData, reportType);
      
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحضير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض نافذة التقرير
  static void _showReportDialog(
    BuildContext context,
    String title,
    Map<String, dynamic> data,
    String reportType,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.analytics, color: Color(0xFF4A90E2)),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: _buildReportContent(data, reportType),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              // هنا يمكن إضافة تصدير التقرير
              Navigator.of(context).pop();
            },
            icon: const Icon(Icons.file_download),
            label: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التقرير
  static Widget _buildReportContent(Map<String, dynamic> data, String reportType) {
    switch (reportType) {
      case 'monthly_sales':
        return _buildMonthlySalesContent(data);
      case 'top_customers':
        return _buildTopCustomersContent(data);
      case 'top_products':
        return _buildTopProductsContent(data);
      case 'collections':
        return _buildCollectionsContent(data);
      case 'performance':
        return _buildPerformanceContent(data);
      default:
        return const Text('نوع التقرير غير معروف');
    }
  }

  /// بناء محتوى تقرير المبيعات الشهرية
  static Widget _buildMonthlySalesContent(Map<String, dynamic> data) {
    final monthlySales = data['monthlySales'] as Map<String, double>? ?? {};
    final monthlyPaid = data['monthlyPaid'] as Map<String, double>? ?? {};
    final monthlyRemaining = data['monthlyRemaining'] as Map<String, double>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المبيعات الشهرية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...monthlySales.entries.map((entry) {
          final month = entry.key;
          final sales = entry.value;
          final paid = monthlyPaid[month] ?? 0;
          final remaining = monthlyRemaining[month] ?? 0;
          
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              title: Text('شهر $month'),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('إجمالي المبيعات: ${sales.toStringAsFixed(2)} ج.م'),
                  Text('المدفوع: ${paid.toStringAsFixed(2)} ج.م'),
                  Text('المتبقي: ${remaining.toStringAsFixed(2)} ج.م'),
                ],
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  /// بناء محتوى تقرير أفضل العملاء
  static Widget _buildTopCustomersContent(Map<String, dynamic> data) {
    final customers = data['customers'] as List<Map<String, dynamic>>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أفضل 10 عملاء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...customers.asMap().entries.map((entry) {
          final index = entry.key;
          final customer = entry.value;
          
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: const Color(0xFF4A90E2),
                child: Text('${index + 1}'),
              ),
              title: Text(customer['name']),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('عدد الفواتير: ${customer['totalInvoices']}'),
                  Text('إجمالي المبيعات: ${customer['totalAmount'].toStringAsFixed(2)} ج.م'),
                  Text('المدفوع: ${customer['totalPaid'].toStringAsFixed(2)} ج.م'),
                ],
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  /// بناء محتوى تقرير أفضل المنتجات
  static Widget _buildTopProductsContent(Map<String, dynamic> data) {
    final products = data['products'] as List<Map<String, dynamic>>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أفضل 10 منتجات',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...products.asMap().entries.map((entry) {
          final index = entry.key;
          final product = entry.value;
          
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: const Color(0xFF4A90E2),
                child: Text('${index + 1}'),
              ),
              title: Text(product['name']),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('الكمية المباعة: ${product['totalQuantity']}'),
                  Text('الإيرادات: ${product['totalRevenue'].toStringAsFixed(2)} ج.م'),
                  Text('متوسط السعر: ${product['averagePrice'].toStringAsFixed(2)} ج.م'),
                ],
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  /// بناء محتوى تقرير التحصيلات
  static Widget _buildCollectionsContent(Map<String, dynamic> data) {
    final totalCollections = data['totalCollections'] ?? 0.0;
    final paymentMethodStats = data['paymentMethodStats'] as Map<String, double>? ?? {};
    final collectorStats = data['collectorStats'] as Map<String, double>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجمالي التحصيلات: ${totalCollections.toStringAsFixed(2)} ج.م',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        const Text(
          'طريقة الدفع',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...paymentMethodStats.entries.map((entry) {
          return ListTile(
            title: Text(entry.key),
            trailing: Text('${entry.value.toStringAsFixed(2)} ج.م'),
          );
        }).toList(),
        const SizedBox(height: 16),
        const Text(
          'المحصلين',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...collectorStats.entries.map((entry) {
          return ListTile(
            title: Text(entry.key),
            trailing: Text('${entry.value.toStringAsFixed(2)} ج.م'),
          );
        }).toList(),
      ],
    );
  }

  /// بناء محتوى تقرير الأداء العام
  static Widget _buildPerformanceContent(Map<String, dynamic> data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات العامة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildStatItem('إجمالي العملاء', '${data['totalCustomers']}'),
        _buildStatItem('إجمالي المنتجات', '${data['totalProducts']}'),
        _buildStatItem('إجمالي الفواتير', '${data['totalInvoices']}'),
        _buildStatItem('إجمالي التحصيلات', '${data['totalCollections']}'),
        _buildStatItem('إجمالي المبيعات', '${data['totalSales'].toStringAsFixed(2)} ج.م'),
        _buildStatItem('إجمالي المدفوع', '${data['totalPaid'].toStringAsFixed(2)} ج.م'),
        _buildStatItem('إجمالي المتبقي', '${data['totalRemaining'].toStringAsFixed(2)} ج.م'),
        _buildStatItem('معدل الدفع', '${data['paymentRate'].toStringAsFixed(1)}%'),
        _buildStatItem('معدل التحصيل', '${data['collectionRate'].toStringAsFixed(1)}%'),
        _buildStatItem('متوسط الفاتورة', '${data['averageInvoiceAmount'].toStringAsFixed(2)} ج.م'),
        _buildStatItem('متوسط التحصيل', '${data['averageCollectionAmount'].toStringAsFixed(2)} ج.م'),
      ],
    );
  }

  /// بناء عنصر إحصائي
  static Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
} 