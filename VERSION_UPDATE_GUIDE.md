# 📱 دليل تحديث إصدار التطبيق

## 🔄 كيفية تغيير إصدار التطبيق

### 📋 تنسيق الإصدار
```
version: X.Y.Z+B
```
- **X.Y.Z** = رقم الإصدار (مثل: 1.2.0)
- **B** = رقم البناء (مثل: 3)

### 📁 الملفات التي تحتاج إلى تحديث

#### 1. **الملف الرئيسي - `pubspec.yaml`** ⭐ (الأهم)
```yaml
version: 1.2.0+3
```

#### 2. **ملف Android - `android/app/build.gradle.kts`**
```kotlin
defaultConfig {
    versionCode = 3
    versionName = "1.2.0"
}
```

#### 3. **ملف iOS - `ios/Runner/Info.plist`**
```xml
<key>CFBundleShortVersionString</key>
<string>1.2.0</string>
<key>CFBundleVersion</key>
<string>3</string>
```

## 🎯 خطوات التحديث

### الطريقة الأولى: التحديث التلقائي (المفضلة)
1. **تحديث `pubspec.yaml` فقط:**
   ```yaml
   version: 1.2.0+3
   ```
2. **تشغيل الأمر:**
   ```bash
   flutter clean
   flutter pub get
   ```

### الطريقة الثانية: التحديث اليدوي
1. تحديث `pubspec.yaml`
2. تحديث `android/app/build.gradle.kts`
3. تحديث `ios/Runner/Info.plist`

## 📊 أمثلة على الإصدارات

| الإصدار | الوصف |
|---------|-------|
| `1.0.0+1` | الإصدار الأول |
| `1.0.1+2` | إصلاحات بسيطة |
| `1.1.0+3` | ميزات جديدة |
| `2.0.0+4` | تحديث كبير |

## ⚠️ ملاحظات مهمة

1. **رقم البناء (Build Number)** يجب أن يكون أكبر من السابق
2. **رقم الإصدار** يتبع نظام Semantic Versioning
3. **Android** و **iOS** يأخذان الإصدار من `pubspec.yaml` تلقائياً
4. **Windows** و **macOS** يأخذان الإصدار من `pubspec.yaml` تلقائياً

## 🔧 أوامر مفيدة

```bash
# تنظيف وإعادة بناء
flutter clean
flutter pub get

# بناء APK مع إصدار محدد
flutter build apk --build-name=1.2.0 --build-number=3

# بناء iOS مع إصدار محدد
flutter build ios --build-name=1.2.0 --build-number=3
```

## 📱 التحقق من الإصدار

بعد التحديث، يمكنك التحقق من الإصدار في التطبيق:

```dart
import 'package:package_info_plus/package_info_plus.dart';

Future<String> getAppVersion() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  return packageInfo.version;
}
``` 