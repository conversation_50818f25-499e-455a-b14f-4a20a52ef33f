import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

void main() async {
  await createSimpleIcon();
}

Future<void> createSimpleIcon() async {
  print('🎨 إنشاء أيقونة InvoFast البسيطة...');

  // إنشاء مجلد assets/images إذا لم يكن موجوداً
  final assetsDir = Directory('assets/images');
  if (!await assetsDir.exists()) {
    await assetsDir.create(recursive: true);
  }

  // إنشاء الأيقونة الرئيسية
  final icon = await createAtlasIcon();
  final iconFile = File('assets/images/atlas_icon_simple.png');
  await iconFile.writeAsBytes(icon);
  print('✅ تم إنشاء الأيقونة الرئيسية: assets/images/atlas_icon_simple.png');

  // إنشاء الأيقونة التكيفية
  final adaptiveIcon = await createAdaptiveIcon();
  final adaptiveIconFile = File('assets/images/atlas_icon_adaptive.png');
  await adaptiveIconFile.writeAsBytes(adaptiveIcon);
  print('✅ تم إنشاء الأيقونة التكيفية: assets/images/atlas_icon_adaptive.png');

  print('🎉 تم إنشاء جميع الأيقونات بنجاح!');
  print(
    '📱 يمكنك الآن تشغيل: flutter pub get && flutter pub run flutter_launcher_icons',
  );
}

Future<Uint8List> createAtlasIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  final size = const Size(1024, 1024);

  // الألوان
  const primaryColor = Color(0xFF40E0D0); // تركوازي
  const secondaryColor = Color(0xFF20B2AA); // تركوازي متوسط
  const darkColor = Color(0xFF008B8B); // تركوازي داكن
  const whiteColor = Colors.white;

  // رسم خلفية دائرية بتدرج لوني
  final center = Offset(size.width / 2, size.height / 2);
  final radius = 480.0;

  // رسم الدائرة الرئيسية
  final paint = Paint()
    ..style = PaintingStyle.fill
    ..shader = RadialGradient(
      colors: [primaryColor, secondaryColor, darkColor],
      stops: const [0.0, 0.5, 1.0],
    ).createShader(Rect.fromCircle(center: center, radius: radius));

  canvas.drawCircle(center, radius, paint);

  // رسم الصليب الطبي
  final crossPaint = Paint()
    ..color = whiteColor
    ..style = PaintingStyle.fill;

  const crossWidth = 16.0;
  const crossHeight = 240.0;

  // الخط العمودي
  final verticalRect = Rect.fromCenter(
    center: center,
    width: crossWidth,
    height: crossHeight,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(verticalRect, const Radius.circular(8)),
    crossPaint,
  );

  // الخط الأفقي
  final horizontalRect = Rect.fromCenter(
    center: center,
    width: crossHeight,
    height: crossWidth,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(horizontalRect, const Radius.circular(8)),
    crossPaint,
  );

  // إضافة النص "ATLAS"
  const textStyle = TextStyle(
    color: whiteColor,
    fontSize: 120,
    fontWeight: FontWeight.bold,
    fontFamily: 'Arial',
  );

  final textPainter = TextPainter(
    text: const TextSpan(text: 'ATLAS', style: textStyle),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();

  final textOffset = Offset(center.dx - textPainter.width / 2, center.dy + 80);
  textPainter.paint(canvas, textOffset);

  // إضافة النص "MEDICAL"
  const medicalTextStyle = TextStyle(
    color: whiteColor,
    fontSize: 48,
    fontWeight: FontWeight.normal,
    fontFamily: 'Arial',
  );

  final medicalTextPainter = TextPainter(
    text: const TextSpan(text: 'MEDICAL', style: medicalTextStyle),
    textDirection: TextDirection.ltr,
  );
  medicalTextPainter.layout();

  final medicalTextOffset = Offset(
    center.dx - medicalTextPainter.width / 2,
    center.dy + 200,
  );
  medicalTextPainter.paint(canvas, medicalTextOffset);

  // تحويل إلى صورة
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

  return byteData!.buffer.asUint8List();
}

Future<Uint8List> createAdaptiveIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = Size(1024, 1024);

  // خلفية شفافة
  canvas.drawColor(Colors.transparent, BlendMode.clear);

  const whiteColor = Colors.white;
  final center = Offset(size.width / 2, size.height / 2);

  // رسم الصليب الطبي فقط
  final crossPaint = Paint()
    ..color = whiteColor
    ..style = PaintingStyle.fill;

  const crossWidth = 16.0;
  const crossHeight = 240.0;

  // الخط العمودي
  final verticalRect = Rect.fromCenter(
    center: center,
    width: crossWidth,
    height: crossHeight,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(verticalRect, const Radius.circular(8)),
    crossPaint,
  );

  // الخط الأفقي
  final horizontalRect = Rect.fromCenter(
    center: center,
    width: crossHeight,
    height: crossWidth,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(horizontalRect, const Radius.circular(8)),
    crossPaint,
  );

  // إضافة النص "ATLAS" فقط
  const textStyle = TextStyle(
    color: whiteColor,
    fontSize: 120,
    fontWeight: FontWeight.bold,
    fontFamily: 'Arial',
  );

  final textPainter = TextPainter(
    text: const TextSpan(text: 'ATLAS', style: textStyle),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();

  final textOffset = Offset(center.dx - textPainter.width / 2, center.dy + 80);
  textPainter.paint(canvas, textOffset);

  // تحويل إلى صورة
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

  return byteData!.buffer.asUint8List();
}
