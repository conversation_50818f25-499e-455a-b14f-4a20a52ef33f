# 🏛️ ميزة عرض فواتير العميل من قائمة المحافظات

## 📋 نظرة عامة

تم إضافة ميزة شاملة تسمح بالوصول إلى فواتير العميل والتحكم الكامل بها من خلال قائمة المحافظات. هذه الميزة تتيح للمستخدم التنقل بسهولة من المحافظة إلى العملاء ثم إلى فواتير كل عميل.

## 🚀 الميزات المضافة

### 1. **الوصول المباشر لفواتير العميل**
- الضغط على العميل من قائمة المحافظات
- عرض جميع فواتير العميل المحدد
- إحصائيات شاملة للعميل

### 2. **التحكم الكامل في الفواتير**
- إضافة فاتورة جديدة للعميل
- تعديل الفواتير الموجودة
- حذف الفواتير
- إضافة تحصيل للفواتير

### 3. **ميزات إضافية**
- إرسال الفواتير عبر واتساب
- إرسال الفواتير عبر رسائل نصية
- البحث في فواتير العميل
- عرض إحصائيات مفصلة

## 🔧 التفاصيل التقنية

### 1. **تعديل شاشة المحافظات**
```dart
// إضافة إمكانية الضغط على العميل
trailing: const Icon(
  Icons.arrow_forward_ios,
  color: Color(0xFF40E0D0),
),
onTap: () => _showCustomerInvoices(customer),
```

### 2. **دالة الانتقال لفواتير العميل**
```dart
void _showCustomerInvoices(Map<String, dynamic> customer) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => CustomerInvoicesScreen(customer: customer),
    ),
  );
}
```

### 3. **شاشة فواتير العميل الجديدة**
```dart
class CustomerInvoicesScreen extends StatefulWidget {
  final Map<String, dynamic> customer;

  const CustomerInvoicesScreen({super.key, required this.customer});

  @override
  State<CustomerInvoicesScreen> createState() => _CustomerInvoicesScreenState();
}
```

### 4. **تحميل فواتير العميل**
```dart
Future<void> _loadInvoices() async {
  setState(() {
    _isLoading = true;
  });

  try {
    final invoices = await _databaseHelper.getInvoicesByCustomer(widget.customer['id']);
    setState(() {
      _invoices = invoices;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _isLoading = false;
    });
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل الفواتير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
```

### 5. **حساب الإحصائيات**
```dart
final totalInvoices = _invoices.length;
final totalAmount = _invoices.fold<double>(0, (sum, invoice) => sum + (invoice['total_amount'] ?? 0));
final totalPaid = _invoices.fold<double>(0, (sum, invoice) => sum + (invoice['paid_amount'] ?? 0));
final totalRemaining = totalAmount - totalPaid;
```

## 📱 واجهة المستخدم

### 1. **معلومات العميل**
```dart
Container(
  margin: const EdgeInsets.all(16),
  padding: const EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.1),
        spreadRadius: 1,
        blurRadius: 5,
        offset: const Offset(0, 2),
      ),
    ],
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // معلومات العميل الأساسية
      Row(
        children: [
          CircleAvatar(
            backgroundColor: const Color(0xFF40E0D0),
            child: Text(
              widget.customer['name'].toString().substring(0, 1).toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.customer['name'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.customer['phone'] != null)
                  Text(
                    'الهاتف: ${widget.customer['phone']}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
              ],
            ),
          ),
        ],
      ),
      const SizedBox(height: 16),
      // إحصائيات العميل
      Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الفواتير',
              totalInvoices.toString(),
              Icons.receipt,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'إجمالي المبالغ',
              '${totalAmount.toStringAsFixed(2)} ج.م',
              Icons.attach_money,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المتبقي',
              '${totalRemaining.toStringAsFixed(2)} ج.م',
              Icons.pending,
              Colors.red,
            ),
          ),
        ],
      ),
    ],
  ),
),
```

### 2. **بطاقات الإحصائيات**
```dart
Widget _buildStatCard(String title, String value, IconData icon, Color color) {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: color.withOpacity(0.3)),
    ),
    child: Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: color.withOpacity(0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}
```

### 3. **قائمة الفواتير**
- عرض جميع فواتير العميل
- إمكانية البحث في الفواتير
- خيارات متعددة لكل فاتورة (تعديل، تحصيل، إرسال، حذف)
- عرض حالة الفاتورة (مدفوعة/غير مدفوعة)

## 🎯 سيناريوهات الاستخدام

### 1. **الوصول لفواتير العميل**
- المستخدم يفتح شاشة المحافظات
- يختار محافظة معينة
- يرى قائمة العملاء في هذه المحافظة
- يضغط على عميل محدد
- ينتقل إلى شاشة فواتير هذا العميل

### 2. **إدارة فواتير العميل**
- المستخدم يرى إحصائيات العميل
- يمكنه إضافة فاتورة جديدة
- يمكنه تعديل الفواتير الموجودة
- يمكنه إضافة تحصيل للفواتير
- يمكنه حذف الفواتير

### 3. **إرسال الفواتير**
- المستخدم يختار فاتورة معينة
- يضغط على "إرسال واتساب" أو "رسالة نصية"
- يتم إرسال تفاصيل الفاتورة للعميل

## ⚠️ ملاحظات مهمة

### 1. **التحكم الكامل**
- جميع العمليات متاحة من شاشة واحدة
- لا حاجة للانتقال بين الشاشات
- تجربة مستخدم سلسة ومتسقة

### 2. **الأمان**
- التحقق من صحة البيانات
- تأكيد الحذف
- حفظ معرف المستخدم

### 3. **الأداء**
- تحميل سريع للفواتير
- تحديث فوري للبيانات
- واجهة متجاوبة

## 🧪 اختبار الميزة

### 1. **اختبار الانتقال**
```dart
// اختبار الانتقال من المحافظة إلى فواتير العميل
final customer = {
  'id': 1,
  'name': 'عميل تجريبي',
  'phone': '0123456789',
};

Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => CustomerInvoicesScreen(customer: customer),
  ),
);
```

### 2. **اختبار تحميل الفواتير**
```dart
// اختبار تحميل فواتير العميل
final invoices = await _databaseHelper.getInvoicesByCustomer(customerId);
assert(invoices.isNotEmpty);
```

### 3. **اختبار الإحصائيات**
```dart
// اختبار حساب الإحصائيات
final totalInvoices = invoices.length;
final totalAmount = invoices.fold<double>(0, (sum, invoice) => sum + (invoice['total_amount'] ?? 0));
final totalPaid = invoices.fold<double>(0, (sum, invoice) => sum + (invoice['paid_amount'] ?? 0));
final totalRemaining = totalAmount - totalPaid;

assert(totalRemaining >= 0);
```

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | إضافة إمكانية الضغط على العميل | `governorates_screen.dart` |
| 2024-01-15 | إنشاء شاشة فواتير العميل | `customer_invoices_screen.dart` |
| 2024-01-15 | إضافة التحكم الكامل في الفواتير | `customer_invoices_screen.dart` |
| 2024-01-15 | إضافة الإحصائيات والبحث | `customer_invoices_screen.dart` |

## 🔮 التطويرات المستقبلية

### 1. **ميزات إضافية**
- تصفية الفواتير حسب التاريخ
- تصدير فواتير العميل
- إشعارات للفواتير المتأخرة

### 2. **تحسينات**
- رسوم بيانية للإحصائيات
- تقارير مفصلة
- ميزة الطباعة

### 3. **أمان**
- صلاحيات محددة للمستخدمين
- سجل العمليات
- نسخ احتياطية

## 💡 نصائح للاستخدام

### 1. **للمستخدمين**
- استخدم البحث للعثور على فواتير محددة
- راجع الإحصائيات قبل إضافة فواتير جديدة
- استخدم ميزة الإرسال للتواصل مع العملاء

### 2. **للمديرين**
- راجع إحصائيات العملاء بانتظام
- تأكد من صحة البيانات
- استخدم التقارير للمتابعة

### 3. **للمطورين**
- اختبر الميزة في بيئة التطوير
- تأكد من تحديث قاعدة البيانات
- وثق أي تغييرات إضافية

## 🎉 النتيجة النهائية

الآن يمكن للمستخدمين الوصول إلى فواتير أي عميل والتحكم الكامل بها مباشرة من قائمة المحافظات! 🚀

### الميزات الرئيسية:
- ✅ الوصول المباشر لفواتير العميل
- ✅ التحكم الكامل في الفواتير
- ✅ إحصائيات شاملة للعميل
- ✅ إرسال الفواتير عبر واتساب ورسائل نصية
- ✅ البحث في فواتير العميل
- ✅ واجهة مستخدم سهلة ومفهومة 