import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CollectionDialog extends StatefulWidget {
  final Map<String, dynamic> invoice;
  final double remainingAmount;

  const CollectionDialog({
    Key? key,
    required this.invoice,
    required this.remainingAmount,
  }) : super(key: key);

  @override
  State<CollectionDialog> createState() => _CollectionDialogState();
}

class _CollectionDialogState extends State<CollectionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _collectorNameController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  String _selectedPaymentMethod = 'نقداً';
  bool _isLoading = false;

  final List<String> _paymentMethods = [
    'نقداً',
    'فودافون كاش',
    'انستا باي',
    'اتصالات كاش',
    'المحافظ المصرية',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.remainingAmount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _collectorNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveCollection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      // التحقق من عدم تجاوز المبلغ المتبقي
      if (amount > widget.remainingAmount) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (${widget.remainingAmount.toStringAsFixed(2)} ج.م)'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final result = {
        'amount': amount,
        'date': DateFormat('yyyy-MM-dd').format(_selectedDate),
        'collector_name': _collectorNameController.text.trim().isEmpty 
            ? 'نظام تلقائي' 
            : _collectorNameController.text.trim(),
        'payment_method': _selectedPaymentMethod,
        'notes': _notesController.text.trim(),
      };

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التحصيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(
            Icons.payment,
            color: Color(0xFF4A90E2),
          ),
          const SizedBox(width: 8),
          const Text(
            'إضافة تحصيل',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(0xFF4A90E2),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الفاتورة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'فاتورة رقم: ${widget.invoice['invoice_number']}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'العميل: ${widget.invoice['customer_name'] ?? 'غير محدد'}',
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'المبلغ المتبقي: ${widget.remainingAmount.toStringAsFixed(2)} ج.م',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // المبلغ
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'المبلغ *',
                  prefixIcon: const Icon(
                    Icons.attach_money,
                    color: Color(0xFF4A90E2),
                  ),
                  suffixText: 'ج.م',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (double.parse(value) <= 0) {
                    return 'يجب أن يكون المبلغ أكبر من صفر';
                  }
                  
                  // التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
                  final enteredAmount = double.parse(value);
                  if (enteredAmount > widget.remainingAmount) {
                    return 'لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (${widget.remainingAmount.toStringAsFixed(2)} ج.م)';
                  }
                  
                  return null;
                },
                onChanged: (value) {
                  // التحقق من المبلغ عند الإدخال
                  if (value.isNotEmpty) {
                    final enteredAmount = double.tryParse(value) ?? 0.0;
                    if (enteredAmount > widget.remainingAmount) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'تحذير: المبلغ أعلى من المتبقي في الفاتورة (${widget.remainingAmount.toStringAsFixed(2)} ج.م)',
                              ),
                              backgroundColor: Colors.orange,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      });
                    }
                  }
                },
              ),
              const SizedBox(height: 16),

              // التاريخ
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'التاريخ *',
                    prefixIcon: const Icon(
                      Icons.calendar_today,
                      color: Color(0xFF4A90E2),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  child: Text(
                    DateFormat('yyyy-MM-dd').format(_selectedDate),
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // طريقة الدفع
              DropdownButtonFormField<String>(
                value: _selectedPaymentMethod,
                decoration: InputDecoration(
                  labelText: 'طريقة الدفع *',
                  prefixIcon: const Icon(
                    Icons.payment,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                items: _paymentMethods.map((method) {
                  return DropdownMenuItem<String>(
                    value: method,
                    child: Text(method),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPaymentMethod = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // اسم المحصل
              TextFormField(
                controller: _collectorNameController,
                decoration: InputDecoration(
                  labelText: 'اسم المحصل',
                  hintText: 'اتركه فارغاً للنظام التلقائي',
                  prefixIcon: const Icon(
                    Icons.person,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
              const SizedBox(height: 16),

              // الملاحظات
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'ملاحظات',
                  prefixIcon: const Icon(
                    Icons.note,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text(
            'إلغاء',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCollection,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4A90E2),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Text('حفظ التحصيل'),
        ),
      ],
    );
  }
} 
