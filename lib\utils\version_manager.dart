import 'package:shared_preferences/shared_preferences.dart';

class VersionManager {
  static const String _versionKey = 'app_version';
  static const String _buildNumberKey = 'build_number';
  static const String _lastUpdateKey = 'last_update';
  static const String _updateHistoryKey = 'update_history';

  // الإصدار الحالي
  static const String currentVersion = '1.0.9';
  static const int currentBuildNumber = 11;

  // الحصول على الإصدار المحفوظ
  static Future<String> getStoredVersion() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_versionKey) ?? '0.0.0';
  }

  // الحصول على رقم البناء المحفوظ
  static Future<int> getStoredBuildNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_buildNumberKey) ?? 0;
  }

  // الحصول على تاريخ آخر تحديث
  static Future<String> getLastUpdateDate() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastUpdateKey) ?? '';
  }

  // الحصول على سجل التحديثات
  static Future<List<Map<String, dynamic>>> getUpdateHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getStringList(_updateHistoryKey) ?? [];

    return historyJson.map((json) {
      try {
        // تحويل JSON إلى Map (تبسيط)
        final parts = json.split('|');
        if (parts.length >= 3) {
          return {
            'version': parts[0],
            'build': int.tryParse(parts[1]) ?? 0,
            'date': parts[2],
            'description': parts.length > 3 ? parts[3] : '',
          };
        }
      } catch (e) {
        print('خطأ في تحليل سجل التحديث: $e');
      }
      return <String, dynamic>{};
    }).toList();
  }

  // حفظ الإصدار الحالي
  static Future<void> saveCurrentVersion() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final dateString =
        '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

    await prefs.setString(_versionKey, currentVersion);
    await prefs.setInt(_buildNumberKey, currentBuildNumber);
    await prefs.setString(_lastUpdateKey, dateString);

    // إضافة إلى سجل التحديثات
    await _addToUpdateHistory(currentVersion, currentBuildNumber, dateString);
  }

  // إضافة تحديث إلى السجل
  static Future<void> _addToUpdateHistory(
    String version,
    int build,
    String date,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final history = await getUpdateHistory();

    // التحقق من عدم وجود نسخة مكررة
    final exists = history.any(
      (item) => item['version'] == version && item['build'] == build,
    );

    if (!exists) {
      final updateEntry = '$version|$build|$date|تحديث تلقائي';
      history.add({
        'version': version,
        'build': build,
        'date': date,
        'description': 'تحديث تلقائي',
      });

      // حفظ السجل (أول 50 تحديث فقط)
      final historyStrings = history
          .take(50)
          .map(
            (item) =>
                '${item['version']}|${item['build']}|${item['date']}|${item['description']}',
          )
          .toList();

      await prefs.setStringList(_updateHistoryKey, historyStrings);
    }
  }

  // التحقق من وجود تحديث جديد
  static Future<bool> hasNewVersion() async {
    final storedVersion = await getStoredVersion();
    final storedBuild = await getStoredBuildNumber();

    return storedVersion != currentVersion || storedBuild != currentBuildNumber;
  }

  // الحصول على معلومات الإصدار
  static Future<Map<String, dynamic>> getVersionInfo() async {
    final storedVersion = await getStoredVersion();
    final storedBuild = await getStoredBuildNumber();
    final lastUpdate = await getLastUpdateDate();
    final hasUpdate = await hasNewVersion();

    return {
      'current_version': currentVersion,
      'current_build': currentBuildNumber,
      'stored_version': storedVersion,
      'stored_build': storedBuild,
      'last_update': lastUpdate,
      'has_new_version': hasUpdate,
    };
  }

  // تحديث الإصدار يدوياً
  static Future<void> updateVersion(
    String newVersion,
    int newBuild,
    String description,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final dateString =
        '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

    await prefs.setString(_versionKey, newVersion);
    await prefs.setInt(_buildNumberKey, newBuild);
    await prefs.setString(_lastUpdateKey, dateString);

    // إضافة إلى سجل التحديثات
    await _addToUpdateHistory(newVersion, newBuild, dateString);
  }

  // مسح سجل التحديثات
  static Future<void> clearUpdateHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_updateHistoryKey);
  }
}
