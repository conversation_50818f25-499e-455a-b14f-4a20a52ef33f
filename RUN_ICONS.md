# 🚀 أيقونات تشغيل تطبيق أطلس للمستلزمات الطبية

## 📁 الملفات المتاحة

### 1. `run_app.bat` - ملف التشغيل الأساسي
- **الاستخدام**: انقر نقراً مزدوجاً لتشغيل
- **الميزات**: 
  - واجهة تفاعلية باللغة العربية
  - خيارات متعددة للتشغيل (ويب، Windows، Android)
  - تحقق تلقائي من Flutter
  - تحميل التبعيات تلقائياً

### 2. `run_app.ps1` - ملف PowerShell
- **الاستخدام**: انقر بزر الماوس الأيمن → "Run with PowerShell"
- **الميزات**:
  - ألوان جميلة في Terminal
  - نفس الوظائف مع تصميم أفضل
  - مناسب لمستخدمي PowerShell

### 3. `create_shortcut.vbs` - إنشاء اختصار
- **الاستخدام**: انقر نقراً مزدوجاً لإنشاء اختصار على سطح المكتب
- **الميزات**:
  - ينشئ اختصار على سطح المكتب
  - أيقونة جميلة للتطبيق
  - سهولة الوصول

## 🎯 كيفية الاستخدام

### الطريقة الأولى: التشغيل المباشر
1. انقر نقراً مزدوجاً على `run_app.bat`
2. اختر منصة التشغيل (1-4)
3. انتظر حتى يفتح التطبيق

### الطريقة الثانية: إنشاء اختصار
1. انقر نقراً مزدوجاً على `create_shortcut.vbs`
2. سيتم إنشاء اختصار على سطح المكتب
3. انقر على الاختصار لتشغيل التطبيق

### الطريقة الثالثة: PowerShell
1. انقر بزر الماوس الأيمن على `run_app.ps1`
2. اختر "Run with PowerShell"
3. اتبع التعليمات

## 📋 خيارات التشغيل

### 1. تشغيل على الويب (Chrome) - الأسرع
- ✅ يعمل على أي جهاز
- ✅ لا يحتاج محاكي
- ✅ سريع وسهل

### 2. تشغيل على Windows
- ✅ تطبيق سطح المكتب
- ✅ أداء أفضل
- ✅ واجهة محسنة

### 3. تشغيل على Android
- ⚠️ يحتاج محاكي أو جهاز Android
- ✅ تجربة الهاتف الحقيقية
- ✅ جميع الميزات متاحة

## 🔧 استكشاف الأخطاء

### مشكلة: "Flutter غير مثبت"
**الحل:**
1. تأكد من تثبيت Flutter
2. أضف Flutter إلى PATH
3. أعد تشغيل الكمبيوتر

### مشكلة: "خطأ في تحميل التبعيات"
**الحل:**
1. تأكد من وجود اتصال بالإنترنت
2. جرب: `flutter clean && flutter pub get`

### مشكلة: "لا يمكن تشغيل الملف"
**الحل:**
1. تأكد من أن الملف في مجلد المشروع
2. جرب تشغيل كمسؤول
3. تحقق من إعدادات الأمان

## 📞 بيانات تسجيل الدخول

بعد تشغيل التطبيق:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## 🎉 المميزات

- ✅ **سهولة الاستخدام**: انقر نقراً مزدوجاً للتشغيل
- ✅ **واجهة عربية**: جميع الرسائل باللغة العربية
- ✅ **خيارات متعددة**: ويب، Windows، Android
- ✅ **تحقق تلقائي**: من Flutter والتبعيات
- ✅ **أيقونة جميلة**: على سطح المكتب
- ✅ **دعم كامل**: لجميع المنصات

## 📱 التطبيق جاهز!

بعد تشغيل أي من الملفات، ستحصل على:
- تطبيق متكامل لإدارة المستلزمات الطبية
- واجهة عربية احترافية
- جميع الوظائف المطلوبة
- أداء عالي واستقرار ممتاز

---

**ملاحظة**: جميع الملفات آمنة ومكتوبة خصيصاً لهذا المشروع! 🛡️ 