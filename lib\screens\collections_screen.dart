import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../widgets/atlas_logo.dart';

class CollectionsScreen extends StatefulWidget {
  const CollectionsScreen({super.key});

  @override
  State<CollectionsScreen> createState() => _CollectionsScreenState();
}

class _CollectionsScreenState extends State<CollectionsScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<Map<String, dynamic>> _collections = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCollections();
  }

  Future<void> _loadCollections() async {
    try {
      final collections = await _dbHelper.getCollections();
      setState(() {
        _collections = collections;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المدفوعات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> get _filteredCollections {
    if (_searchQuery.isEmpty) return _collections;
    return _collections.where((collection) {
      final customerName =
          collection['customer_name']?.toString().toLowerCase() ?? '';
      final collectorName =
          collection['collector_name']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();
      return customerName.contains(query) || collectorName.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: Text(
          'التحصيل',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCollections,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في المدفوعات...',
                prefixIcon: const Icon(Icons.search, color: Color(0xFF4A90E2)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4A90E2)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFF4A90E2),
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // قائمة المدفوعات
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
                  )
                : _filteredCollections.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.payment_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? 'لا توجد مدفوعات'
                              : 'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadCollections,
                    color: const Color(0xFF4A90E2),
                    child: ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom:
                            80, // إضافة مساحة في الأسفل لتجنب تداخل FloatingActionButton
                      ),
                      itemCount: _filteredCollections.length,
                      itemBuilder: (context, index) {
                        final collection = _filteredCollections[index];

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(16),
                            leading: const CircleAvatar(
                              backgroundColor: Colors.green,
                              child: Icon(Icons.payment, color: Colors.white),
                            ),
                            title: Text(
                              'دفعة من ${collection['customer_name'] ?? 'غير محدد'}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF4A90E2),
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'التاريخ: ${collection['date']}',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                Text(
                                  'المحصل: ${collection['collector_name']}',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                if (collection['notes'] != null &&
                                    collection['notes'].isNotEmpty)
                                  Text(
                                    'ملاحظات: ${collection['notes']}',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                              ],
                            ),
                            trailing: Text(
                              '${(collection['amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddCollectionScreen(),
            ),
          ).then((_) => _loadCollections());
        },
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class AddCollectionScreen extends StatefulWidget {
  const AddCollectionScreen({super.key});

  @override
  State<AddCollectionScreen> createState() => _AddCollectionScreenState();
}

class _AddCollectionScreenState extends State<AddCollectionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedCustomerId;
  int? _selectedInvoiceId;
  DateTime _selectedDate = DateTime.now();
  List<Map<String, dynamic>> _customers = [];
  List<Map<String, dynamic>> _customerInvoices = [];
  String? _collectorName;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final customers = await DatabaseHelper().getCustomers();
      final currentUserName = await AuthService.getCurrentUserName();
      setState(() {
        _customers = customers;
        _collectorName = currentUserName;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // الحصول على المبلغ المتبقي في الفاتورة المختارة
  double _getSelectedInvoiceRemainingAmount() {
    if (_selectedInvoiceId == null) return 0.0;

    final selectedInvoice = _customerInvoices.firstWhere(
      (inv) => inv['id'] == _selectedInvoiceId,
      orElse: () => {},
    );

    return selectedInvoice['remaining_amount'] ?? 0.0;
  }

  Future<void> _loadCustomerInvoices(int customerId) async {
    try {
      final invoices = await DatabaseHelper().getInvoicesByCustomer(customerId);
      // فلترة الفواتير التي لها مبلغ متبقي
      final unpaidInvoices = invoices
          .where((invoice) => (invoice['remaining_amount'] ?? 0.0) > 0)
          .toList();

      setState(() {
        _customerInvoices = unpaidInvoices;
        _selectedInvoiceId = null;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل فواتير العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveCollection() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCustomerId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedInvoiceId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الفاتورة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);

      // التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
      if (_selectedInvoiceId != null) {
        final selectedInvoice = _customerInvoices.firstWhere(
          (inv) => inv['id'] == _selectedInvoiceId,
          orElse: () => {},
        );
        if (selectedInvoice.isNotEmpty) {
          final remainingAmount = selectedInvoice['remaining_amount'] ?? 0.0;
          if (amount > remainingAmount) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'لا يمكن إضافة مبلغ أعلى من المتبقي (${remainingAmount.toStringAsFixed(2)} ج.م)',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return;
          }
        }
      }

      final collectionData = {
        'customer_id': _selectedCustomerId,
        'invoice_id': _selectedInvoiceId,
        'amount': amount,
        'date': DateFormat('yyyy-MM-dd').format(_selectedDate),
        'collector_name': _collectorName,
        'notes': _notesController.text.trim(),
      };

      await DatabaseHelper().insertCollection(collectionData);

      // تحديث الفاتورة إذا تم اختيارها
      if (_selectedInvoiceId != null) {
        final invoice = _customerInvoices.firstWhere(
          (inv) => inv['id'] == _selectedInvoiceId,
        );
        final newPaidAmount = (invoice['paid_amount'] ?? 0.0) + amount;
        final newRemainingAmount =
            (invoice['total_amount'] ?? 0.0) - newPaidAmount;

        await DatabaseHelper().updateInvoice(_selectedInvoiceId!, {
          'paid_amount': newPaidAmount,
          'remaining_amount': newRemainingAmount,
        });
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل المدفوعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المدفوعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'تسجيل مدفوعة جديدة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // العميل
              DropdownButtonFormField<int>(
                value: _selectedCustomerId,
                decoration: InputDecoration(
                  labelText: 'العميل *',
                  prefixIcon: const Icon(
                    Icons.person,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                items: _customers.map((customer) {
                  return DropdownMenuItem<int>(
                    value: customer['id'],
                    child: Text(customer['name'] ?? ''),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCustomerId = value;
                    _selectedInvoiceId = null;
                    _customerInvoices = [];
                  });
                  if (value != null) {
                    _loadCustomerInvoices(value);
                  }
                },
              ),
              const SizedBox(height: 16),

              // الفاتورة (إجباري)
              DropdownButtonFormField<int>(
                value: _selectedInvoiceId,
                decoration: InputDecoration(
                  labelText: 'الفاتورة *',
                  prefixIcon: const Icon(
                    Icons.receipt,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                items: _customerInvoices.map((invoice) {
                  final invoiceNumber =
                      invoice['invoice_number']?.toString().trim() ??
                      'غير محدد';
                  return DropdownMenuItem<int>(
                    value: invoice['id'],
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'فاتورة  $invoiceNumber - متبقي: ${(invoice['remaining_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedInvoiceId = value;
                  });

                  // التحقق من المبلغ المدخل عند تغيير الفاتورة
                  if (value != null && _amountController.text.isNotEmpty) {
                    final selectedInvoice = _customerInvoices.firstWhere(
                      (inv) => inv['id'] == value,
                      orElse: () => {},
                    );
                    if (selectedInvoice.isNotEmpty) {
                      final remainingAmount =
                          selectedInvoice['remaining_amount'] ?? 0.0;
                      final enteredAmount =
                          double.tryParse(_amountController.text) ?? 0.0;
                      if (enteredAmount > remainingAmount) {
                        // إظهار تحذير للمستخدم
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تحذير: المبلغ المدخل أعلى من المتبقي (${remainingAmount.toStringAsFixed(2)} ج.م)',
                                ),
                                backgroundColor: Colors.orange,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          }
                        });
                      }
                    }
                  }
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار الفاتورة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // التاريخ
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'تاريخ المدفوعة',
                    prefixIcon: const Icon(
                      Icons.calendar_today,
                      color: Color(0xFF4A90E2),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF4A90E2),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  child: Text(DateFormat('yyyy-MM-dd').format(_selectedDate)),
                ),
              ),
              const SizedBox(height: 16),

              // عرض المبلغ المتبقي في الفاتورة المختارة
              if (_selectedInvoiceId != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'المبلغ المتبقي في الفاتورة: ${_getSelectedInvoiceRemainingAmount().toStringAsFixed(2)} ج.م',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              if (_selectedInvoiceId != null) const SizedBox(height: 16),

              // المبلغ
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  // التحقق من المبلغ عند الإدخال
                  if (value.isNotEmpty && _selectedInvoiceId != null) {
                    final selectedInvoice = _customerInvoices.firstWhere(
                      (inv) => inv['id'] == _selectedInvoiceId,
                      orElse: () => {},
                    );
                    if (selectedInvoice.isNotEmpty) {
                      final remainingAmount =
                          selectedInvoice['remaining_amount'] ?? 0.0;
                      final enteredAmount = double.tryParse(value) ?? 0.0;
                      if (enteredAmount > remainingAmount) {
                        // إظهار تحذير للمستخدم
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تحذير: المبلغ أعلى من المتبقي (${remainingAmount.toStringAsFixed(2)} ج.م)',
                                ),
                                backgroundColor: Colors.orange,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          }
                        });
                      }
                    }
                  }
                },
                decoration: InputDecoration(
                  labelText: 'المبلغ *',
                  prefixIcon: const Icon(
                    Icons.attach_money,
                    color: Color(0xFF4A90E2),
                  ),
                  suffixText: 'ج.م',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (double.parse(value) <= 0) {
                    return 'يجب أن يكون المبلغ أكبر من صفر';
                  }

                  // التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
                  if (_selectedInvoiceId != null) {
                    final selectedInvoice = _customerInvoices.firstWhere(
                      (inv) => inv['id'] == _selectedInvoiceId,
                      orElse: () => {},
                    );
                    if (selectedInvoice.isNotEmpty) {
                      final remainingAmount =
                          selectedInvoice['remaining_amount'] ?? 0.0;
                      final enteredAmount = double.parse(value);
                      if (enteredAmount > remainingAmount) {
                        return 'لا يمكن إضافة مبلغ أعلى من المتبقي (${remainingAmount.toStringAsFixed(2)} ج.م)';
                      }
                    }
                  }

                  return null;
                },
              ),
              const SizedBox(height: 16),

              // المحصل
              TextFormField(
                initialValue: _collectorName,
                enabled: false,
                decoration: InputDecoration(
                  labelText: 'المحصل',
                  prefixIcon: const Icon(
                    Icons.person_outline,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              const SizedBox(height: 16),

              // الملاحظات
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'ملاحظات',
                  prefixIcon: const Icon(Icons.note, color: Color(0xFF4A90E2)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveCollection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 3,
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'تسجيل المدفوعة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
