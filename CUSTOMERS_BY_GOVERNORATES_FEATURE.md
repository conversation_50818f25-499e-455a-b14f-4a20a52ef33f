# 🏛️ ميزة العملاء حسب المحافظات مع التحكم الكامل

## 📋 نظرة عامة

تم تعديل قسم العملاء ليكون مقسماً حسب المحافظات مع إضافة التحكم الكامل في العميل وميزة إرسال تقرير التحصيلات. تم حذف قسم المحافظات المنفصل ودمجه في قسم العملاء.

## 🚀 الميزات المضافة

### 1. **تقسيم العملاء حسب المحافظات**
- عرض المحافظات مع عدد العملاء في كل محافظة
- الانتقال إلى قائمة العملاء في كل محافظة
- إحصائيات فورية لكل محافظة

### 2. **التحكم الكامل في العميل**
- عرض تفاصيل العميل
- تعديل بيانات العميل
- حذف العميل
- إرسال تقرير التحصيلات

### 3. **ميزة إرسال تقرير التحصيلات**
- إرسال جميع التحصيلات للعميل عبر واتساب
- ترتيب التحصيلات حسب التاريخ (الأحدث أولاً)
- عرض تفاصيل كاملة لكل تحصيل
- حساب إجمالي التحصيلات

## 🔧 التفاصيل التقنية

### 1. **تعديل شاشة العملاء الرئيسية**
```dart
class _CustomersScreenState extends State<CustomersScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<String> _governorates = [];
  Map<String, int> _customerCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGovernorates();
  }

  Future<void> _loadGovernorates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final governorates = await _dbHelper.getGovernorates();
      final counts = await _dbHelper.getCustomerCountByGovernorate();

      setState(() {
        _governorates = governorates;
        _customerCounts = counts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المحافظات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
```

### 2. **شاشة العملاء حسب المحافظة**
```dart
class CustomersByGovernorateScreen extends StatefulWidget {
  final String governorate;

  const CustomersByGovernorateScreen({super.key, required this.governorate});

  @override
  State<CustomersByGovernorateScreen> createState() =>
      _CustomersByGovernorateScreenState();
}
```

### 3. **ميزة إرسال تقرير التحصيلات**
```dart
Future<void> _sendCollectionsReport(Map<String, dynamic> customer) async {
  try {
    // الحصول على جميع التحصيلات للعميل
    final collections = await _databaseHelper.getCollectionsByCustomer(customer['id']);
    
    if (collections.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد تحصيلات لهذا العميل'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // ترتيب التحصيلات حسب التاريخ
    collections.sort((a, b) => DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));

    // الحصول على رقم هاتف العميل
    final phone = customer['phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يوجد رقم هاتف للعميل'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // تنسيق رسالة التحصيلات
    String message = 'تقرير التحصيلات للعميل: ${customer['name']}\n\n';
    
    double totalAmount = 0;
    for (var collection in collections) {
      final date = collection['date'];
      final amount = collection['amount'] ?? 0.0;
      final invoiceNumber = collection['invoice_id'] ?? 'غير محدد';
      final paymentMethod = collection['payment_method'] ?? 'نقداً';
      final collectorName = collection['collector_name'] ?? 'غير محدد';
      final notes = collection['notes'] ?? '';

      message += '📅 التاريخ: $date\n';
      message += '💰 المبلغ: ${amount.toStringAsFixed(2)} ج.م\n';
      message += '📄 رقم الفاتورة: $invoiceNumber\n';
      message += '💳 طريقة الدفع: $paymentMethod\n';
      message += '👤 المحصل: $collectorName\n';
      if (notes.isNotEmpty) {
        message += '📝 ملاحظات: $notes\n';
      }
      message += '─────────────────\n';
      
      totalAmount += amount;
    }
    
    message += '\n💰 إجمالي التحصيلات: ${totalAmount.toStringAsFixed(2)} ج.م';

    // إرسال الرسالة عبر واتساب
    final success = await MessagingService.sendCustomMessage(
      phone: phone,
      message: message,
      title: 'تقرير التحصيلات',
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال تقرير التحصيلات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في إرسال تقرير التحصيلات'),
          backgroundColor: Colors.red,
        ),
      );
    }
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إرسال تقرير التحصيلات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
```

### 4. **إضافة دالة إرسال رسالة مخصصة**
```dart
// في MessagingService
static Future<bool> sendCustomMessage({
  required String phone,
  required String message,
  String title = 'رسالة من أطلس',
}) async {
  try {
    // إزالة الرموز من رقم الهاتف
    final cleanPhone = _cleanPhoneNumber(phone);
    
    // إضافة العنوان للرسالة
    final fullMessage = '$title\n\n$message';
    
    // إنشاء رابط WhatsApp
    final whatsappUrl = 'https://wa.me/$cleanPhone?text=${Uri.encodeComponent(fullMessage)}';
    
    // فتح WhatsApp
    if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
      await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.externalApplication);
      return true;
    } else {
      print('❌ لا يمكن فتح WhatsApp');
      return false;
    }
  } catch (e) {
    print('❌ خطأ في إرسال الرسالة المخصصة عبر WhatsApp: $e');
    return false;
  }
}
```

## 📱 واجهة المستخدم

### 1. **شاشة العملاء الرئيسية**
- عرض المحافظات مع عدد العملاء
- تصميم بطاقات جذاب
- إمكانية التحديث
- زر إضافة عميل جديد

### 2. **شاشة العملاء حسب المحافظة**
- قائمة العملاء في المحافظة المحددة
- البحث في العملاء
- خيارات متعددة لكل عميل:
  - تفاصيل العميل
  - تعديل العميل
  - إرسال تقرير التحصيلات
  - حذف العميل

### 3. **تقرير التحصيلات**
- ترتيب حسب التاريخ (الأحدث أولاً)
- عرض تفاصيل كاملة:
  - التاريخ
  - المبلغ
  - رقم الفاتورة
  - طريقة الدفع
  - المحصل
  - الملاحظات
- إجمالي التحصيلات

## 🎯 سيناريوهات الاستخدام

### 1. **عرض العملاء حسب المحافظة**
- المستخدم يفتح قسم العملاء
- يرى قائمة المحافظات مع عدد العملاء
- يختار محافظة معينة
- يرى قائمة العملاء في هذه المحافظة

### 2. **التحكم في العميل**
- المستخدم يضغط على عميل معين
- يختار من القائمة:
  - تفاصيل العميل (عرض كامل المعلومات)
  - تعديل العميل (تعديل البيانات)
  - إرسال تقرير التحصيلات
  - حذف العميل

### 3. **إرسال تقرير التحصيلات**
- المستخدم يختار "إرسال تقرير التحصيلات"
- النظام يجمع جميع التحصيلات للعميل
- يرتبها حسب التاريخ (الأحدث أولاً)
- يرسل التقرير عبر واتساب
- يعرض رسالة نجاح أو فشل

## ⚠️ ملاحظات مهمة

### 1. **حذف قسم المحافظات المنفصل**
- تم دمج قسم المحافظات في قسم العملاء
- تم حذف الرابط من لوحة التحكم
- تم حذف الاستيراد من الملفات

### 2. **ترتيب التحصيلات**
- التحصيلات مرتبة حسب التاريخ (الأحدث أولاً)
- عرض تفاصيل كاملة لكل تحصيل
- حساب إجمالي التحصيلات

### 3. **الأمان**
- التحقق من وجود رقم هاتف للعميل
- التحقق من وجود تحصيلات
- معالجة الأخطاء بشكل مناسب

## 🧪 اختبار الميزة

### 1. **اختبار عرض المحافظات**
```dart
// اختبار تحميل المحافظات
final governorates = await _dbHelper.getGovernorates();
final counts = await _dbHelper.getCustomerCountByGovernorate();

assert(governorates.isNotEmpty);
assert(counts.isNotEmpty);
```

### 2. **اختبار إرسال تقرير التحصيلات**
```dart
// اختبار إرسال تقرير التحصيلات
final customer = {
  'id': 1,
  'name': 'عميل تجريبي',
  'phone': '0123456789',
};

final collections = await _databaseHelper.getCollectionsByCustomer(customer['id']);
collections.sort((a, b) => DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));

assert(collections.isNotEmpty);
```

### 3. **اختبار ترتيب التحصيلات**
```dart
// اختبار ترتيب التحصيلات حسب التاريخ
final sortedCollections = collections.sort((a, b) => 
  DateTime.parse(b['date']).compareTo(DateTime.parse(a['date']))
);

// التحقق من أن الأحدث أولاً
final firstDate = DateTime.parse(sortedCollections.first['date']);
final lastDate = DateTime.parse(sortedCollections.last['date']);
assert(firstDate.isAfter(lastDate));
```

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | تعديل شاشة العملاء لتكون مقسمة حسب المحافظات | `customers_screen.dart` |
| 2024-01-15 | إضافة ميزة إرسال تقرير التحصيلات | `customers_screen.dart` |
| 2024-01-15 | إضافة دالة sendCustomMessage | `messaging_service.dart` |
| 2024-01-15 | حذف قسم المحافظات من لوحة التحكم | `dashboard_screen.dart` |

## 🔮 التطويرات المستقبلية

### 1. **ميزات إضافية**
- تصفية العملاء حسب المنطقة
- إحصائيات مفصلة لكل محافظة
- تصدير تقارير التحصيلات

### 2. **تحسينات**
- إضافة رسوم بيانية للإحصائيات
- ميزة الطباعة للتقارير
- إشعارات للعملاء الجدد

### 3. **أمان**
- صلاحيات محددة للمستخدمين
- سجل العمليات
- نسخ احتياطية

## 💡 نصائح للاستخدام

### 1. **للمستخدمين**
- استخدم البحث للعثور على عملاء محددة
- راجع تفاصيل العميل قبل الإرسال
- تأكد من صحة رقم الهاتف

### 2. **للمديرين**
- راجع تقارير التحصيلات بانتظام
- تأكد من صحة البيانات
- استخدم الإحصائيات للمتابعة

### 3. **للمطورين**
- اختبر الميزة في بيئة التطوير
- تأكد من تحديث قاعدة البيانات
- وثق أي تغييرات إضافية

## 🎉 النتيجة النهائية

الآن قسم العملاء مقسم حسب المحافظات مع التحكم الكامل وإمكانية إرسال تقارير التحصيلات! 🚀

### الميزات الرئيسية:
- ✅ تقسيم العملاء حسب المحافظات
- ✅ التحكم الكامل في العميل
- ✅ إرسال تقرير التحصيلات عبر واتساب
- ✅ ترتيب التحصيلات حسب التاريخ
- ✅ عرض تفاصيل كاملة للتحصيلات
- ✅ حذف قسم المحافظات المنفصل
- ✅ واجهة مستخدم سهلة ومفهومة 