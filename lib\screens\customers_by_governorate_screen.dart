import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import 'customer_details_screen.dart';

class CustomersByGovernorateScreen extends StatefulWidget {
  final String governorate;

  const CustomersByGovernorateScreen({super.key, required this.governorate});

  @override
  State<CustomersByGovernorateScreen> createState() =>
      _CustomersByGovernorateScreenState();
}

class _CustomersByGovernorateScreenState
    extends State<CustomersByGovernorateScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<Map<String, dynamic>> _customers = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final allCustomers = await _dbHelper.getCustomers();
      final filteredCustomers = allCustomers.where((customer) {
        return customer['governorate'] == widget.governorate;
      }).toList();

      setState(() {
        _customers = filteredCustomers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملاء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> get _filteredCustomers {
    if (_searchQuery.isEmpty) return _customers;
    return _customers.where((customer) {
      final name = customer['name']?.toString().toLowerCase() ?? '';
      final phone = customer['primary_phone']?.toString().toLowerCase() ?? '';
      final address = customer['address']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();

      return name.contains(query) ||
          phone.contains(query) ||
          address.contains(query);
    }).toList();
  }

  void _createInvoiceForCustomer(Map<String, dynamic> customer) {
    Navigator.pushNamed(
      context,
      '/add-invoice',
      arguments: {'selectedCustomer': customer},
    ).then((_) => _loadCustomers());
  }

  void _viewCustomerInvoices(Map<String, dynamic> customer) {
    Navigator.pushNamed(context, '/customer-invoices', arguments: customer);
  }

  void _editCustomer(Map<String, dynamic> customer) {
    Navigator.pushNamed(
      context,
      '/add-edit-customer',
      arguments: customer,
    ).then((_) => _loadCustomers());
  }

  void _viewCustomerDetails(Map<String, dynamic> customer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailsScreen(customer: customer),
      ),
    ).then((_) => _loadCustomers());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: Text('عملاء ${widget.governorate}'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCustomers,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في العملاء...',
                prefixIcon: const Icon(Icons.search, color: Color(0xFF4A90E2)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4A90E2)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFF4A90E2),
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // معلومات المحافظة
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const CircleAvatar(
                  backgroundColor: Color(0xFF4A90E2),
                  child: Icon(Icons.location_city, color: Colors.white),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.governorate,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4A90E2),
                        ),
                      ),
                      Text(
                        '${_customers.length} عميل',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // قائمة العملاء
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
                  )
                : _filteredCustomers.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _searchQuery.isEmpty
                              ? Icons.people_outline
                              : Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? 'لا يوجد عملاء في هذه المحافظة'
                              : 'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (_searchQuery.isEmpty) ...[
                          const SizedBox(height: 8),
                          const Text(
                            'اضغط على + لإضافة عميل جديد',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                        ],
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadCustomers,
                    color: const Color(0xFF4A90E2),
                    child: ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom:
                            80, // إضافة مساحة في الأسفل لتجنب تداخل FloatingActionButton
                      ),
                      itemCount: _filteredCustomers.length,
                      itemBuilder: (context, index) {
                        final customer = _filteredCustomers[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: InkWell(
                            onTap: () => _viewCustomerDetails(customer),
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  // صورة العميل
                                  CircleAvatar(
                                    backgroundColor: const Color(0xFF4A90E2),
                                    radius: 25,
                                    child: Text(
                                      (customer['name'] ?? 'C')
                                          .toString()
                                          .substring(0, 1)
                                          .toUpperCase(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  
                                  // معلومات العميل
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          customer['name'] ?? 'غير محدد',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: Color(0xFF4A90E2),
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        if (customer['primary_phone'] != null)
                                          Row(
                                            children: [
                                              const Icon(
                                                Icons.phone,
                                                size: 14,
                                                color: Colors.grey,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                customer['primary_phone'],
                                                style: const TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ],
                                          ),
                                        if (customer['address'] != null)
                                          Row(
                                            children: [
                                              const Icon(
                                                Icons.location_on,
                                                size: 14,
                                                color: Colors.grey,
                                              ),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                child: Text(
                                                  customer['address'],
                                                  style: const TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 14,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        if (customer['area'] != null)
                                          Row(
                                            children: [
                                              const Icon(
                                                Icons.location_city,
                                                size: 14,
                                                color: Colors.grey,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                customer['area'],
                                                style: const TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ],
                                          ),
                                      ],
                                    ),
                                  ),
                                  
                                  // الأزرار السريعة
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // زر إنشاء فاتورة
                                      IconButton(
                                        onPressed: () => _createInvoiceForCustomer(customer),
                                        icon: const Icon(
                                          Icons.add_shopping_cart,
                                          color: Color(0xFF4A90E2),
                                        ),
                                        tooltip: 'إنشاء فاتورة',
                                      ),
                                      // زر القائمة المنسدلة
                                      PopupMenuButton<String>(
                                        icon: const Icon(Icons.more_vert),
                                        onSelected: (value) {
                                          if (value == 'view_invoices') {
                                            _viewCustomerInvoices(customer);
                                          } else if (value == 'edit') {
                                            _editCustomer(customer);
                                          }
                                        },
                                        itemBuilder: (context) => [
                                          const PopupMenuItem(
                                            value: 'view_invoices',
                                            child: Row(
                                              children: [
                                                Icon(Icons.receipt, color: Colors.blue),
                                                SizedBox(width: 8),
                                                Expanded(child: Text('عرض الفواتير')),
                                              ],
                                            ),
                                          ),
                                          const PopupMenuItem(
                                            value: 'edit',
                                            child: Row(
                                              children: [
                                                Icon(Icons.edit, color: Colors.orange),
                                                SizedBox(width: 8),
                                                Expanded(child: Text('تعديل العميل')),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(
            context,
            '/add-edit-customer',
            arguments: {'preSelectedGovernorate': widget.governorate},
          ).then((_) => _loadCustomers());
        },
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        child: const Icon(Icons.person_add),
        tooltip: 'إضافة عميل جديد',
      ),
    );
  }
}
