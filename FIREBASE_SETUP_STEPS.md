# 🔥 دليل ربط Firebase خطوة بخطوة

## 📋 الخطوات المطلوبة:

### 1️⃣ إنشاء مشروع Firebase

1. **اذهب إلى Firebase Console:**
   - افتح المتصفح واذهب إلى: https://console.firebase.google.com/
   - سجل دخول بحساب Google

2. **إنشاء مشروع جديد:**
   - انقر على "إنشاء مشروع" أو "Create project"
   - أدخل اسم المشروع: `atlas-medical-supplies`
   - انقر "Continue"

3. **إعدادات المشروع:**
   - يمكنك إيقاف Google Analytics (اختياري)
   - انقر "Create project"

### 2️⃣ إضافة تطبيق Android

1. **في Firebase Console:**
   - انقر على أيقونة Android (📱)
   - أدخل Package name: `com.atlas.medical.atlas_medical_supplies`
   - أدخل App nickname: `Atlas Medical Supplies`
   - انقر "Register app"

2. **تحميل ملف google-services.json:**
   - سيتم تحميل ملف `google-services.json`
   - احفظ الملف في مجلد: `android/app/google-services.json`
   - استبدل الملف الموجود

### 3️⃣ تفعيل Authentication

1. **في Firebase Console:**
   - اذهب إلى "Authentication" من القائمة الجانبية
   - انقر على "Get started"

2. **تفعيل Sign-in methods:**
   - انقر على "Email/Password"
   - فعّل "Email/Password"
   - انقر "Save"

### 4️⃣ إنشاء Firestore Database

1. **في Firebase Console:**
   - اذهب إلى "Firestore Database"
   - انقر على "Create database"

2. **إعدادات قاعدة البيانات:**
   - اختر "Start in test mode" (للاختبار)
   - اختر موقع قاعدة البيانات (مثل us-central1)
   - انقر "Done"

### 5️⃣ تحديث firebase_options.dart

1. **في Firebase Console:**
   - اذهب إلى "Project settings" (⚙️)
   - انقر على "General" tab
   - انزل إلى "Your apps" section

2. **نسخ الإعدادات:**
   - انقر على تطبيق Android
   - انسخ `apiKey`, `appId`, `messagingSenderId`, `projectId`

3. **تحديث الملف:**
   - افتح `lib/firebase_options.dart`
   - استبدل القيم بالقيم الحقيقية

### 6️⃣ اختبار الربط

1. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

2. **تسجيل الدخول:**
   - استخدم رقم الهاتف: `01125312343`
   - كلمة المرور: `123456`

3. **التحقق من Firebase:**
   - اذهب إلى Firestore Database
   - ستجد collections جديدة: `users`, `customers`, `invoices`, `collections`

## ✅ بعد الربط:

- ✅ البيانات ستُحفظ في السحابة
- ✅ يمكن الوصول للبيانات من أي جهاز
- ✅ النسخ الاحتياطي التلقائي
- ✅ المزامنة التلقائية

## 🚨 ملاحظات مهمة:

1. **لا تشارك API keys** مع أي شخص
2. **غيّر قواعد الأمان** في Firestore قبل الإنتاج
3. **راقب الاستخدام** في Firebase Console
4. **احتفظ بنسخة احتياطية** من البيانات

## 📞 للمساعدة:

إذا واجهت أي مشاكل:
- راجع [Firebase Documentation](https://firebase.google.com/docs)
- راجع [FlutterFire Documentation](https://firebase.flutter.dev/)
- تأكد من أن جميع الخطوات تمت بشكل صحيح

## 🔧 إعدادات إضافية:

### قواعد الأمان في Firestore:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### تفعيل Authentication للمستخدمين الجدد:
- في Firebase Console > Authentication > Users
- يمكنك إضافة مستخدمين يدوياً أو السماح بالتسجيل التلقائي 