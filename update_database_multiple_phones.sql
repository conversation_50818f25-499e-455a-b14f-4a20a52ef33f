-- تحديث قاعدة البيانات لدعم أرقام الهواتف المتعددة
-- تاريخ الإنشاء: 2024-12-19
-- الإصدار: 11

-- 1. إن<PERSON><PERSON><PERSON> جدول أرقام الهواتف للعملاء
CREATE TABLE IF NOT EXISTS customer_phones (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    phone TEXT NOT NULL,
    phone_type TEXT DEFAULT 'الرئيسي',
    is_primary INTEGER DEFAULT 0,
    notes TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
);

-- 2. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_customer_phones_customer_id ON customer_phones(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_phones_phone ON customer_phones(phone);
CREATE INDEX IF NOT EXISTS idx_customer_phones_is_primary ON customer_phones(is_primary);

-- 3. نقل أرقام الهواتف الموجودة من جدول العملاء إلى الجدول الجديد
INSERT INTO customer_phones (customer_id, phone, phone_type, is_primary, notes, created_at)
SELECT 
    id as customer_id,
    phone,
    'الرئيسي' as phone_type,
    1 as is_primary,
    'رقم الهاتف الأساسي' as notes,
    created_at
FROM customers 
WHERE phone IS NOT NULL AND phone != '';

-- 4. التحقق من نقل البيانات
SELECT 
    'إجمالي العملاء' as description,
    COUNT(*) as count
FROM customers
UNION ALL
SELECT 
    'العملاء بأرقام هواتف' as description,
    COUNT(*) as count
FROM customers 
WHERE phone IS NOT NULL AND phone != ''
UNION ALL
SELECT 
    'أرقام الهواتف المنقولة' as description,
    COUNT(*) as count
FROM customer_phones;

-- 5. عرض عينة من البيانات المنقولة
SELECT 
    c.id as customer_id,
    c.name as customer_name,
    c.phone as old_phone,
    cp.phone as new_phone,
    cp.phone_type,
    cp.is_primary,
    cp.created_at
FROM customers c
LEFT JOIN customer_phones cp ON c.id = cp.customer_id
WHERE c.phone IS NOT NULL AND c.phone != ''
ORDER BY c.name
LIMIT 10;

-- 6. إحصائيات أرقام الهواتف حسب النوع
SELECT 
    phone_type,
    COUNT(*) as count
FROM customer_phones
GROUP BY phone_type
ORDER BY count DESC;

-- 7. العملاء بدون أرقام هواتف
SELECT 
    id,
    name,
    governorate,
    area,
    created_at
FROM customers 
WHERE phone IS NULL OR phone = ''
ORDER BY name;

-- 8. التحقق من صحة البيانات
SELECT 
    'العملاء بدون أرقام هواتف' as check_type,
    COUNT(*) as count
FROM customers 
WHERE phone IS NULL OR phone = ''
UNION ALL
SELECT 
    'أرقام هواتف أساسية' as check_type,
    COUNT(*) as count
FROM customer_phones 
WHERE is_primary = 1
UNION ALL
SELECT 
    'أرقام هواتف ثانوية' as check_type,
    COUNT(*) as count
FROM customer_phones 
WHERE is_primary = 0;

-- 9. العملاء بأكثر من رقم هاتف (للمستقبل)
SELECT 
    customer_id,
    COUNT(*) as phone_count
FROM customer_phones
GROUP BY customer_id
HAVING COUNT(*) > 1
ORDER BY phone_count DESC;

-- 10. تحديث إصدار قاعدة البيانات (إذا كان هناك جدول للإصدارات)
-- CREATE TABLE IF NOT EXISTS database_version (
--     version INTEGER PRIMARY KEY,
--     updated_at TEXT DEFAULT CURRENT_TIMESTAMP
-- );
-- INSERT OR REPLACE INTO database_version (version) VALUES (11);

-- ملاحظات:
-- 1. هذا السكريبت آمن للتشغيل عدة مرات (IF NOT EXISTS)
-- 2. يتم نقل البيانات الموجودة تلقائياً
-- 3. جميع البيانات الأصلية محفوظة
-- 4. يمكن التراجع عن التحديث إذا لزم الأمر 