# 💰 ميزة إضافة التحصيل في كل فاتورة

## 📋 نظرة عامة

تم إضافة ميزة التحصيل المباشر من قائمة الفواتير. هذه الميزة تسمح بإضافة تحصيل جديد لأي فاتورة مباشرة من قائمة الفواتير دون الحاجة للانتقال إلى شاشة التحصيل المنفصلة.

## 🚀 الميزات المضافة

### 1. **إضافة تحصيل مباشر من الفاتورة**
- خيار "إضافة تحصيل" في قائمة كل فاتورة
- نافذة مخصصة لإدخال بيانات التحصيل
- تحديث تلقائي للفاتورة بعد التحصيل

### 2. **نافذة تحصيل متقدمة**
- عرض معلومات الفاتورة المختارة
- التحقق من المبلغ المتبقي
- خيارات متعددة لطريقة الدفع
- إدخال اسم المحصل والملاحظات

### 3. **التحقق والتحذيرات**
- التحقق من عدم تجاوز المبلغ المتبقي
- تحذيرات فورية أثناء الإدخال
- رسائل خطأ واضحة

## 🔧 التفاصيل التقنية

### 1. **إضافة خيار التحصيل في قائمة الفواتير**
```dart
// في PopupMenuButton للفواتير
if (!isPaid)
  const PopupMenuItem(
    value: 'collect',
    child: Row(
      children: [
        Icon(
          Icons.payment,
          color: Colors.green,
        ),
        SizedBox(width: 8),
        Text('إضافة تحصيل'),
      ],
    ),
  ),
```

### 2. **معالجة اختيار التحصيل**
```dart
trailing: PopupMenuButton<String>(
  onSelected: (value) {
    if (value == 'edit') {
      _editInvoice(invoice);
    } else if (value == 'collect') {
      _addCollectionForInvoice(invoice);  // ← إضافة التحصيل
    } else if (value == 'whatsapp') {
      _sendInvoiceViaWhatsApp(invoice);
    }
    // ... باقي الخيارات
  },
  // ...
),
```

### 3. **دالة إضافة التحصيل**
```dart
Future<void> _addCollectionForInvoice(Map<String, dynamic> invoice) async {
  final remainingAmount = invoice['remaining_amount'] ?? 0.0;
  
  if (remainingAmount <= 0) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('هذه الفاتورة مدفوعة بالكامل'),
        backgroundColor: Colors.orange,
      ),
    );
    return;
  }

  // عرض نافذة إضافة التحصيل
  final result = await showDialog<Map<String, dynamic>>(
    context: context,
    builder: (context) => CollectionDialog(
      invoice: invoice,
      remainingAmount: remainingAmount,
    ),
  );

  if (result != null) {
    try {
      final currentUserId = await AuthService.getCurrentUserId();
      final currentUserName = await AuthService.getCurrentUserName();

      final collectionData = {
        'customer_id': invoice['customer_id'],
        'invoice_id': invoice['id'],
        'amount': result['amount'],
        'date': result['date'],
        'collector_name': result['collector_name'],
        'payment_method': result['payment_method'],
        'notes': result['notes'],
        'created_by': currentUserId,
        'customer_name': invoice['customer_name'],
      };

      await DatabaseHelper().insertCollection(collectionData);

      // تحديث الفاتورة
      final newPaidAmount = (invoice['paid_amount'] ?? 0.0) + result['amount'];
      final newRemainingAmount = (invoice['total_amount'] ?? 0.0) - newPaidAmount;

      await DatabaseHelper().updateInvoice(invoice['id'], {
        'paid_amount': newPaidAmount,
        'remaining_amount': newRemainingAmount,
      });

      // إعادة تحميل الفواتير
      _loadInvoices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة التحصيل بنجاح: ${result['amount'].toStringAsFixed(2)} ج.م'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة التحصيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
```

### 4. **نافذة التحصيل المخصصة**
```dart
class CollectionDialog extends StatefulWidget {
  final Map<String, dynamic> invoice;
  final double remainingAmount;

  const CollectionDialog({
    Key? key,
    required this.invoice,
    required this.remainingAmount,
  }) : super(key: key);

  @override
  State<CollectionDialog> createState() => _CollectionDialogState();
}
```

### 5. **التحقق من المبلغ**
```dart
validator: (value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال المبلغ';
  }
  if (double.tryParse(value) == null) {
    return 'يرجى إدخال رقم صحيح';
  }
  if (double.parse(value) <= 0) {
    return 'يجب أن يكون المبلغ أكبر من صفر';
  }
  
  // التحقق من عدم تجاوز المبلغ للفاتورة المتبقية
  final enteredAmount = double.parse(value);
  if (enteredAmount > widget.remainingAmount) {
    return 'لا يمكن إضافة مبلغ أعلى من المتبقي في الفاتورة (${widget.remainingAmount.toStringAsFixed(2)} ج.م)';
  }
  
  return null;
},
```

## 📱 واجهة المستخدم

### 1. **قائمة الفواتير**
- خيار "إضافة تحصيل" يظهر فقط للفواتير غير المدفوعة
- أيقونة خضراء للتحصيل
- موضع مناسب في القائمة

### 2. **نافذة التحصيل**
- عرض معلومات الفاتورة المختارة
- حقول إدخال منظمة وواضحة
- تحقق فوري من المبلغ
- تصميم متجاوب

### 3. **معلومات الفاتورة**
```dart
Container(
  padding: const EdgeInsets.all(12),
  decoration: BoxDecoration(
    color: Colors.blue.shade50,
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: Colors.blue.shade200),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'فاتورة رقم: ${widget.invoice['invoice_number']}',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      const SizedBox(height: 4),
      Text(
        'العميل: ${widget.invoice['customer_name'] ?? 'غير محدد'}',
      ),
      const SizedBox(height: 4),
      Text(
        'المبلغ المتبقي: ${widget.remainingAmount.toStringAsFixed(2)} ج.م',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.red,
        ),
      ),
    ],
  ),
),
```

## 🎯 سيناريوهات الاستخدام

### 1. **إضافة تحصيل عادي**
- المستخدم يفتح قائمة الفواتير
- يختار فاتورة غير مدفوعة
- يضغط على "إضافة تحصيل"
- يدخل المبلغ وبيانات التحصيل
- يحفظ التحصيل

### 2. **تحصيل كامل الفاتورة**
- المستخدم يختار فاتورة متبقى فيها مبلغ
- يضغط على "إضافة تحصيل"
- يدخل المبلغ المتبقي بالكامل
- يحفظ التحصيل وتصبح الفاتورة مدفوعة

### 3. **تحصيل جزئي**
- المستخدم يختار فاتورة كبيرة
- يضغط على "إضافة تحصيل"
- يدخل مبلغ جزئي
- يحفظ التحصيل ويبقى مبلغ متبقي

## ⚠️ ملاحظات مهمة

### 1. **التحقق من المبلغ**
- لا يمكن تحصيل مبلغ أكبر من المتبقي
- تحذيرات فورية أثناء الإدخال
- رسائل خطأ واضحة

### 2. **تحديث الفاتورة**
- تحديث تلقائي للمبلغ المدفوع
- تحديث تلقائي للمبلغ المتبقي
- إعادة تحميل قائمة الفواتير

### 3. **الأمان**
- التحقق من صحة البيانات
- حفظ معرف المستخدم
- حماية من التلاعب

## 🧪 اختبار الميزة

### 1. **اختبار التحصيل العادي**
```dart
// اختبار إضافة تحصيل عادي
final invoice = {
  'id': 1,
  'invoice_number': 'INV-001',
  'total_amount': 1000.0,
  'paid_amount': 0.0,
  'remaining_amount': 1000.0,
  'customer_id': 1,
  'customer_name': 'عميل تجريبي',
};

final collectionData = {
  'amount': 500.0,
  'date': '2024-01-15',
  'collector_name': 'محمد',
  'payment_method': 'نقداً',
  'notes': 'تحصيل جزئي',
};

// يجب أن ينجح التحصيل
```

### 2. **اختبار تجاوز المبلغ**
```dart
// اختبار تحصيل مبلغ أكبر من المتبقي
final remainingAmount = 500.0;
final enteredAmount = 600.0;

if (enteredAmount > remainingAmount) {
  // يجب أن يفشل التحصيل
  print('خطأ: المبلغ أكبر من المتبقي');
}
```

### 3. **اختبار الفاتورة المدفوعة**
```dart
// اختبار تحصيل فاتورة مدفوعة
final invoice = {
  'remaining_amount': 0.0,
};

if (invoice['remaining_amount'] <= 0) {
  // يجب أن يظهر تحذير
  print('هذه الفاتورة مدفوعة بالكامل');
}
```

## 📝 سجل التغييرات

| التاريخ | التغيير | الملف |
|---------|---------|-------|
| 2024-01-15 | إضافة خيار التحصيل في قائمة الفواتير | `invoices_screen.dart` |
| 2024-01-15 | إنشاء نافذة التحصيل المخصصة | `collection_dialog.dart` |
| 2024-01-15 | إضافة دالة معالجة التحصيل | `invoices_screen.dart` |
| 2024-01-15 | إضافة التحقق من المبلغ | `collection_dialog.dart` |

## 🔮 التطويرات المستقبلية

### 1. **ميزات إضافية**
- تحصيل متعدد للفواتير
- جدولة التحصيل
- إشعارات التحصيل

### 2. **تحسينات**
- إضافة المزيد من طرق الدفع
- تقارير التحصيل
- إحصائيات مفصلة

### 3. **أمان**
- تسجيل كامل للتحصيلات
- صلاحيات محددة
- مراجعة التحصيلات

## 💡 نصائح للاستخدام

### 1. **للمستخدمين**
- تأكد من المبلغ المتبقي قبل التحصيل
- استخدم الملاحظات لتوضيح التفاصيل
- راجع البيانات قبل الحفظ

### 2. **للمديرين**
- راجع التحصيلات بانتظام
- تأكد من صحة البيانات
- استخدم التقارير للمتابعة

### 3. **للمطورين**
- اختبر الميزة في بيئة التطوير
- تأكد من تحديث قاعدة البيانات
- وثق أي تغييرات إضافية

## 🎉 النتيجة النهائية

الآن يمكن للمستخدمين إضافة تحصيل جديد لأي فاتورة مباشرة من قائمة الفواتير بسهولة وسرعة! 🚀

### الميزات الرئيسية:
- ✅ إضافة تحصيل مباشر من الفاتورة
- ✅ نافذة مخصصة ومتقدمة
- ✅ التحقق من المبلغ المتبقي
- ✅ تحديث تلقائي للفاتورة
- ✅ واجهة مستخدم سهلة ومفهومة 