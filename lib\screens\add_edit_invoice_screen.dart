import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/invoice_message_service.dart';

class AddEditInvoiceScreen extends StatefulWidget {
  final Map<String, dynamic>? invoice;

  const AddEditInvoiceScreen({super.key, this.invoice});

  @override
  State<AddEditInvoiceScreen> createState() => _AddEditInvoiceScreenState();
}

class _AddEditInvoiceScreenState extends State<AddEditInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // حقول الفاتورة
  final _invoiceNumberController = TextEditingController();
  final _dateController = TextEditingController();
  final _notesController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _productController = TextEditingController();

  // متغيرات الحالة
  Map<String, dynamic>? _selectedCustomer;
  Map<String, dynamic>? _selectedProduct;
  List<Map<String, dynamic>> _customers = [];
  List<Map<String, dynamic>> _products = [];
  bool _isLoading = false;
  DateTime _selectedDate = DateTime.now();
  bool _isEditDisabled = false;
  String _editDisabledReason = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _loadProducts();
    _initializeFieldsAsync();
    _checkForPreselectedCustomer();
    _checkEditPermission();

    // إضافة listeners لتحديث المبلغ المتبقي تلقائياً
    _totalAmountController.addListener(_updateRemainingAmount);
    _paidAmountController.addListener(_updateRemainingAmount);
  }

  void _checkEditPermission() {
    if (widget.invoice != null) {
      final createdAt = widget.invoice!['created_at'];
      if (createdAt != null) {
        try {
          final creationTime = DateTime.parse(createdAt);
          final now = DateTime.now();
          final difference = now.difference(creationTime);

          if (difference.inMinutes >= 15) {
            setState(() {
              _isEditDisabled = true;
              _editDisabledReason =
                  'لا يمكن تعديل الفاتورة بعد مرور 15 دقيقة من إنشائها';
            });
          }
        } catch (e) {
          print('خطأ في تحليل تاريخ الإنشاء: $e');
        }
      }
    }
  }

  void _initializeFieldsAsync() async {
    await _initializeFields();
  }

  void _updateRemainingAmount() {
    setState(() {
      // هذا سيحدث واجهة المستخدم لعرض المبلغ المتبقي المحدث
    });
  }

  void _checkForPreselectedCustomer() {
    // التحقق من وجود عميل محدد مسبقاً من شاشة المحافظات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        if (args['selectedCustomer'] != null) {
          final selectedCustomer =
              args['selectedCustomer'] as Map<String, dynamic>;
          setState(() {
            _selectedCustomer = selectedCustomer;
          });
        } else if (args['customer_id'] != null) {
          // استقبال بيانات العميل من شاشة العملاء حسب المحافظات
          _loadCustomerById(args['customer_id']);
        }
      }
    });
  }

  Future<void> _loadCustomerById(int customerId) async {
    try {
      final customer = await _dbHelper.getCustomer(customerId);
      if (customer != null) {
        setState(() {
          _selectedCustomer = customer;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _initializeFields() async {
    if (widget.invoice != null) {
      _invoiceNumberController.text = widget.invoice!['invoice_number'] ?? '';
      _dateController.text = widget.invoice!['date'] ?? '';
      _notesController.text = widget.invoice!['notes'] ?? '';
      _totalAmountController.text = (widget.invoice!['total_amount'] ?? 0.0)
          .toString();
      _paidAmountController.text = (widget.invoice!['paid_amount'] ?? 0.0)
          .toString();
      _selectedDate =
          DateTime.tryParse(widget.invoice!['date']) ?? DateTime.now();

      // تحميل بيانات العميل
      _loadCustomerData(widget.invoice!['customer_id']);
    } else {
      _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate);
      await _generateInvoiceNumber();
      _totalAmountController.text = '';
      _paidAmountController.text = '';
    }
  }

  Future<void> _loadCustomers() async {
    try {
      final customers = await _dbHelper.getCustomers();

      // إذا لم يكن هناك عملاء، إنشاء عميل افتراضي
      if (customers.isEmpty) {
        await _dbHelper.insertCustomer({
          'name': 'عميل افتراضي',
          'phone': '0000000000',
          'address': 'عنوان افتراضي',
          'governorate': 'القاهرة',
        });

        // إعادة تحميل العملاء
        final updatedCustomers = await _dbHelper.getCustomers();
        setState(() {
          _customers = updatedCustomers;
        });
      } else {
        setState(() {
          _customers = customers;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملاء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadProducts() async {
    try {
      final products = await _dbHelper.getProducts();
      setState(() {
        _products = products;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنتجات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCustomerData(int customerId) async {
    try {
      final customer = await _dbHelper.getCustomer(customerId);
      setState(() {
        _selectedCustomer = customer;
      });
    } catch (e) {
      print('خطأ في تحميل بيانات العميل: $e');
    }
  }

  Future<void> _generateInvoiceNumber() async {
    try {
      final now = DateTime.now();
      String baseNumber =
          'INV-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';

      // البحث عن آخر رقم فاتورة لهذا اليوم
      int counter = 1;

      // البحث عن رقم فريد
      String invoiceNumber;
      bool isUnique = false;

      do {
        invoiceNumber = '$baseNumber-${counter.toString().padLeft(3, '0')}';

        // التحقق من وجود الرقم في قاعدة البيانات
        final existingInvoice = await _dbHelper.getInvoiceByNumber(
          invoiceNumber,
        );
        if (existingInvoice == null) {
          isUnique = true;
        } else {
          counter++;
        }
      } while (!isUnique && counter < 1000); // حد أقصى لتجنب الحلقة اللانهائية

      if (mounted) {
        setState(() {
          _invoiceNumberController.text = invoiceNumber;
        });
      }
    } catch (e) {
      // في حالة الخطأ، استخدم الطريقة القديمة
      final now = DateTime.now();
      final invoiceNumber =
          'INV-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${DateTime.now().millisecondsSinceEpoch % 1000}';
      if (mounted) {
        setState(() {
          _invoiceNumberController.text = invoiceNumber;
        });
      }
    }
  }

  // حساب المبلغ المتبقي
  double _calculateRemainingAmount() {
    final totalAmount =
        double.tryParse(_totalAmountController.text.trim()) ?? 0.0;
    final paidAmount =
        double.tryParse(_paidAmountController.text.trim()) ?? 0.0;
    return (totalAmount - paidAmount).clamp(0.0, double.infinity);
  }

  void _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العميل'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // التحقق من المبلغ الإجمالي
    final totalAmountText = _totalAmountController.text.trim();
    if (totalAmountText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال المبلغ الإجمالي'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final totalAmount = double.tryParse(totalAmountText);
    if (totalAmount == null || totalAmount < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال مبلغ إجمالي صحيح'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // التحقق من المبلغ المدفوع
    final paidAmountText = _paidAmountController.text.trim();
    final paidAmount = paidAmountText.isEmpty
        ? 0.0
        : double.tryParse(paidAmountText) ?? 0.0;
    if (paidAmount < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال مبلغ مدفوع صحيح'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (paidAmount > totalAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('المبلغ المدفوع لا يمكن أن يتجاوز المبلغ الإجمالي'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على المستخدم الحالي أو المستخدم الافتراضي
      Map<String, dynamic>? currentUser = await AuthService.getCurrentUser();
      if (currentUser == null) {
        // إذا لم يكن هناك مستخدم مسجل دخول، استخدم المستخدم الافتراضي
        final users = await _dbHelper.getUsers();
        if (users.isNotEmpty) {
          currentUser = users.first;
        }
      }

      final remainingAmount = _calculateRemainingAmount();

      // إضافة معلومات المنتج إلى الملاحظات إذا تم اختيار منتج
      String finalNotes = _notesController.text.trim();
      if (_selectedProduct != null) {
        final productName = _selectedProduct!['name'] ?? '';
        final productPrice = _selectedProduct!['unit_price']?.toString() ?? '0';
        final productInfo = '\nالمنتج: $productName - السعر: $productPrice ج.م';
        finalNotes = finalNotes.isEmpty
            ? productInfo
            : '$finalNotes$productInfo';
      }

      final invoiceData = {
        'invoice_number': _invoiceNumberController.text.trim(),
        'customer_id': _selectedCustomer!['id'],
        'customer_name': _selectedCustomer!['name'],
        'date': _dateController.text,
        'total_amount': totalAmount,
        'paid_amount': paidAmount,
        'remaining_amount': remainingAmount,
        'notes': finalNotes,
        'created_by': currentUser?['id'] ?? 1,
        'created_by_name': currentUser?['name'] ?? 'نظام',
      };

      if (widget.invoice == null) {
        // إضافة فاتورة جديدة
        final invoiceId = await _dbHelper.insertInvoice(invoiceData);

        // إضافة المنتج إلى جدول عناصر الفواتير إذا تم اختيار منتج
        if (_selectedProduct != null) {
          try {
            final invoiceItemData = {
              'invoice_id': invoiceId,
              'product_name': _selectedProduct!['name'] ?? '',
              'quantity': 1.0, // كمية افتراضية
              'unit_price': _selectedProduct!['unit_price'] ?? 0.0,
              'total_price': _selectedProduct!['unit_price'] ?? 0.0,
              'notes': _selectedProduct!['description'] ?? '',
            };
            await _dbHelper.insertInvoiceItem(invoiceItemData);
          } catch (e) {
            print('تحذير: فشل في إضافة المنتج إلى جدول عناصر الفواتير: $e');
            // لا نوقف العملية إذا فشل إضافة المنتج إلى جدول العناصر
          }
        }
      } else {
        // تحديث فاتورة موجودة
        await _dbHelper.updateInvoice(widget.invoice!['id'], invoiceData);

        // تحديث أو إضافة المنتج في جدول عناصر الفواتير
        if (_selectedProduct != null) {
          try {
            // حذف العناصر الموجودة للفاتورة
            await _dbHelper.deleteInvoiceItems(widget.invoice!['id']);

            // إضافة المنتج الجديد
            final invoiceItemData = {
              'invoice_id': widget.invoice!['id'],
              'product_name': _selectedProduct!['name'] ?? '',
              'quantity': 1.0, // كمية افتراضية
              'unit_price': _selectedProduct!['unit_price'] ?? 0.0,
              'total_price': _selectedProduct!['unit_price'] ?? 0.0,
              'notes': _selectedProduct!['description'] ?? '',
            };
            await _dbHelper.insertInvoiceItem(invoiceItemData);
          } catch (e) {
            print('تحذير: فشل في تحديث المنتج في جدول عناصر الفواتير: $e');
            // لا نوقف العملية إذا فشل تحديث المنتج
          }
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.invoice == null
                  ? 'تم إضافة الفاتورة بنجاح'
                  : 'تم تحديث الفاتورة بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ في حفظ الفاتورة';

        // تحسين رسالة الخطأ
        if (e.toString().contains(
          'UNIQUE constraint failed: invoices.invoice_number',
        )) {
          errorMessage =
              'رقم الفاتورة موجود بالفعل. يرجى توليد رقم جديد أو تغيير الرقم الحالي.';
        } else if (e.toString().contains('DatabaseException')) {
          errorMessage = 'خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.';
        } else {
          errorMessage = 'خطأ في حفظ الفاتورة: $e';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'توليد رقم جديد',
              textColor: Colors.white,
              onPressed: () async {
                await _generateInvoiceNumber();
              },
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          widget.invoice == null ? 'إضافة فاتورة جديدة' : 'تعديل الفاتورة',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // عرض رسالة تعطيل التعديل إذا كان التعديل معطل
            if (_isEditDisabled)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _editDisabledReason,
                        style: const TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الفاتورة الأساسية
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'معلومات الفاتورة',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF4A90E2),
                              ),
                            ),
                            const SizedBox(height: 12),

                            // رقم الفاتورة
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _invoiceNumberController,
                                    enabled: !_isEditDisabled,
                                    decoration: InputDecoration(
                                      labelText: 'رقم الفاتورة *',
                                      prefixIcon: const Icon(
                                        Icons.receipt,
                                        color: Color(0xFF4A90E2),
                                      ),
                                      filled: _isEditDisabled,
                                      fillColor: _isEditDisabled
                                          ? Colors.grey[200]
                                          : null,
                                    ),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return 'يرجى إدخال رقم الفاتورة';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                if (widget.invoice == null && !_isEditDisabled)
                                  IconButton(
                                    onPressed: () async {
                                      await _generateInvoiceNumber();
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'تم توليد رقم فاتورة جديد',
                                          ),
                                          backgroundColor: Colors.green,
                                        ),
                                      );
                                    },
                                    icon: const Icon(
                                      Icons.refresh,
                                      color: Color(0xFF4A90E2),
                                    ),
                                    tooltip: 'توليد رقم جديد',
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // العميل
                            DropdownButtonFormField<int>(
                              value: _selectedCustomer?['id'] as int?,
                              isExpanded: true,
                              decoration: InputDecoration(
                                labelText: 'العميل *',
                                prefixIcon: const Icon(
                                  Icons.person,
                                  color: Color(0xFF4A90E2),
                                ),
                                filled: _isEditDisabled,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : null,
                              ),
                              items: _customers.map((customer) {
                                return DropdownMenuItem<int>(
                                  value: customer['id'] as int,
                                  child: Container(
                                    width: double.infinity,
                                    child: Text(
                                      customer['name'] ?? '',
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: _isEditDisabled
                                  ? null
                                  : (value) {
                                      setState(() {
                                        _selectedCustomer = _customers
                                            .firstWhere(
                                              (customer) =>
                                                  customer['id'] == value,
                                              orElse: () => <String, dynamic>{},
                                            );
                                      });
                                    },
                              validator: (value) {
                                if (value == null) {
                                  return 'يرجى اختيار العميل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 12),

                            // المنتج
                            DropdownButtonFormField<int>(
                              value: _selectedProduct?['id'] as int?,
                              isExpanded: true,
                              decoration: InputDecoration(
                                labelText: 'المنتج',
                                prefixIcon: const Icon(
                                  Icons.inventory,
                                  color: Color(0xFF4A90E2),
                                ),
                                helperText:
                                    'اختياري - سيتم تعيين السعر تلقائياً',
                                filled: _isEditDisabled,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : null,
                              ),
                              items: [
                                const DropdownMenuItem<int>(
                                  value: null,
                                  child: Text('-- اختر المنتج --'),
                                ),
                                ..._products.map((product) {
                                  return DropdownMenuItem<int>(
                                    value: product['id'] as int,
                                    child: Container(
                                      width: double.infinity,
                                      child: Text(
                                        '${product['name']} - ${(product['unit_price'] ?? 0.0).toStringAsFixed(2)} ج.م',
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ],
                              onChanged: _isEditDisabled
                                  ? null
                                  : (value) {
                                      setState(() {
                                        if (value != null) {
                                          _selectedProduct = _products
                                              .firstWhere(
                                                (product) =>
                                                    product['id'] == value,
                                                orElse: () =>
                                                    <String, dynamic>{},
                                              );
                                          // تحديث المبلغ الإجمالي تلقائياً بالسعر المحدد
                                          final unitPrice =
                                              _selectedProduct?['unit_price']
                                                  as double? ??
                                              0.0;
                                          _totalAmountController.text =
                                              unitPrice.toString();
                                        } else {
                                          _selectedProduct = null;
                                          // إعادة تعيين المبلغ الإجمالي إلى فارغ
                                          _totalAmountController.text = '';
                                        }
                                      });
                                    },
                            ),
                            const SizedBox(height: 12),

                            // عرض معلومات المنتج المختار
                            if (_selectedProduct != null)
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE8F5E8),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: const Color(0xFF4CAF50),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.check_circle,
                                          color: Color(0xFF4CAF50),
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'المنتج المختار:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Color(0xFF4CAF50),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'الاسم: ${_selectedProduct!['name'] ?? ''}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      'السعر: ${(_selectedProduct!['unit_price'] ?? 0.0).toStringAsFixed(2)} ج.م',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF4CAF50),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (_selectedProduct!['description'] !=
                                            null &&
                                        _selectedProduct!['description']
                                            .toString()
                                            .isNotEmpty)
                                      Text(
                                        'الوصف: ${_selectedProduct!['description']}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                  ],
                                ),
                              ),
                            const SizedBox(height: 12),

                            // التاريخ
                            TextFormField(
                              controller: _dateController,
                              readOnly: true,
                              onTap: _isEditDisabled ? null : _selectDate,
                              decoration: InputDecoration(
                                labelText: 'تاريخ الفاتورة *',
                                prefixIcon: const Icon(
                                  Icons.calendar_today,
                                  color: Color(0xFF4A90E2),
                                ),
                                filled: _isEditDisabled,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : null,
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'يرجى اختيار التاريخ';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // المبلغ الإجمالي
                            TextFormField(
                              controller: _totalAmountController,
                              keyboardType: TextInputType.number,
                              readOnly:
                                  _isEditDisabled || _selectedProduct != null,
                              decoration: InputDecoration(
                                labelText: 'المبلغ الإجمالي *',
                                prefixIcon: const Icon(
                                  Icons.attach_money,
                                  color: Color(0xFF4A90E2),
                                ),
                                suffixText: 'ج.م',
                                helperText: _selectedProduct != null
                                    ? 'السعر محدد تلقائياً من المنتج المختار'
                                    : 'أدخل المبلغ الإجمالي يدوياً',
                                filled:
                                    _isEditDisabled || _selectedProduct != null,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : (_selectedProduct != null
                                          ? const Color(0xFFF0F8FF)
                                          : null),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'يرجى إدخال المبلغ الإجمالي';
                                }
                                final amount = double.tryParse(value);
                                if (amount == null || amount < 0) {
                                  return 'يرجى إدخال مبلغ صحيح';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // المبلغ المدفوع
                            TextFormField(
                              controller: _paidAmountController,
                              keyboardType: TextInputType.number,
                              enabled: !_isEditDisabled,
                              decoration: InputDecoration(
                                labelText: 'المبلغ المدفوع',
                                prefixIcon: const Icon(
                                  Icons.payment,
                                  color: Color(0xFF4A90E2),
                                ),
                                suffixText: 'ج.م',
                                helperText: 'اتركه فارغاً إذا لم يتم الدفع بعد',
                                filled: _isEditDisabled,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : null,
                              ),
                              validator: (value) {
                                if (value != null && value.trim().isNotEmpty) {
                                  final amount = double.tryParse(value);
                                  if (amount == null || amount < 0) {
                                    return 'يرجى إدخال مبلغ صحيح';
                                  }
                                  final totalAmount =
                                      double.tryParse(
                                        _totalAmountController.text.trim(),
                                      ) ??
                                      0.0;
                                  if (amount > totalAmount) {
                                    return 'المبلغ المدفوع لا يمكن أن يتجاوز المبلغ الإجمالي';
                                  }
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // عرض المبلغ المتبقي
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE8F5E8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFF4A90E2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.account_balance_wallet,
                                    color: Color(0xFF4A90E2),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'المبلغ المتبقي: ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF4A90E2),
                                    ),
                                  ),
                                  Text(
                                    '${_calculateRemainingAmount().toStringAsFixed(2)} ج.م',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // الملاحظات
                            TextFormField(
                              controller: _notesController,
                              maxLines: 3,
                              enabled: !_isEditDisabled,
                              decoration: InputDecoration(
                                labelText: 'ملاحظات',
                                prefixIcon: const Icon(
                                  Icons.note,
                                  color: Color(0xFF4A90E2),
                                ),
                                filled: _isEditDisabled,
                                fillColor: _isEditDisabled
                                    ? Colors.grey[200]
                                    : null,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // زر إنشاء رسالة الفاتورة
                            if (widget.invoice != null)
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: _isEditDisabled
                                      ? null
                                      : () async {
                                          try {
                                            final message =
                                                await InvoiceMessageService.generateInvoiceMessage(
                                                  widget.invoice!,
                                                );
                                            if (mounted) {
                                              showDialog(
                                                context: context,
                                                builder: (context) => AlertDialog(
                                                  title: const Text(
                                                    'رسالة الفاتورة',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Color(0xFF4A90E2),
                                                    ),
                                                  ),
                                                  content: SingleChildScrollView(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          message,
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 14,
                                                                height: 1.5,
                                                              ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(
                                                            context,
                                                          ).pop(),
                                                      child: const Text(
                                                        'إغلاق',
                                                      ),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () {
                                                        // هنا يمكن إضافة وظيفة نسخ الرسالة أو إرسالها
                                                        ScaffoldMessenger.of(
                                                          context,
                                                        ).showSnackBar(
                                                          const SnackBar(
                                                            content: Text(
                                                              'تم نسخ الرسالة إلى الحافظة',
                                                            ),
                                                            backgroundColor:
                                                                Colors.green,
                                                          ),
                                                        );
                                                        Navigator.of(
                                                          context,
                                                        ).pop();
                                                      },
                                                      style:
                                                          ElevatedButton.styleFrom(
                                                            backgroundColor:
                                                                const Color(
                                                                  0xFF4A90E2,
                                                                ),
                                                            foregroundColor:
                                                                Colors.white,
                                                          ),
                                                      child: const Text(
                                                        'نسخ الرسالة',
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            }
                                          } catch (e) {
                                            if (mounted) {
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    'خطأ في إنشاء الرسالة: $e',
                                                  ),
                                                  backgroundColor: Colors.red,
                                                ),
                                              );
                                            }
                                          }
                                        },
                                  icon: const Icon(
                                    Icons.message,
                                    color: Colors.white,
                                  ),
                                  label: const Text(
                                    'إنشاء رسالة الفاتورة',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF4A90E2),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // زر الحفظ
            Container(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: (_isLoading || _isEditDisabled)
                      ? null
                      : _saveInvoice,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isEditDisabled
                        ? Colors.grey
                        : const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 3,
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          _isEditDisabled
                              ? 'التعديل غير متاح'
                              : (widget.invoice == null
                                    ? 'إضافة الفاتورة'
                                    : 'حفظ التغييرات'),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _dateController.dispose();
    _notesController.dispose();
    _totalAmountController.dispose();
    _paidAmountController.dispose();
    _productController.dispose();
    super.dispose();
  }
}
