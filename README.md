# InvoFast - نظام إدارة العملاء والفواتير

## نظرة عامة
InvoFast هو تطبيق Flutter لإدارة العملاء والفواتير والتحصيل مع واجهة مستخدم عربية سهلة الاستخدام.

## المتطلبات
- Flutter SDK 3.0.0 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- Android Studio / VS Code
- جهاز Android أو محاكي

## التثبيت والتشغيل

### 1. تثبيت التبعيات
```bash
flutter pub get
```

### 2. تشغيل التطبيق
```bash
flutter run
```

### 3. بناء التطبيق للإنتاج
```bash
flutter build apk --release
```

## الميزات الرئيسية

### إدارة العملاء
- إضافة وتعديل وحذف العملاء
- إدارة أرقام الهواتف المتعددة
- تصنيف العملاء حسب المحافظة
- البحث والتصفية

### إدارة الفواتير
- إنشاء وتعديل الفواتير
- تتبع المدفوع والمتبقي
- طباعة ومشاركة الفواتير
- تصدير البيانات

### نظام التحصيل
- إضافة التحصيلات
- تتبع طرق الدفع
- تقارير التحصيل

### نظام المستخدمين
- إدارة الأدوار والصلاحيات
- تسجيل الدخول والخروج
- تتبع النشاطات

## حل المشاكل الشائعة

### التطبيق لا يعمل (Crash)
1. تأكد من تثبيت جميع التبعيات:
   ```bash
   flutter clean
   flutter pub get
   ```

2. تأكد من إصدار Flutter:
   ```bash
   flutter --version
   ```

3. تحقق من الأخطاء:
   ```bash
   flutter analyze
   ```

### مشاكل قاعدة البيانات
- التطبيق ينشئ قاعدة بيانات تلقائياً عند أول تشغيل
- يمكن إعادة تعيين قاعدة البيانات من إعدادات التطبيق

### مشاكل الأذونات
- تأكد من منح أذونات التخزين للتطبيق
- في Android: الإعدادات > التطبيقات > InvoFast > الأذونات

## بيانات تسجيل الدخول الافتراضية
- **رقم الهاتف**: admin
- **كلمة المرور**: 123456

## هيكل المشروع
```
lib/
├── main.dart                 # نقطة البداية
├── database/                 # قاعدة البيانات
├── models/                   # نماذج البيانات
├── screens/                  # شاشات التطبيق
├── services/                 # الخدمات
├── utils/                    # الأدوات المساعدة
└── widgets/                  # العناصر المخصصة
```

## التطوير

### إضافة ميزات جديدة
1. أنشئ الشاشة في مجلد `screens/`
2. أضف النموذج في مجلد `models/` إذا لزم الأمر
3. أضف الخدمة في مجلد `services/` إذا لزم الأمر
4. أضف المسار في `main.dart`

### تخصيص التصميم
- الألوان الرئيسية: `Color(0xFF4A90E2)`
- الخط: Arial
- اتجاه النص: RTL (من اليمين لليسار)

## الدعم
للمساعدة والدعم التقني، يرجى التواصل مع فريق التطوير.

## الإصدار
الإصدار الحالي: 1.0.9+11
