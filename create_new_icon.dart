import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() async {
  // إنشاء أيقونة جديدة
  await createAppIcon();
}

Future<void> createAppIcon() async {
  // إنشاء أيقونة بسيطة مع نص "ATLAS"
  final recorder = PictureRecorder();
  final canvas = Canvas(recorder);
  final size = const Size(1024, 1024);
  
  // خلفية تركوازية
  final paint = Paint()
    ..color = const Color(0xFF4A90E2)
    ..style = PaintingStyle.fill;
  
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  
  // إطار أبيض
  final borderPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.stroke
    ..strokeWidth = 20;
  
  canvas.drawRect(
    Rect.fromLTWH(10, 10, size.width - 20, size.height - 20),
    borderPaint,
  );
  
  // نص "ATLAS"
  const textStyle = TextStyle(
    color: Colors.white,
    fontSize: 200,
    fontWeight: FontWeight.bold,
    fontFamily: 'Arial',
  );
  
  final textSpan = TextSpan(
    text: 'ATLAS',
    style: textStyle,
  );
  
  final textPainter = TextPainter(
    text: textSpan,
    textDirection: TextDirection.ltr,
  );
  
  textPainter.layout();
  
  final textX = (size.width - textPainter.width) / 2;
  final textY = (size.height - textPainter.height) / 2;
  
  textPainter.paint(canvas, Offset(textX, textY));
  
  // تحويل إلى صورة
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ImageByteFormat.png);
  final bytes = byteData!.buffer.asUint8List();
  
  // حفظ الصورة
  final file = File('assets/images/new_atlas_icon.png');
  await file.writeAsBytes(bytes);
  
  print('✅ تم إنشاء الأيقونة الجديدة: assets/images/new_atlas_icon.png');
  print('📝 لتطبيق الأيقونة الجديدة:');
  print('1. انسخ الملف إلى assets/images/');
  print('2. حدث image_path في pubspec.yaml');
  print('3. شغل: flutter pub get');
  print('4. شغل: flutter pub run flutter_launcher_icons:main');
} 
