# Add Nile Company Customer Script
Write-Host "Starting to add Nile Company customer..." -ForegroundColor Green

# Check if database exists
$dbPath = "$env:LOCALAPPDATA\com.example.atlas_medical_supplies\databases\atlas_medical.db"

if (-not (Test-Path $dbPath)) {
    Write-Host "Database not found at: $dbPath" -ForegroundColor Red
    Write-Host "Please run the application first to create the database" -ForegroundColor Yellow
    exit 1
}

Write-Host "Database found successfully" -ForegroundColor Green

# Customer data
$customerName = "شركة النيل"
$customerPhone = "01110473536"
$customerAddress = "أبو حمص"
$customerGovernorate = "البحيرة"
$customerArea = "أبو حمص"
$customerNotes = "Added on $(Get-Date -Format 'yyyy-MM-dd')"

Write-Host "Customer data:" -ForegroundColor Cyan
Write-Host "  Name: $customerName" -ForegroundColor White
Write-Host "  Phone: $customerPhone" -ForegroundColor White
Write-Host "  Governorate: $customerGovernorate" -ForegroundColor White
Write-Host "  Area: $customerArea" -ForegroundColor White
Write-Host "  Address: $customerAddress" -ForegroundColor White

# Check if customer already exists
$checkQuery = "SELECT id, name FROM customers WHERE phone = '$customerPhone'"
try {
    $existingCustomer = sqlite3 $dbPath "$checkQuery"
    
    if ($existingCustomer) {
        Write-Host "Warning: Customer already exists in database" -ForegroundColor Yellow
        Write-Host "Customer ID: $existingCustomer" -ForegroundColor White
        exit 0
    }
} catch {
    Write-Host "SQLite not available, trying alternative method..." -ForegroundColor Yellow
}

# Alternative method using Flutter/Dart
Write-Host "Creating Flutter script to add customer..." -ForegroundColor Cyan

# Create a simple Flutter script
$flutterScript = @"
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    String path = join(await getDatabasesPath(), 'atlas_medical.db');
    Database db = await openDatabase(path);
    
    final customerData = {
      'name': 'شركة النيل',
      'phone': '01110473536',
      'address': 'أبو حمص',
      'governorate': 'البحيرة',
      'area': 'أبو حمص',
      'notes': 'Added on ${Get-Date -Format 'yyyy-MM-dd'}',
    };
    
    // Check if customer exists
    List<Map<String, dynamic>> existing = await db.query(
      'customers',
      where: 'phone = ?',
      whereArgs: [customerData['phone']],
    );
    
    if (existing.isNotEmpty) {
      print('Customer already exists: ID: ${existing.first['id']}');
      await db.close();
      return;
    }
    
    // Insert customer
    int customerId = await db.insert('customers', customerData);
    print('Customer added successfully! ID: $customerId');
    
    await db.close();
    
  } catch (e) {
    print('Error: $e');
  }
}
"@

$flutterScript | Out-File -FilePath "temp_add_customer.dart" -Encoding UTF8

Write-Host "Flutter script created. You can run it with: flutter run temp_add_customer.dart" -ForegroundColor Green
Write-Host "Or add the customer manually through the application interface" -ForegroundColor Cyan

Write-Host "Customer data ready for insertion:" -ForegroundColor Green
Write-Host "Name: $customerName" -ForegroundColor White
Write-Host "Phone: $customerPhone" -ForegroundColor White
Write-Host "Governorate: $customerGovernorate" -ForegroundColor White
Write-Host "Area: $customerArea" -ForegroundColor White 