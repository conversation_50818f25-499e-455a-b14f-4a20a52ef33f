# دليل حل مشاكل InvoFast

## المشاكل الشائعة وحلولها

### 1. التطبيق لا يعمل (Crash)

#### الأسباب المحتملة:
- مشاكل في التبعيات
- أخطاء في الكود
- مشاكل في قاعدة البيانات
- مشاكل في الأذونات

#### الحلول:

##### الخطوة 1: تنظيف وإعادة تثبيت التبعيات
```bash
flutter clean
flutter pub get
```

##### الخطوة 2: التحقق من الأخطاء
```bash
flutter analyze
```

##### الخطوة 3: تشغيل التطبيق في وضع التصحيح
```bash
flutter run --debug
```

##### الخطوة 4: إعادة تعيين قاعدة البيانات
إذا استمرت المشكلة، يمكن إعادة تعيين قاعدة البيانات:
1. احذف التطبيق من الجهاز
2. أعد تثبيت التطبيق
3. أو استخدم خيار "إعادة تعيين قاعدة البيانات" من إعدادات التطبيق

### 2. مشاكل في قاعدة البيانات

#### الأعراض:
- التطبيق يتعطل عند فتح شاشة معينة
- لا تظهر البيانات
- أخطاء في حفظ البيانات

#### الحلول:
1. **إعادة تهيئة قاعدة البيانات**:
   - اذهب إلى الإعدادات
   - اختر "إعادة تعيين قاعدة البيانات"
   - تأكيد العملية

2. **حذف وإعادة تثبيت التطبيق**:
   - احذف التطبيق
   - أعد تثبيت التطبيق
   - سيتم إنشاء قاعدة بيانات جديدة

### 3. مشاكل في الأذونات

#### الأعراض:
- لا يمكن حفظ الملفات
- لا يمكن مشاركة الفواتير
- أخطاء في الطباعة

#### الحلول:
1. **منح أذونات التخزين**:
   - اذهب إلى إعدادات الجهاز
   - التطبيقات > InvoFast > الأذونات
   - فعّل أذونات التخزين

2. **منح أذونات الكاميرا** (إذا لزم الأمر):
   - فعّل أذونات الكاميرا للمسح الضوئي

### 4. مشاكل في الأداء

#### الأعراض:
- التطبيق بطيء
- تجميد الشاشة
- استهلاك عالي للذاكرة

#### الحلول:
1. **إغلاق التطبيقات الأخرى**
2. **إعادة تشغيل الجهاز**
3. **مسح ذاكرة التخزين المؤقت**:
   - اذهب إلى إعدادات التطبيق
   - اختر "مسح الذاكرة المؤقتة"

### 5. مشاكل في تسجيل الدخول

#### الأعراض:
- لا يمكن تسجيل الدخول
- رسائل خطأ في المصادقة

#### الحلول:
1. **استخدام البيانات الافتراضية**:
   - رقم الهاتف: `admin`
   - كلمة المرور: `123456`

2. **إعادة تعيين كلمة المرور**:
   - اذهب إلى إعدادات التطبيق
   - اختر "إعادة تعيين كلمة المرور"

### 6. مشاكل في الطباعة والتصدير

#### الأعراض:
- لا يمكن طباعة الفواتير
- لا يمكن تصدير البيانات
- أخطاء في إنشاء PDF

#### الحلول:
1. **تأكد من أذونات التخزين**
2. **تأكد من وجود مساحة كافية**
3. **أعد تشغيل التطبيق**

### 7. مشاكل في الإشعارات

#### الأعراض:
- لا تظهر الإشعارات
- إشعارات متأخرة

#### الحلول:
1. **تفعيل الإشعارات في إعدادات الجهاز**
2. **تفعيل الإشعارات في إعدادات التطبيق**
3. **إعادة تشغيل التطبيق**

## خطوات التشخيص العامة

### 1. جمع المعلومات
- سجل رسائل الخطأ
- لاحظ متى تحدث المشكلة
- سجل الإجراءات التي تؤدي للمشكلة

### 2. اختبار الحلول
- جرب الحلول بالترتيب
- اختبر بعد كل حل
- سجل النتائج

### 3. طلب المساعدة
إذا لم تحل المشكلة:
1. سجل تفاصيل المشكلة
2. التقط لقطة شاشة للخطأ
3. ارفق سجل الأخطاء
4. تواصل مع فريق الدعم

## سجل الأخطاء

### كيفية الحصول على سجل الأخطاء:
```bash
flutter logs
```

### في Android:
1. اذهب إلى إعدادات المطور
2. فعّل "تسجيل الأخطاء"
3. شغل التطبيق
4. اذهب إلى "سجل الأخطاء" في إعدادات المطور

## نصائح للوقاية

1. **تحديث التطبيق بانتظام**
2. **النسخ الاحتياطي المنتظم**
3. **عدم إغلاق التطبيق فجأة**
4. **الحفاظ على مساحة كافية في الجهاز**
5. **إعادة تشغيل الجهاز بانتظام**

## الدعم الفني

إذا لم تحل المشكلة بعد تطبيق جميع الحلول:
- تواصل مع فريق الدعم الفني
- ارفق تفاصيل المشكلة
- ارفق سجل الأخطاء
- ارفق لقطات الشاشة

---

**ملاحظة**: هذا الدليل يغطي المشاكل الشائعة. للمشاكل المتقدمة، يرجى التواصل مع فريق التطوير. 