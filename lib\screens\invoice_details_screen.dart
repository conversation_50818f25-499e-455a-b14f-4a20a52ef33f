import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';
import '../services/messaging_service.dart';
import '../services/invoice_sharing_service.dart';
import '../widgets/collection_dialog.dart';
import '../widgets/atlas_logo.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> invoice;

  const InvoiceDetailsScreen({super.key, required this.invoice});

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<Map<String, dynamic>> _collections = [];
  Map<String, dynamic>? _customer;
  bool _isLoading = true;

  // إضافة cache للبيانات
  static Map<int, Map<String, dynamic>> _cachedCustomers = {};
  static Map<int, List<Map<String, dynamic>>> _cachedCollections = {};
  static DateTime? _lastCacheTime;
  static const Duration _cacheDuration = Duration(minutes: 3);

  @override
  void initState() {
    super.initState();
    _loadInvoiceDetails();
  }

  Future<void> _loadInvoiceDetails() async {
    try {
      final customerId = widget.invoice['customer_id'];

      // التحقق من cache
      if (_isCacheValid(customerId)) {
        setState(() {
          _collections = _cachedCollections[customerId] ?? [];
          _customer = _cachedCustomers[customerId];
          _isLoading = false;
        });
        return;
      }

      // تحميل البيانات بشكل متوازي
      final futures = await Future.wait([
        _dbHelper.getCollectionsByCustomer(customerId),
        _dbHelper.getCustomer(customerId),
      ]);

      final allCollections = futures[0] as List<Map<String, dynamic>>;
      final customer = futures[1] as Map<String, dynamic>?;

      // تصفية المدفوعات لتشمل فقط تلك المتعلقة بهذه الفاتورة
      final collections = allCollections
          .where(
            (collection) => collection['invoice_id'] == widget.invoice['id'],
          )
          .toList();

      // تحديث cache
      _updateCache(customerId, customer, collections);

      setState(() {
        _collections = collections;
        _customer = customer;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل تفاصيل الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // التحقق من صلاحية cache
  bool _isCacheValid(int customerId) {
    if (_lastCacheTime == null) return false;
    if (!_cachedCustomers.containsKey(customerId)) return false;
    return DateTime.now().difference(_lastCacheTime!) < _cacheDuration;
  }

  // تحديث cache
  void _updateCache(
    int customerId,
    Map<String, dynamic>? customer,
    List<Map<String, dynamic>> collections,
  ) {
    if (customer != null) {
      _cachedCustomers[customerId] = Map.from(customer);
    }
    _cachedCollections[customerId] = List.from(collections);
    _lastCacheTime = DateTime.now();
  }

  // مسح cache
  void _clearCache() {
    _cachedCustomers.clear();
    _cachedCollections.clear();
    _lastCacheTime = null;
  }

  double get _totalCollections {
    return _collections.fold(
      0.0,
      (sum, collection) => sum + (collection['amount'] ?? 0.0),
    );
  }

  Future<void> _addCollection() async {
    final remainingAmount = widget.invoice['remaining_amount'] ?? 0.0;

    if (remainingAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('هذه الفاتورة مدفوعة بالكامل'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CollectionDialog(
        invoice: widget.invoice,
        remainingAmount: remainingAmount,
      ),
    );

    if (result != null) {
      try {
        final currentUserId = await AuthService.getCurrentUserId();
        final currentUserName = await AuthService.getCurrentUserName();

        final collectionData = {
          'customer_id': widget.invoice['customer_id'],
          'invoice_id': widget.invoice['id'],
          'amount': result['amount'],
          'date': result['date'],
          'collector_name': result['collector_name'],
          'payment_method': result['payment_method'],
          'notes': result['notes'],
          'created_by': currentUserId,
          'customer_name': widget.invoice['customer_name'],
        };

        await _dbHelper.insertCollection(collectionData);

        // تحديث الفاتورة
        final newPaidAmount =
            (widget.invoice['paid_amount'] ?? 0.0) + result['amount'];
        final newRemainingAmount =
            (widget.invoice['total_amount'] ?? 0.0) - newPaidAmount;

        await _dbHelper.updateInvoice(widget.invoice['id'], {
          'paid_amount': newPaidAmount,
          'remaining_amount': newRemainingAmount,
        });

        // إعادة تحميل التفاصيل
        _loadInvoiceDetails();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة التحصيل بنجاح: ${result['amount'].toStringAsFixed(2)} ج.م',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة التحصيل: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _sendViaWhatsApp() async {
    if (_customer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن العثور على بيانات العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final phone = _customer!['phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد رقم هاتف للعميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final success = await MessagingService.sendInvoiceViaWhatsApp(
      customerPhone: phone,
      customerName: _customer!['name'] ?? 'عميل',
      invoiceNumber: widget.invoice['invoice_number'] ?? '',
      totalAmount: widget.invoice['total_amount'] ?? 0.0,
      paidAmount: widget.invoice['paid_amount'] ?? 0.0,
      remainingAmount: widget.invoice['remaining_amount'] ?? 0.0,
      invoiceDate: widget.invoice['date'] ?? '',
      invoiceItems: [],
      invoiceId: widget.invoice['id'],
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال الفاتورة عبر واتساب بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في إرسال الفاتورة عبر واتساب'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendViaSMS() async {
    if (_customer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن العثور على بيانات العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final phone = _customer!['phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد رقم هاتف للعميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final success = await MessagingService.sendInvoiceViaSMS(
      customerPhone: phone,
      customerName: _customer!['name'] ?? 'عميل',
      invoiceNumber: widget.invoice['invoice_number'] ?? '',
      totalAmount: widget.invoice['total_amount'] ?? 0.0,
      paidAmount: widget.invoice['paid_amount'] ?? 0.0,
      remainingAmount: widget.invoice['remaining_amount'] ?? 0.0,
      invoiceDate: widget.invoice['date'] ?? '',
      invoiceItems: [],
      invoiceId: widget.invoice['id'],
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال الرسالة النصية بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في إرسال الرسالة النصية'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _testSMS() async {
    if (_customer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن العثور على بيانات العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final phone = _customer!['phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد رقم هاتف للعميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final success = await MessagingService.testSMS(phone);

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم فتح تطبيق الرسائل لاختبار الإرسال'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في فتح تطبيق الرسائل'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showPhoneInfo() {
    if (_customer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن العثور على بيانات العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final phone = _customer!['phone']?.toString().trim();
    if (phone == null || phone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد رقم هاتف للعميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final phoneInfo = MessagingService.getFormattedPhoneInfo(phone);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات الرقم'),
        content: Text(phoneInfo),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: Text(
          'فاتورة رقم ${widget.invoice['invoice_number']}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_customer != null)
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () async {
                try {
                  await InvoiceSharingService.sendInvoice(
                    context,
                    widget.invoice,
                    _customer!,
                  );
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في إرسال الفاتورة: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              tooltip: 'إرسال الفاتورة',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInvoiceDetails,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
            )
          : RefreshIndicator(
              onRefresh: _loadInvoiceDetails,
              color: const Color(0xFF4A90E2),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الفاتورة الأساسية
                    _buildInvoiceInfoCard(),
                    const SizedBox(height: 16),

                    // معلومات العميل
                    if (_customer != null) ...[
                      _buildCustomerInfoCard(),
                      const SizedBox(height: 16),
                    ],

                    // المدفوعات
                    _buildCollectionsCard(),
                    const SizedBox(height: 16),

                    // ملخص مالي
                    _buildFinancialSummaryCard(),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildInvoiceInfoCard() {
    final remainingAmount = widget.invoice['remaining_amount'] ?? 0.0;
    final isPaid = remainingAmount <= 0;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: isPaid
                      ? Colors.green
                      : const Color(0xFF4A90E2),
                  child: Icon(
                    isPaid ? Icons.check : Icons.receipt,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فاتورة رقم ${widget.invoice['invoice_number']}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4A90E2),
                        ),
                      ),
                      Text(
                        'التاريخ: ${widget.invoice['date']}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isPaid ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isPaid ? 'مدفوعة' : 'غير مدفوعة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المبلغ الإجمالي:'),
                Text(
                  '${(widget.invoice['total_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المدفوع:'),
                Text(
                  '${(widget.invoice['paid_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            if (!isPaid) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('المتبقي:'),
                  Text(
                    '${remainingAmount.toStringAsFixed(2)} ج.م',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
            if (widget.invoice['created_by_name'] != null) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('أنشأها:'),
                  Text(
                    widget.invoice['created_by_name'],
                    style: const TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات العميل',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, color: Color(0xFF4A90E2)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _customer!['name'] ?? 'غير محدد',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            if (_customer!['phone'] != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.phone, color: Color(0xFF4A90E2)),
                  const SizedBox(width: 8),
                  Expanded(child: Text(_customer!['phone'])),
                ],
              ),
            ],
            if (_customer!['address'] != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.location_on, color: Color(0xFF4A90E2)),
                  const SizedBox(width: 8),
                  Expanded(child: Text(_customer!['address'])),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCollectionsCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'المدفوعات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4A90E2),
                  ),
                ),
                Text(
                  '${_collections.length} دفعة',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_collections.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'لا توجد مدفوعات لهذه الفاتورة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _collections.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final collection = _collections[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: const Icon(Icons.payment, color: Colors.green),
                    title: Text(
                      'دفعة بتاريخ ${collection['date']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('المحصل: ${collection['collector_name']}'),
                        Text('الطريقة: ${collection['payment_method']}'),
                        if (collection['notes'] != null &&
                            collection['notes'].isNotEmpty)
                          Text('ملاحظات: ${collection['notes']}'),
                      ],
                    ),
                    trailing: Text(
                      '${(collection['amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملخص المالي',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي الفاتورة:'),
                Text(
                  '${(widget.invoice['total_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي المدفوعات:'),
                Text(
                  '${_totalCollections.toStringAsFixed(2)} ج.م',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'المتبقي:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${(widget.invoice['remaining_amount'] ?? 0.0).toStringAsFixed(2)} ج.م',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    final remainingAmount = widget.invoice['remaining_amount'] ?? 0.0;
    final isPaid = remainingAmount <= 0;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 12,
      ), // تقليل الـ padding
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            if (!isPaid)
              ElevatedButton.icon(
                onPressed: _addCollection,
                icon: const Icon(Icons.payment, size: 18), // تقليل حجم الأيقونة
                label: const Text(
                  'إضافة تحصيل',
                  style: TextStyle(fontSize: 12),
                ), // تقليل حجم الخط
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ), // تقليل الـ padding
                ),
              ),
            if (!isPaid) const SizedBox(width: 6), // تقليل المسافة
            ElevatedButton.icon(
              onPressed: _sendViaWhatsApp,
              icon: const Icon(Icons.message, size: 18),
              label: const Text('واتساب', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
            const SizedBox(width: 6),
            ElevatedButton.icon(
              onPressed: _sendViaSMS,
              icon: const Icon(Icons.sms, size: 18),
              label: const Text('رسالة', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
            const SizedBox(width: 6),
            ElevatedButton.icon(
              onPressed: _testSMS,
              icon: const Icon(Icons.sms_failed, size: 18),
              label: const Text('اختبار', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
            const SizedBox(width: 6),
            ElevatedButton.icon(
              onPressed: _showPhoneInfo,
              icon: const Icon(Icons.info, size: 18),
              label: const Text(
                'معلومات الرقم',
                style: TextStyle(fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
