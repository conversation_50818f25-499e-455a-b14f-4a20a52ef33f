# ميزة المبلغ المدفوع في الفواتير

## نظرة عامة
تم إضافة ميزة المبلغ المدفوع في الفواتير لتتبع المدفوعات الجزئية والكاملة للفواتير.

## الميزات المضافة

### 1. حقل إدخال المبلغ المدفوع
- إضافة حقل إدخال للمبلغ المدفوع في شاشة إنشاء/تعديل الفاتورة
- التحقق من صحة المبلغ المدخل
- منع إدخال مبلغ مدفوع أكبر من المبلغ الإجمالي

### 2. حساب تلقائي للمبلغ المتبقي
- حساب تلقائي للمبلغ المتبقي (المبلغ الإجمالي - المبلغ المدفوع)
- عرض المبلغ المتبقي في واجهة المستخدم
- تحديث فوري عند تغيير المبالغ

### 3. عرض المعلومات في الواجهات المختلفة

#### شاشة إنشاء/تعديل الفاتورة
- حقل إدخال المبلغ المدفوع
- عرض المبلغ المتبقي في مربع ملون
- التحقق من صحة البيانات

#### شاشة قائمة الفواتير
- عرض المبلغ المدفوع باللون الأخضر
- عرض المبلغ المتبقي باللون الأحمر (إذا كان هناك مبلغ متبقي)
- مؤشر بصري لحالة الدفع (أيقونة خضراء للفواتير المدفوعة بالكامل)

#### شاشة تفاصيل الفاتورة
- عرض تفاصيل كاملة للمبالغ
- المبلغ الإجمالي
- المبلغ المدفوع
- المبلغ المتبقي

### 4. دعم في خدمات الإرسال
- تضمين المبلغ المدفوع والمتبقي في رسائل WhatsApp
- تضمين المبلغ المدفوع والمتبقي في الرسائل النصية
- تنسيق واضح للمبالغ في الرسائل

## كيفية الاستخدام

### إنشاء فاتورة جديدة
1. انتقل إلى شاشة إضافة فاتورة جديدة
2. أدخل المبلغ الإجمالي للفاتورة
3. أدخل المبلغ المدفوع (اختياري - اتركه فارغاً إذا لم يتم الدفع)
4. سيتم حساب المبلغ المتبقي تلقائياً
5. احفظ الفاتورة

### تعديل فاتورة موجودة
1. افتح الفاتورة للتعديل
2. عدّل المبلغ المدفوع حسب الحاجة
3. سيتم تحديث المبلغ المتبقي تلقائياً
4. احفظ التغييرات

### تتبع المدفوعات
- يمكن رؤية حالة الدفع في قائمة الفواتير
- الفواتير المدفوعة بالكامل تظهر بأيقونة خضراء
- الفواتير غير المدفوعة أو المدفوعة جزئياً تظهر بأيقونة زرقاء

## التحسينات التقنية

### قاعدة البيانات
- استخدام الحقول الموجودة: `paid_amount`, `remaining_amount`
- حساب تلقائي للمبلغ المتبقي
- التحقق من صحة البيانات

### واجهة المستخدم
- تصميم متجاوب ومريح للاستخدام
- ألوان مميزة للمبالغ المختلفة
- رسائل خطأ واضحة ومفيدة

### الأداء
- تحديث فوري للمبالغ
- حساب تلقائي بدون إعادة تحميل الصفحة
- حفظ فعال للبيانات

## الفوائد

1. **تتبع أفضل للمدفوعات**: معرفة دقيقة للمبالغ المدفوعة والمتبقية
2. **تقارير محسنة**: إمكانية إنشاء تقارير عن حالة الدفع
3. **إدارة أفضل للذمم**: تتبع دقيق للذمم المدينة
4. **شفافية مع العملاء**: إرسال تفاصيل واضحة عن المدفوعات

## التوافق
- متوافق مع جميع الميزات الموجودة
- لا يؤثر على البيانات الموجودة
- يعمل مع جميع أنواع الفواتير 