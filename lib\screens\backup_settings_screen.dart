import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/auto_backup_service.dart';
import '../services/google_drive_service.dart';
import '../widgets/atlas_logo.dart';

class BackupSettingsScreen extends StatefulWidget {
  const BackupSettingsScreen({super.key});

  @override
  State<BackupSettingsScreen> createState() => _BackupSettingsScreenState();
}

class _BackupSettingsScreenState extends State<BackupSettingsScreen> {
  bool _isLoading = true;
  bool _autoBackupEnabled = true;
  DateTime? _lastBackupTime;
  int _totalBackups = 0;
  List<Map<String, dynamic>> _backups = [];

  @override
  void initState() {
    super.initState();
    _loadBackupStats();
  }

  Future<void> _loadBackupStats() async {
    try {
      final stats = await AutoBackupService.getBackupStats();
      final backups = await GoogleDriveService.getBackups();

      setState(() {
        _autoBackupEnabled = stats['is_enabled'] ?? true;
        _lastBackupTime = stats['last_backup'] != null
            ? DateTime.parse(stats['last_backup'])
            : null;
        _totalBackups = stats['total_backups'] ?? 0;
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل إحصائيات النسخ الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleAutoBackup(bool value) async {
    try {
      await AutoBackupService.setAutoBackupEnabled(value);
      setState(() {
        _autoBackupEnabled = value;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              value
                  ? 'تم تفعيل النسخ الاحتياطية التلقائية'
                  : 'تم تعطيل النسخ الاحتياطية التلقائية',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير إعدادات النسخ الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performManualBackup() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final success = await AutoBackupService.performManualBackup();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفع النسخة الاحتياطية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadBackupStats();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في رفع النسخة الاحتياطية'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في النسخة الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreFromBackup(Map<String, dynamic> backup) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: Text(
          'هل أنت متأكد من استعادة البيانات من النسخة الاحتياطية "${backup['name']}"؟\n'
          'سيتم استبدال جميع البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() {
          _isLoading = true;
        });

        final result = await GoogleDriveService.restoreFromBackup(backup['id']);

        if (result['success'] && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تمت الاستعادة بنجاح: ${result['message']}'),
              backgroundColor: Colors.green,
            ),
          );
          _loadBackupStats();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في الاستعادة: ${result['message']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في الاستعادة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteBackup(Map<String, dynamic> backup) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف النسخة الاحتياطية "${backup['name']}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await GoogleDriveService.deleteBackup(backup['id']);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف النسخة الاحتياطية بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadBackupStats();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حذف النسخة الاحتياطية'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف النسخة الاحتياطية: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: Text(
          'إعدادات النسخ الاحتياطية',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBackupStats,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
            )
          : RefreshIndicator(
              onRefresh: _loadBackupStats,
              color: const Color(0xFF4A90E2),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // إعدادات النسخ الاحتياطية التلقائية
                    _buildAutoBackupSettings(),
                    const SizedBox(height: 16),

                    // إحصائيات النسخ الاحتياطية
                    _buildBackupStats(),
                    const SizedBox(height: 16),

                    // قائمة النسخ الاحتياطية
                    _buildBackupsList(),
                  ],
                ),
              ),
            ),
      // تم إزالة FloatingActionButton حسب طلب المستخدم
      // floatingActionButton: FloatingActionButton.extended(
      //   onPressed: _performManualBackup,
      //   backgroundColor: const Color(0xFF4A90E2),
      //   foregroundColor: Colors.white,
      //   icon: const Icon(Icons.backup),
      //   label: const Text('نسخة احتياطية يدوية'),
      // ),
    );
  }

  Widget _buildAutoBackupSettings() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'النسخ الاحتياطية التلقائية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.sync, color: Color(0xFF4A90E2)),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'رفع نسخة احتياطية كل ثانية',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                Switch(
                  value: _autoBackupEnabled,
                  onChanged: _toggleAutoBackup,
                  activeColor: const Color(0xFF4A90E2),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _autoBackupEnabled
                  ? '✅ النسخ الاحتياطية التلقائية مفعلة'
                  : '❌ النسخ الاحتياطية التلقائية معطلة',
              style: TextStyle(
                color: _autoBackupEnabled ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupStats() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات النسخ الاحتياطية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.storage, color: Color(0xFF4A90E2)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إجمالي النسخ الاحتياطية: $_totalBackups',
                        style: const TextStyle(fontSize: 16),
                      ),
                      if (_lastBackupTime != null)
                        Text(
                          'آخر نسخة احتياطية: ${DateFormat('yyyy/MM/dd HH:mm:ss').format(_lastBackupTime!)}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupsList() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'النسخ الاحتياطية المتاحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4A90E2),
                  ),
                ),
                Text(
                  '${_backups.length} نسخة',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_backups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'لا توجد نسخ احتياطية',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _backups.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final backup = _backups[index];
                  final created = backup['created'] != null
                      ? DateTime.parse(backup['created'])
                      : null;

                  return ListTile(
                    leading: const Icon(Icons.backup, color: Color(0xFF4A90E2)),
                    title: Text(
                      backup['name'] ?? 'نسخة احتياطية',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (created != null)
                          Text(
                            'التاريخ: ${DateFormat('yyyy/MM/dd HH:mm:ss').format(created)}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        if (backup['size'] != null)
                          Text(
                            'الحجم: ${(int.parse(backup['size'].toString()) / 1024).toStringAsFixed(2)} KB',
                            style: const TextStyle(fontSize: 12),
                          ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'restore') {
                          _restoreFromBackup(backup);
                        } else if (value == 'delete') {
                          _deleteBackup(backup);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'restore',
                          child: Row(
                            children: [
                              Icon(Icons.restore, color: Colors.orange),
                              SizedBox(width: 8),
                              Expanded(child: Text('استعادة')),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Expanded(child: Text('حذف')),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
