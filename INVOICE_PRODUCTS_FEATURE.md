# ميزة إضافة المنتجات في الفواتير

## نظرة عامة
تم إضافة ميزة جديدة تسمح باختيار وإضافة المنتجات في تفاصيل الفاتورة وقت الإنشاء. هذه الميزة تحسن من دقة الفواتير وتوفر تفاصيل أكثر عن المنتجات المباعة.

## الميزات المضافة

### 1. اختيار المنتج
- قائمة منسدلة لاختيار المنتج من قاعدة البيانات
- عرض جميع المنتجات النشطة فقط
- عرض سعر المنتج تلقائياً عند اختياره

### 2. إضافة المنتج للفاتورة
- اختيار المنتج من القائمة المنسدلة
- إضافة المنتج للفاتورة بنقرة واحدة
- الكمية تلقائياً = 1
- السعر = سعر المنتج المحدد في قاعدة البيانات

### 3. عرض المنتجات المضافة
- قائمة بجميع المنتجات المضافة للفاتورة
- عرض اسم المنتج وسعره فقط
- إمكانية حذف أي منتج من الفاتورة
- تحديث المبلغ الإجمالي للفاتورة تلقائياً

### 4. التحقق من صحة البيانات
- التأكد من اختيار منتج واحد على الأقل
- رسائل خطأ واضحة للمستخدم

## التغييرات التقنية

### الملفات المعدلة
- `lib/screens/add_edit_invoice_screen.dart`

### المتغيرات الجديدة
```dart
List<Map<String, dynamic>> _products = [];
List<Map<String, dynamic>> _invoiceItems = [];
Map<String, dynamic>? _selectedProduct;
```

### الدوال الجديدة
- `_loadProducts()` - تحميل المنتجات من قاعدة البيانات
- `_loadInvoiceItems()` - تحميل عناصر الفاتورة الموجودة
- `_addInvoiceItem()` - إضافة منتج جديد للفاتورة
- `_removeInvoiceItem()` - حذف منتج من الفاتورة
- `_calculateItemTotalValue()` - حساب السعر الإجمالي للعنصر
- `_calculateInvoiceTotalFromItems()` - حساب السعر الإجمالي للفاتورة من العناصر
- `_onProductSelected()` - معالجة اختيار المنتج

### واجهة المستخدم
- قسم جديد "إضافة المنتجات" مع قائمة منسدلة فقط
- عرض سعر المنتج المختار
- قسم "المنتجات المضافة" لعرض المنتجات المختارة
- أزرار إضافة وحذف المنتجات
- عرض السعر الإجمالي للفاتورة

## كيفية الاستخدام

### إضافة فاتورة جديدة
1. اختر العميل والتاريخ
2. في قسم "إضافة المنتجات":
   - اختر المنتج من القائمة المنسدلة
   - سعر المنتج يظهر تلقائياً
   - اضغط "إضافة المنتج للفاتورة"
3. كرر العملية لإضافة منتجات أخرى
4. المبلغ الإجمالي للفاتورة يتم حسابه تلقائياً
5. أدخل المبلغ المدفوع والملاحظات
6. اضغط "إضافة الفاتورة"

### تعديل فاتورة موجودة
1. يتم تحميل المنتجات المضافة مسبقاً
2. يمكن إضافة منتجات جديدة أو حذف موجودة
3. يتم تحديث المبلغ الإجمالي تلقائياً
4. احفظ التغييرات

## التحسينات المستقبلية
- إضافة البحث في المنتجات
- إضافة تصنيفات المنتجات
- إضافة خصومات على المنتجات
- إضافة وحدات قياس مختلفة
- إضافة مخزون المنتجات

## ملاحظات مهمة
- يجب أن تكون المنتجات موجودة في قاعدة البيانات قبل إضافتها للفواتير
- يتم حفظ عناصر الفاتورة في جدول منفصل `invoice_items`
- عند حذف فاتورة، يتم حذف عناصرها تلقائياً
- المبلغ الإجمالي للفاتورة يتم حسابه من مجموع أسعار المنتجات
- كل منتج يتم إضافته بكمية = 1 وسعر = سعر المنتج في قاعدة البيانات 