# دليل إضافة عميل شركة النيل

## البيانات المطلوبة لإضافة العميل:

### معلومات العميل:
- **الاسم:** شركة النيل
- **رقم الهاتف:** 01110473536
- **المحافظة:** البحيرة
- **المنطقة:** أبو حمص
- **العنوان:** أبو حمص
- **ملاحظات:** تم الإضافة بتاريخ اليوم

## خطوات الإضافة من خلال التطبيق:

### 1. تشغيل التطبيق
```bash
flutter run
```

### 2. تسجيل الدخول
- استخدم بيانات تسجيل الدخول الخاصة بك

### 3. الوصول إلى صفحة العملاء
- من القائمة الرئيسية، اختر "العملاء" أو "إدارة العملاء"

### 4. إضافة عميل جديد
- اضغط على زر "إضافة عميل جديد" أو "+"
- أو ابحث عن "إضافة عميل" في القائمة

### 5. ملء بيانات العميل
في النموذج، أدخل البيانات التالية:

```
الاسم: شركة النيل
رقم الهاتف: 01110473536
المحافظة: البحيرة
المنطقة: أبو حمص
العنوان: أبو حمص
ملاحظات: تم الإضافة بتاريخ [تاريخ اليوم]
```

### 6. حفظ العميل
- اضغط على زر "حفظ" أو "إضافة"
- تأكد من ظهور رسالة نجاح الإضافة

### 7. التحقق من الإضافة
- ابحث عن "شركة النيل" في قائمة العملاء
- تأكد من ظهور البيانات بشكل صحيح

## ملاحظات مهمة:

1. **التأكد من عدم التكرار:** تأكد من عدم وجود عميل بنفس رقم الهاتف مسبقاً
2. **صحة البيانات:** تأكد من صحة جميع البيانات المدخلة
3. **النسخ الاحتياطي:** يُنصح بعمل نسخة احتياطية قبل إضافة بيانات جديدة

## في حالة وجود مشاكل:

1. **التطبيق لا يفتح:** تأكد من تثبيت Flutter وتشغيل `flutter pub get`
2. **خطأ في قاعدة البيانات:** تأكد من تشغيل التطبيق مرة واحدة على الأقل لإنشاء قاعدة البيانات
3. **خطأ في الإضافة:** تحقق من صحة البيانات المدخلة

## بيانات العميل للتأكد:

```
الاسم: شركة النيل
الهاتف: 01110473536
المحافظة: البحيرة
المنطقة: أبو حمص
العنوان: أبو حمص
```

---

**تم إنشاء هذا الدليل في:** $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') 