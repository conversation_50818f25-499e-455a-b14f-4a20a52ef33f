import 'package:intl/intl.dart';
import '../database/database_helper.dart';

/// خدمة لإنشاء رسائل نصية احترافية لملخص الفواتير
class InvoiceMessageService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static final NumberFormat _currencyFormatter = NumberFormat(
    '#,##0.00',
    'ar_EG',
  );
  static final DateFormat _dateFormatter = DateFormat('dd/MM/yyyy', 'ar_EG');

  /// دالة لإنشاء رسالة نصية احترافية لملخص الفاتورة
  /// تأخذ كائن الفاتورة وتُرجع نص الرسالة باللغة العربية
  static Future<String> generateInvoiceMessage(
    Map<String, dynamic> invoice,
  ) async {
    try {
      // الحصول على تفاصيل العميل
      final customer = await _dbHelper.getCustomer(invoice['customer_id']);
      if (customer == null) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      // الحصول على عناصر الفاتورة من جدول invoice_items
      final invoiceItems = await _dbHelper.getInvoiceItems(invoice['id']);

      // الحصول على التحصيلات المرتبطة بالفاتورة
      final collections = await _dbHelper.getCollectionsByInvoice(
        invoice['id'],
      );

      // تنسيق اسم العميل
      final customerName = customer['name'] ?? 'عميل غير محدد';

      // تنسيق رقم الفاتورة
      final invoiceNumber = invoice['invoice_number'] ?? 'غير محدد';

      // تنسيق تاريخ الفاتورة
      final invoiceDate = invoice['date'] != null
          ? _dateFormatter.format(DateTime.parse(invoice['date']))
          : 'غير محدد';

      // تنسيق المبالغ
      final totalAmount = invoice['total_amount'] ?? 0.0;
      final paidAmount = invoice['paid_amount'] ?? 0.0;
      final remainingAmount = invoice['remaining_amount'] ?? 0.0;

      // بناء تفاصيل المنتجات
      String productsDetails = '';
      if (invoiceItems.isNotEmpty) {
        for (final item in invoiceItems) {
          final productName = item['product_name'] ?? 'منتج غير محدد';
          final quantity = item['quantity'] ?? 0.0;
          final unitPrice = item['unit_price'] ?? 0.0;
          final totalPrice = item['total_price'] ?? 0.0;

          productsDetails +=
              '- $productName × ${quantity.toStringAsFixed(0)} = ${_currencyFormatter.format(totalPrice)} ج.م\n';
        }
      } else {
        // إذا لم توجد عناصر في الجدول، ابحث في الملاحظات
        final notes = invoice['notes']?.toString() ?? '';
        if (notes.contains('المنتج:')) {
          // البحث عن معلومات المنتج في الملاحظات
          final productMatch = RegExp(
            r'المنتج:\s*(.+?)\s*-\s*السعر:\s*(.+?)\s*ج\.م',
          ).firstMatch(notes);
          if (productMatch != null) {
            final productName = productMatch.group(1)?.trim() ?? '';
            final productPrice = productMatch.group(2)?.trim() ?? '';
            productsDetails = '- $productName - السعر: $productPrice ج.م\n';
          }
        }

        // إذا لم يتم العثور على منتج في أي مكان، استخدم الرسالة الافتراضية
        if (productsDetails.isEmpty) {
          productsDetails = '- لا توجد منتجات محددة\n';
        }
      }

      // تحديد تاريخ التحصيل المتوقع
      String collectionDate = '';
      if (collections.isNotEmpty) {
        // إذا كان هناك تحصيلات، نأخذ آخر تاريخ تحصيل
        final lastCollection = collections.first;
        final collectionDateStr = lastCollection['date'];
        if (collectionDateStr != null) {
          collectionDate = _dateFormatter.format(
            DateTime.parse(collectionDateStr),
          );
        }
      }

      // بناء الرسالة النهائية
      String message =
          '''
السيد/ $customerName
فاتورة رقم: $invoiceNumber
تاريخ: $invoiceDate

المنتجات:
$productsDetails
إجمالي الفاتورة: ${_currencyFormatter.format(totalAmount)} ج.م
تم الدفع: ${_currencyFormatter.format(paidAmount)} ج.م
المتبقي: ${_currencyFormatter.format(remainingAmount)} ج.م''';

      // إضافة تاريخ التحصيل إذا كان موجوداً
      if (collectionDate.isNotEmpty) {
        message += '\nتاريخ التحصيل: $collectionDate';
      }

      // إضافة ملاحظات الفاتورة إذا كانت موجودة (بدون معلومات المنتج)
      final notes = invoice['notes']?.toString() ?? '';
      if (notes.isNotEmpty && !notes.contains('المنتج:')) {
        message += '\n\nملاحظات: $notes';
      }

      message += '\n\nنشكر تعاونكم.';

      return message;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع رسالة مبسطة
      return '''
فاتورة رقم: ${invoice['invoice_number'] ?? 'غير محدد'}
تاريخ: ${invoice['date'] != null ? _dateFormatter.format(DateTime.parse(invoice['date'])) : 'غير محدد'}
إجمالي الفاتورة: ${_currencyFormatter.format(invoice['total_amount'] ?? 0.0)} ج.م
تم الدفع: ${_currencyFormatter.format(invoice['paid_amount'] ?? 0.0)} ج.م
المتبقي: ${_currencyFormatter.format(invoice['remaining_amount'] ?? 0.0)} ج.م

نشكر تعاونكم.''';
    }
  }

  /// دالة لإنشاء رسالة مبسطة للفاتورة (بدون تفاصيل المنتجات)
  static String generateSimpleInvoiceMessage(Map<String, dynamic> invoice) {
    final customerName = invoice['customer_name'] ?? 'عميل غير محدد';
    final invoiceNumber = invoice['invoice_number'] ?? 'غير محدد';
    final invoiceDate = invoice['date'] != null
        ? _dateFormatter.format(DateTime.parse(invoice['date']))
        : 'غير محدد';
    final totalAmount = invoice['total_amount'] ?? 0.0;
    final paidAmount = invoice['paid_amount'] ?? 0.0;
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;

    return '''
السيد/ $customerName
فاتورة رقم: $invoiceNumber
تاريخ: $invoiceDate
إجمالي الفاتورة: ${_currencyFormatter.format(totalAmount)} ج.م
تم الدفع: ${_currencyFormatter.format(paidAmount)} ج.م
المتبقي: ${_currencyFormatter.format(remainingAmount)} ج.م

نشكر تعاونكم.''';
  }

  /// دالة لإنشاء رسالة تذكير بالدفع
  static String generatePaymentReminderMessage(Map<String, dynamic> invoice) {
    final customerName = invoice['customer_name'] ?? 'عميل غير محدد';
    final invoiceNumber = invoice['invoice_number'] ?? 'غير محدد';
    final invoiceDate = invoice['date'] != null
        ? _dateFormatter.format(DateTime.parse(invoice['date']))
        : 'غير محدد';
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;

    return '''
السيد/ $customerName
تذكير بدفع الفاتورة رقم: $invoiceNumber
تاريخ الفاتورة: $invoiceDate
المبلغ المتبقي: ${_currencyFormatter.format(remainingAmount)} ج.م

نرجو منكم تسديد المبلغ المتبقي في أقرب وقت ممكن.
نشكر تعاونكم.''';
  }

  /// دالة لإنشاء رسالة تأكيد الدفع
  static String generatePaymentConfirmationMessage(
    Map<String, dynamic> invoice,
    double paidAmount,
  ) {
    final customerName = invoice['customer_name'] ?? 'عميل غير محدد';
    final invoiceNumber = invoice['invoice_number'] ?? 'غير محدد';
    final totalAmount = invoice['total_amount'] ?? 0.0;
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;

    return '''
السيد/ $customerName
تم استلام مبلغ ${_currencyFormatter.format(paidAmount)} ج.م
للفاتورة رقم: $invoiceNumber
إجمالي الفاتورة: ${_currencyFormatter.format(totalAmount)} ج.م
المتبقي: ${_currencyFormatter.format(remainingAmount)} ج.م

شكراً لكم على الدفع.''';
  }
}
