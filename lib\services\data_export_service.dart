import 'dart:io';
import 'package:flutter/material.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import '../database/database_helper.dart';

class DataExportService {
  /// تصدير جميع البيانات إلى ملف Excel
  static Future<String?> exportAllDataToExcel() async {
    try {
      // طلب صلاحيات الكتابة
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('مطلوب صلاحية الكتابة للتصدير');
      }

      final dbHelper = DatabaseHelper();
      
      // جلب جميع البيانات
      final customers = await dbHelper.getCustomers();
      final products = await dbHelper.getProducts();
      final invoices = await dbHelper.getAllInvoices();
      final collections = await dbHelper.getAllCollections();
      final users = await dbHelper.getUsers();

      // إنشاء ملف Excel
      final excel = Excel.createExcel();
      
      // إضافة ورقة العملاء
      _addCustomersSheet(excel, customers);
      
      // إضافة ورقة المنتجات
      _addProductsSheet(excel, products);
      
      // إضافة ورقة الفواتير
      _addInvoicesSheet(excel, invoices);
      
      // إضافة ورقة التحصيلات
      _addCollectionsSheet(excel, collections);
      
      // إضافة ورقة المستخدمين
      _addUsersSheet(excel, users);
      
      // إضافة ورقة الإحصائيات
      _addStatisticsSheet(excel, customers, products, invoices, collections);

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'atlas_export_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(excel.encode()!);

      return file.path;
    } catch (e) {
      print('خطأ في تصدير البيانات: $e');
      return null;
    }
  }

  /// إضافة ورقة العملاء
  static void _addCustomersSheet(Excel excel, List<Map<String, dynamic>> customers) {
    final sheet = excel['العملاء'];
    
    // رأس الجدول
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'الرقم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = 'الاسم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value = 'الهاتف الرئيسي';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value = 'العنوان';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value = 'المحافظة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value = 'المنطقة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: 0)).value = 'تاريخ الإنشاء';

    // بيانات العملاء
    for (int i = 0; i < customers.length; i++) {
      final customer = customers[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = customer['id'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = customer['name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = customer['primary_phone'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = customer['address'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = customer['governorate'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = customer['area'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = customer['created_at'];
    }
  }

  /// إضافة ورقة المنتجات
  static void _addProductsSheet(Excel excel, List<Map<String, dynamic>> products) {
    final sheet = excel['المنتجات'];
    
    // رأس الجدول
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'الرقم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = 'اسم المنتج';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value = 'السعر';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value = 'الوصف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value = 'تاريخ الإنشاء';

    // بيانات المنتجات
    for (int i = 0; i < products.length; i++) {
      final product = products[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = product['id'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = product['name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = product['price'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = product['description'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = product['created_at'];
    }
  }

  /// إضافة ورقة الفواتير
  static void _addInvoicesSheet(Excel excel, List<Map<String, dynamic>> invoices) {
    final sheet = excel['الفواتير'];
    
    // رأس الجدول
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'رقم الفاتورة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = 'اسم العميل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value = 'التاريخ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value = 'المبلغ الإجمالي';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value = 'المبلغ المدفوع';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value = 'المبلغ المتبقي';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: 0)).value = 'المنشئ';

    // بيانات الفواتير
    for (int i = 0; i < invoices.length; i++) {
      final invoice = invoices[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = invoice['invoice_number'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = invoice['customer_name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = invoice['date'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = invoice['total_amount'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = invoice['paid_amount'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = invoice['remaining_amount'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = invoice['created_by_name'];
    }
  }

  /// إضافة ورقة التحصيلات
  static void _addCollectionsSheet(Excel excel, List<Map<String, dynamic>> collections) {
    final sheet = excel['التحصيلات'];
    
    // رأس الجدول
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'الرقم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = 'اسم العميل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value = 'المبلغ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value = 'التاريخ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value = 'المحصل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value = 'طريقة الدفع';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: 0)).value = 'ملاحظات';

    // بيانات التحصيلات
    for (int i = 0; i < collections.length; i++) {
      final collection = collections[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = collection['id'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = collection['customer_name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = collection['amount'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = collection['date'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = collection['collector_name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = collection['payment_method'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = collection['notes'];
    }
  }

  /// إضافة ورقة المستخدمين
  static void _addUsersSheet(Excel excel, List<Map<String, dynamic>> users) {
    final sheet = excel['المستخدمين'];
    
    // رأس الجدول
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'الرقم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = 'الاسم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value = 'الهاتف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value = 'الدور';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value = 'الحالة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value = 'تاريخ الإنشاء';

    // بيانات المستخدمين
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = user['id'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = user['name'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = user['phone'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = user['role'];
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = user['is_active'] == 1 ? 'نشط' : 'غير نشط';
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = user['created_at'];
    }
  }

  /// إضافة ورقة الإحصائيات
  static void _addStatisticsSheet(
    Excel excel,
    List<Map<String, dynamic>> customers,
    List<Map<String, dynamic>> products,
    List<Map<String, dynamic>> invoices,
    List<Map<String, dynamic>> collections,
  ) {
    final sheet = excel['الإحصائيات'];
    
    // حساب الإحصائيات
    final totalCustomers = customers.length;
    final totalProducts = products.length;
    final totalInvoices = invoices.length;
    final totalCollections = collections.length;
    
    double totalSales = 0;
    double totalPaid = 0;
    double totalRemaining = 0;
    
    for (final invoice in invoices) {
      totalSales += invoice['total_amount'] ?? 0;
      totalPaid += invoice['paid_amount'] ?? 0;
      totalRemaining += invoice['remaining_amount'] ?? 0;
    }
    
    double totalCollectionsAmount = 0;
    for (final collection in collections) {
      totalCollectionsAmount += collection['amount'] ?? 0;
    }

    // إضافة الإحصائيات
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'الإحصائيات العامة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1)).value = 'إجمالي العملاء';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 1)).value = totalCustomers;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2)).value = 'إجمالي المنتجات';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 2)).value = totalProducts;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 3)).value = 'إجمالي الفواتير';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 3)).value = totalInvoices;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 4)).value = 'إجمالي التحصيلات';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 4)).value = totalCollections;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 6)).value = 'المبيعات';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 7)).value = 'إجمالي المبيعات';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 7)).value = totalSales;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 8)).value = 'إجمالي المدفوع';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 8)).value = totalPaid;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 9)).value = 'إجمالي المتبقي';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 9)).value = totalRemaining;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 10)).value = 'إجمالي التحصيلات';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 10)).value = totalCollectionsAmount;
    
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 12)).value = 'تاريخ التصدير';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 12)).value = DateTime.now().toString();
  }

  /// عرض خيارات التصدير
  static Future<void> showExportOptions(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.file_download, color: Color(0xFF4A90E2)),
            const SizedBox(width: 8),
            const Text('تصدير البيانات'),
          ],
        ),
        content: const Text('اختر نوع التصدير:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              await _exportAndShareData(context);
            },
            icon: const Icon(Icons.table_chart),
            label: const Text('تصدير Excel'),
          ),
        ],
      ),
    );
  }

  /// تصدير ومشاركة البيانات
  static Future<void> _exportAndShareData(BuildContext context) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري تصدير البيانات...'),
            ],
          ),
        ),
      );

      final filePath = await exportAllDataToExcel();
      
      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      if (filePath != null) {
        // مشاركة الملف
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'تصدير بيانات InvoFast',
        );
        
        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في تصدير البيانات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
} 