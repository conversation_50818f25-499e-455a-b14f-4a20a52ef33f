import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../database/database_helper.dart';
import '../services/auth_service.dart';

class InvoiceDetailsService {
  /// إنشاء تفاصيل الفاتورة في شكل جدول مفصل
  static Future<String> createDetailedInvoiceTable(
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    final totalAmount = invoice['total_amount'] ?? 0.0;
    final paidAmount = invoice['paid_amount'] ?? 0.0;
    final remainingAmount = invoice['remaining_amount'] ?? 0.0;
    final invoiceDate = invoice['date'] ?? '';
    final invoiceNumber = invoice['invoice_number'] ?? '';
    final invoiceId = invoice['id'];
    final notes = invoice['notes'] ?? '';

    // جلب تفاصيل التحصيلات
    List<Map<String, dynamic>> collections = [];
    if (invoiceId != null) {
      try {
        final dbHelper = DatabaseHelper();
        collections = await dbHelper.getCollectionsByInvoice(invoiceId);
      } catch (e) {
        print('خطأ في جلب التحصيلات: $e');
      }
    }

    // جلب اسم المستخدم الذي أنشأ الفاتورة
    String createdBy = invoice['created_by_name'] ?? 'غير محدد';

    // إنشاء الجدول المفصل
    String table = '''
InvoFast
كشف حساب فاتورة

مرحباً ${customer['name']}،

┌─────────────────────────────────────────────────────────────────────────────┐
│ رقم الفاتورة: $invoiceNumber                    التاريخ: $invoiceDate        │
├─────────────────────────────────────────────────────────────────────────────┤
│ المبلغ الإجمالي للفاتورة: ${totalAmount.toStringAsFixed(2)} ج.م                    │
├─────────────────────────────────────────────────────────────────────────────┤''';

    // إضافة تفاصيل التحصيلات
    if (collections.isNotEmpty) {
      table += '''
│ إجمالي التحصيلات:                                                          │''';
      
      for (int i = 0; i < collections.length; i++) {
        final collection = collections[i];
        final amount = collection['amount'] ?? 0.0;
        final date = collection['date'] ?? '';
        final collectorName = collection['collector_name'] ?? '';
        final paymentMethod = collection['payment_method'] ?? 'نقداً';
        final notes = collection['notes'] ?? '';

        table += '''
│ ${i + 1}. تم دفع مبلغ ${amount.toStringAsFixed(2)} ج.م بتاريخ $date                    │
│    المحصل: $collectorName                                                    │
│    طريقة الدفع: $paymentMethod                                              │''';
        
        if (notes.isNotEmpty) {
          table += '''
│    ملاحظات: $notes                                                          │''';
        }
      }
    } else if (paidAmount > 0) {
      // إذا كان هناك مبلغ مدفوع عند إنشاء الفاتورة
      table += '''
│ تم دفع مبلغ ${paidAmount.toStringAsFixed(2)} ج.م عند إنشاء الفاتورة بتاريخ $invoiceDate    │
│ المحصل: $createdBy                                                          │
│ طريقة الدفع: نقداً                                                          │''';
    }

    // إضافة المبلغ المتبقي
    if (remainingAmount > 0) {
      table += '''
├─────────────────────────────────────────────────────────────────────────────┤
│ المبلغ المتبقي: ${remainingAmount.toStringAsFixed(2)} ج.م                                    │''';
    }

    // إضافة ملاحظات الفاتورة
    if (notes.isNotEmpty) {
      table += '''
├─────────────────────────────────────────────────────────────────────────────┤
│ ملاحظات: $notes                                                              │''';
    }

    // إغلاق الجدول
    table += '''
├─────────────────────────────────────────────────────────────────────────────┤
│ المحصل: $createdBy                                                          │
│ طريقة الدفع: نقداً                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ شكراً لتعاملكم معنا                                                          │
│ للاستفسار: 01125312343                                                       │
└─────────────────────────────────────────────────────────────────────────────┘''';

    return table;
  }

  /// عرض تفاصيل الفاتورة في نافذة منبثقة
  static Future<void> showInvoiceDetails(
    BuildContext context,
    Map<String, dynamic> invoice,
    Map<String, dynamic> customer,
  ) async {
    try {
      final details = await createDetailedInvoiceTable(invoice, customer);
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.receipt, color: Color(0xFF4A90E2)),
              const SizedBox(width: 8),
              Text('تفاصيل الفاتورة ${invoice['invoice_number']}'),
            ],
          ),
          content: SingleChildScrollView(
            child: Container(
              width: double.maxFinite,
              child: SelectableText(
                details,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                  height: 1.2,
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                try {
                  await _copyToClipboard(details);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ تفاصيل الفاتورة إلى الحافظة'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في النسخ: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              icon: const Icon(Icons.copy),
              label: const Text('نسخ'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض تفاصيل الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// نسخ النص إلى الحافظة
  static Future<void> _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }
} 