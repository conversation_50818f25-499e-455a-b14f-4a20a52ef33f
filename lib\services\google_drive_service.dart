import 'dart:convert';
import 'dart:io';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import '../database/database_helper.dart';

class GoogleDriveService {
  static const String _folderId = '1eQQ4tTHrtkWGYooG-9mmDJh1ULerTkqB';
  static const String _clientId = 'YOUR_CLIENT_ID.apps.googleusercontent.com';
  static const String _clientSecret = 'YOUR_CLIENT_SECRET';
  static const List<String> _scopes = [drive.DriveApi.driveScope];

  static drive.DriveApi? _driveApi;
  static bool _isInitialized = false;

  // تهيئة خدمة Google Drive
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) return true;

      // إنشاء credentials
      final credentials = ServiceAccountCredentials.fromJson({
        "type": "service_account",
        "project_id": "your-project-id",
        "private_key_id": "your-private-key-id",
        "private_key":
            "-----B<PERSON>IN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n",
        "client_email":
            "<EMAIL>",
        "client_id": "your-client-id",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url":
            "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url":
            "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com",
      });

      // إنشاء client
      final client = await clientViaServiceAccount(credentials, _scopes);
      _driveApi = drive.DriveApi(client);

      _isInitialized = true;
      print('✅ تم تهيئة Google Drive بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في تهيئة Google Drive: $e');
      return false;
    }
  }

  // رفع نسخة احتياطية
  static Future<bool> uploadBackup() async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) return false;
      }

      // إنشاء نسخة احتياطية من قاعدة البيانات
      final backupData = await _createBackupData();
      final backupJson = jsonEncode(backupData);

      // إنشاء اسم الملف مع التاريخ والوقت
      final now = DateTime.now();
      final fileName =
          'atlas_backup_${now.year}_${now.month.toString().padLeft(2, '0')}_${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}_${now.minute.toString().padLeft(2, '0')}_${now.second.toString().padLeft(2, '0')}.json';

      // رفع الملف إلى Google Drive
      final file = drive.File()
        ..name = fileName
        ..parents = [_folderId]
        ..mimeType = 'application/json';

      final media = drive.Media(
        Stream.value(utf8.encode(backupJson)),
        backupJson.length,
      );

      await _driveApi!.files.create(file, uploadMedia: media);

      print('✅ تم رفع النسخة الاحتياطية بنجاح: $fileName');
      return true;
    } catch (e) {
      print('❌ خطأ في رفع النسخة الاحتياطية: $e');
      return false;
    }
  }

  // إنشاء بيانات النسخة الاحتياطية
  static Future<Map<String, dynamic>> _createBackupData() async {
    final dbHelper = DatabaseHelper();
    final db = await dbHelper.database;

    try {
      // جلب جميع البيانات
      final customers = await db.query('customers');
      final invoices = await db.query('invoices');
      final collections = await db.query('collections');
      final invoiceItems = await db.query('invoice_items');
      final products = await db.query('products');
      final users = await db.query('users');

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
        'data': {
          'customers': customers,
          'invoices': invoices,
          'collections': collections,
          'invoice_items': invoiceItems,
          'products': products,
          'users': users,
        },
        'summary': {
          'customers_count': customers.length,
          'invoices_count': invoices.length,
          'collections_count': collections.length,
          'invoice_items_count': invoiceItems.length,
          'products_count': products.length,
          'users_count': users.length,
        },
      };
    } catch (e) {
      print('❌ خطأ في إنشاء بيانات النسخة الاحتياطية: $e');
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'error': e.toString(),
        'data': {},
        'summary': {},
      };
    }
  }

  // جلب قائمة النسخ الاحتياطية
  static Future<List<Map<String, dynamic>>> getBackups() async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) return [];
      }

      final files = await _driveApi!.files.list(
        q: "'$_folderId' in parents and name contains 'atlas_backup'",
        orderBy: 'createdTime desc',
      );

      return files.files
              ?.map(
                (file) => {
                  'id': file.id,
                  'name': file.name,
                  'created': file.createdTime,
                  'size': file.size,
                },
              )
              .toList() ??
          [];
    } catch (e) {
      print('❌ خطأ في جلب قائمة النسخ الاحتياطية: $e');
      return [];
    }
  }

  // استعادة من نسخة احتياطية
  static Future<Map<String, dynamic>> restoreFromBackup(String fileId) async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) {
          return {'success': false, 'message': 'فشل في تهيئة Google Drive'};
        }
      }

      // تحميل الملف
      final file = await _driveApi!.files.get(fileId);
      final media =
          await _driveApi!.files.get(
                fileId,
                downloadOptions: drive.DownloadOptions.fullMedia,
              )
              as drive.Media;

      final content = await media.stream.transform(utf8.decoder).join();
      final backupData = jsonDecode(content) as Map<String, dynamic>;

      // استعادة البيانات
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      await db.transaction((txn) async {
        // حذف البيانات الحالية
        await txn.delete('collections');
        await txn.delete('invoice_items');
        await txn.delete('invoices');
        await txn.delete('customers');
        await txn.delete('products');
        // لا نحذف المستخدمين لتجنب مشاكل تسجيل الدخول

        final data = backupData['data'] as Map<String, dynamic>;

        // استعادة العملاء
        for (final customer in data['customers'] as List) {
          await txn.insert('customers', customer);
        }

        // استعادة المنتجات
        for (final product in data['products'] as List) {
          await txn.insert('products', product);
        }

        // استعادة الفواتير
        for (final invoice in data['invoices'] as List) {
          await txn.insert('invoices', invoice);
        }

        // استعادة منتجات الفواتير
        for (final item in data['invoice_items'] as List) {
          await txn.insert('invoice_items', item);
        }

        // استعادة التحصيلات
        for (final collection in data['collections'] as List) {
          await txn.insert('collections', collection);
        }
      });

      return {
        'success': true,
        'message': 'تمت الاستعادة بنجاح',
        'restored_customers': (backupData['data']['customers'] as List).length,
        'restored_products': (backupData['data']['products'] as List).length,
        'restored_invoices': (backupData['data']['invoices'] as List).length,
        'restored_collections':
            (backupData['data']['collections'] as List).length,
        'restored_invoice_items':
            (backupData['data']['invoice_items'] as List).length,
      };
    } catch (e) {
      print('❌ خطأ في استعادة النسخة الاحتياطية: $e');
      return {'success': false, 'message': 'خطأ في الاستعادة: $e'};
    }
  }

  // حذف نسخة احتياطية قديمة
  static Future<bool> deleteBackup(String fileId) async {
    try {
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) return false;
      }

      await _driveApi!.files.delete(fileId);
      print('✅ تم حذف النسخة الاحتياطية بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في حذف النسخة الاحتياطية: $e');
      return false;
    }
  }

  // تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ فقط)
  static Future<void> cleanupOldBackups() async {
    try {
      final backups = await getBackups();
      if (backups.length > 10) {
        final oldBackups = backups.skip(10).toList();
        for (final backup in oldBackups) {
          await deleteBackup(backup['id']);
        }
        print('✅ تم تنظيف ${oldBackups.length} نسخة احتياطية قديمة');
      }
    } catch (e) {
      print('❌ خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
}
