# دليل تشغيل تطبيق أطلس للمستلزمات الطبية على Android Studio

## 🚨 المشكلة الحالية
التطبيق لا يعمل على Android Studio لأن:
1. ل<PERSON> يوجد جهاز Android متصل
2. ل<PERSON> يوجد محاكي Android نشط
3. المشروع يحتاج إلى إعدادات إضافية

## 🔧 الحلول المتاحة

### الحل الأول: تشغيل التطبيق على الويب (الأسرع)
```bash
flutter run -d chrome
```
أو
```bash
flutter run -d edge
```

### الحل الثاني: تشغيل التطبيق على Windows
```bash
flutter run -d windows
```

### الحل الثالث: إعداد محاكي Android

#### 1. تثبيت Android Studio
- تحميل Android Studio من: https://developer.android.com/studio
- تثبيت Android Studio مع Android SDK

#### 2. إن<PERSON><PERSON>ء محاكي Android
1. افتح Android Studio
2. اذهب إلى **Tools > AVD Manager**
3. اضغط على **Create Virtual Device**
4. اختر جهاز (مثل Pixel 4)
5. اختر نظام Android (مثل API 33)
6. اضغط **Finish**

#### 3. تشغيل المحاكي
1. في AVD Manager، اضغط على زر التشغيل ▶️ بجانب المحاكي
2. انتظر حتى يفتح المحاكي
3. في Terminal، اكتب:
```bash
flutter run
```

### الحل الرابع: ربط جهاز Android حقيقي

#### 1. تفعيل وضع المطور
1. اذهب إلى **Settings > About Phone**
2. اضغط على **Build Number** 7 مرات
3. ستحصل على رسالة "You are now a developer"

#### 2. تفعيل USB Debugging
1. اذهب إلى **Settings > Developer Options**
2. فعّل **USB Debugging**
3. وصل الجهاز بالكمبيوتر عبر USB
4. اكتب في Terminal:
```bash
flutter devices
```

## 📱 تشغيل التطبيق

### في Android Studio:
1. افتح المشروع في Android Studio
2. انتظر حتى يكتمل تحميل Gradle
3. اختر جهاز من القائمة المنسدلة
4. اضغط على زر التشغيل ▶️

### في Terminal:
```bash
# تأكد من أنك في المجلد الصحيح
cd atlas_medical_supplies

# تحقق من الأجهزة المتاحة
flutter devices

# شغل التطبيق
flutter run
```

## 🔍 حل المشاكل الشائعة

### مشكلة: "No supported devices connected"
**الحل:**
```bash
# تشغيل على الويب
flutter run -d chrome

# أو تشغيل على Windows
flutter run -d windows
```

### مشكلة: "Gradle build failed"
**الحل:**
```bash
# تنظيف المشروع
flutter clean

# إعادة تحميل التبعيات
flutter pub get

# إعادة البناء
flutter run
```

### مشكلة: "Android SDK not found"
**الحل:**
1. تأكد من تثبيت Android Studio
2. تأكد من تثبيت Android SDK
3. أضف Android SDK إلى PATH

## 🎯 الطريقة الموصى بها

### للاختبار السريع:
```bash
flutter run -d chrome
```

### للتطوير:
1. استخدم محاكي Android
2. أو ربط جهاز Android حقيقي

### للإنتاج:
```bash
flutter build apk --release
```

## 📋 خطوات سريعة للتشغيل

1. **افتح Terminal في مجلد المشروع:**
   ```bash
   cd atlas_medical_supplies
   ```

2. **تحقق من الأجهزة المتاحة:**
   ```bash
   flutter devices
   ```

3. **شغل التطبيق على الويب (الأسرع):**
   ```bash
   flutter run -d chrome
   ```

4. **أو شغل على Windows:**
   ```bash
   flutter run -d windows
   ```

## 🔧 إعدادات إضافية

### تفعيل دعم Windows:
```bash
flutter config --enable-windows-desktop
```

### تفعيل دعم الويب:
```bash
flutter config --enable-web
```

### تحديث Flutter:
```bash
flutter upgrade
```

## 📞 الدعم

إذا استمرت المشكلة:
1. تأكد من تثبيت Flutter بشكل صحيح
2. تأكد من تثبيت Android Studio
3. تأكد من إعدادات PATH
4. جرب تشغيل التطبيق على الويب أولاً

## ✅ التحقق من الإعداد

```bash
flutter doctor
```

يجب أن تظهر جميع العناصر بخضر ✅

---

**ملاحظة:** التطبيق جاهز للاستخدام على الويب أو Windows حتى لو لم يكن لديك جهاز Android! 