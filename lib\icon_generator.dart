import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await generateAppIcon();
}

Future<void> generateAppIcon() async {
  print('🎨 إنشاء أيقونة InvoFast...');
  
  try {
    // الحصول على مجلد مؤقت
    final tempDir = await getTemporaryDirectory();
    print('📁 المجلد المؤقت: ${tempDir.path}');
    
    // إنشاء الأيقونة
    final iconData = await createAtlasIcon();
    
    // حفظ الأيقونة في المجلد المؤقت
    final iconFile = File('${tempDir.path}/atlas_icon_simple.png');
    await iconFile.writeAsBytes(iconData);
    print('✅ تم إنشاء الأيقونة: ${iconFile.path}');
    
    // إنشاء الأيقونة التكيفية
    final adaptiveIconData = await createAdaptiveIcon();
    final adaptiveIconFile = File('${tempDir.path}/atlas_icon_adaptive.png');
    await adaptiveIconFile.writeAsBytes(adaptiveIconData);
    print('✅ تم إنشاء الأيقونة التكيفية: ${adaptiveIconFile.path}');
    
    // محاولة نسخ الأيقونات إلى مجلد الأصول
    try {
      final assetsDir = Directory('assets/images');
      if (!await assetsDir.exists()) {
        await assetsDir.create(recursive: true);
        print('✅ تم إنشاء مجلد assets/images');
      }
      
      // نسخ الأيقونة الرئيسية
      final finalIconFile = File('assets/images/atlas_icon_simple.png');
      await iconFile.copy(finalIconFile.path);
      print('✅ تم نسخ الأيقونة إلى: ${finalIconFile.path}');
      
      // نسخ الأيقونة التكيفية
      final finalAdaptiveIconFile = File('assets/images/atlas_icon_adaptive.png');
      await adaptiveIconFile.copy(finalAdaptiveIconFile.path);
      print('✅ تم نسخ الأيقونة التكيفية إلى: ${finalAdaptiveIconFile.path}');
      
    } catch (e) {
      print('⚠️ تحذير: لا يمكن نسخ الأيقونات إلى مجلد الأصول: $e');
      print('📁 الأيقونات موجودة في المجلد المؤقت: ${tempDir.path}');
    }
    
    print('🎉 تم إنشاء جميع الأيقونات بنجاح!');
    print('📱 يمكنك الآن تشغيل: flutter pub run flutter_launcher_icons');
    
  } catch (e) {
    print('❌ خطأ في إنشاء الأيقونة: $e');
  }
}

Future<Uint8List> createAtlasIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = Size(1024, 1024);
  
  // رسم خلفية دائرية بتدرج لوني
  final center = Offset(size.width / 2, size.height / 2);
  final radius = 480.0;
  
  final paint = Paint()
    ..shader = RadialGradient(
      colors: [
        const Color(0xFF40E0D0), // تركوازي
        const Color(0xFF20B2AA), // تركوازي متوسط
        const Color(0xFF008B8B), // تركوازي داكن
      ],
      stops: const [0.0, 0.5, 1.0],
    ).createShader(Rect.fromCircle(center: center, radius: radius));
  
  canvas.drawCircle(center, radius, paint);
  
  // رسم الصليب الطبي
  final crossPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;
  
  const crossWidth = 16.0;
  const crossHeight = 240.0;
  
  // الخط العمودي
  final verticalRect = Rect.fromCenter(
    center: center,
    width: crossWidth,
    height: crossHeight,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(verticalRect, const Radius.circular(8)),
    crossPaint,
  );
  
  // الخط الأفقي
  final horizontalRect = Rect.fromCenter(
    center: center,
    width: crossHeight,
    height: crossWidth,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(horizontalRect, const Radius.circular(8)),
    crossPaint,
  );
  
  // إضافة النص "ATLAS"
  const textStyle = TextStyle(
    color: Colors.white,
    fontSize: 120,
    fontWeight: FontWeight.bold,
  );
  
  final textPainter = TextPainter(
    text: const TextSpan(text: 'ATLAS', style: textStyle),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();
  
  final textOffset = Offset(
    center.dx - textPainter.width / 2,
    center.dy + 80,
  );
  textPainter.paint(canvas, textOffset);
  
  // إضافة النص "MEDICAL"
  const medicalTextStyle = TextStyle(
    color: Colors.white,
    fontSize: 48,
    fontWeight: FontWeight.normal,
  );
  
  final medicalTextPainter = TextPainter(
    text: const TextSpan(text: 'MEDICAL', style: medicalTextStyle),
    textDirection: TextDirection.ltr,
  );
  medicalTextPainter.layout();
  
  final medicalTextOffset = Offset(
    center.dx - medicalTextPainter.width / 2,
    center.dy + 200,
  );
  medicalTextPainter.paint(canvas, medicalTextOffset);
  
  // تحويل إلى صورة
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  return byteData!.buffer.asUint8List();
}

Future<Uint8List> createAdaptiveIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = Size(1024, 1024);
  
  // خلفية شفافة
  canvas.drawColor(Colors.transparent, BlendMode.clear);
  
  const whiteColor = Colors.white;
  final center = Offset(size.width / 2, size.height / 2);
  
  // رسم الصليب الطبي فقط
  final crossPaint = Paint()
    ..color = whiteColor
    ..style = PaintingStyle.fill;
  
  const crossWidth = 16.0;
  const crossHeight = 240.0;
  
  // الخط العمودي
  final verticalRect = Rect.fromCenter(
    center: center,
    width: crossWidth,
    height: crossHeight,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(verticalRect, const Radius.circular(8)),
    crossPaint,
  );
  
  // الخط الأفقي
  final horizontalRect = Rect.fromCenter(
    center: center,
    width: crossHeight,
    height: crossWidth,
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(horizontalRect, const Radius.circular(8)),
    crossPaint,
  );
  
  // إضافة النص "ATLAS" فقط
  const textStyle = TextStyle(
    color: whiteColor,
    fontSize: 120,
    fontWeight: FontWeight.bold,
  );
  
  final textPainter = TextPainter(
    text: const TextSpan(text: 'ATLAS', style: textStyle),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();
  
  final textOffset = Offset(
    center.dx - textPainter.width / 2,
    center.dy + 80,
  );
  textPainter.paint(canvas, textOffset);
  
  // تحويل إلى صورة
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  return byteData!.buffer.asUint8List();
} 