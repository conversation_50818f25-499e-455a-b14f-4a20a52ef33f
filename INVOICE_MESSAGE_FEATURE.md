# ميزة إنشاء رسائل الفواتير

## نظرة عامة

تم إضافة ميزة جديدة لإنشاء رسائل نصية احترافية لملخص الفواتير باللغة العربية. هذه الميزة تتيح للمستخدمين إنشاء رسائل واضحة ومهنية تحتوي على جميع تفاصيل الفاتورة.

## الملفات المضافة

### 1. `lib/services/invoice_message_service.dart`
الملف الرئيسي الذي يحتوي على جميع دوال إنشاء الرسائل.

### 2. `test/invoice_message_test.dart`
ملف الاختبارات للتحقق من صحة عمل الدوال.

## الدوال المتاحة

### 1. `generateInvoiceMessage(invoice)`
الدالة الرئيسية لإنشاء رسالة كاملة للفاتورة تتضمن:
- اسم العميل
- رقم الفاتورة
- تاريخ إنشاء الفاتورة
- تفاصيل المنتجات (الاسم + الكمية + السعر لكل وحدة + الإجمالي لكل صنف)
- إجمالي الفاتورة
- المبلغ المدفوع
- المبلغ المتبقي
- تاريخ التحصيل إن وُجد
- ملاحظات الفاتورة إن وُجدت

**مثال للاستخدام:**
```dart
final message = await InvoiceMessageService.generateInvoiceMessage(invoice);
```

### 2. `generateSimpleInvoiceMessage(invoice)`
دالة لإنشاء رسالة مبسطة بدون تفاصيل المنتجات.

**مثال للاستخدام:**
```dart
final message = InvoiceMessageService.generateSimpleInvoiceMessage(invoice);
```

### 3. `generatePaymentReminderMessage(invoice)`
دالة لإنشاء رسالة تذكير بالدفع.

**مثال للاستخدام:**
```dart
final message = InvoiceMessageService.generatePaymentReminderMessage(invoice);
```

### 4. `generatePaymentConfirmationMessage(invoice, paidAmount)`
دالة لإنشاء رسالة تأكيد الدفع.

**مثال للاستخدام:**
```dart
final message = InvoiceMessageService.generatePaymentConfirmationMessage(invoice, 1500.0);
```

## مثال على الرسالة المُنشأة

```
السيد/ أحمد محمد
فاتورة رقم: INV-2024-001
تاريخ: 15/01/2024

المنتجات:
- باراسيتامول 500 مجم × 10 = 50.00 ج.م
- فيتامين سي × 5 = 75.00 ج.م
- كريم مرطب × 2 = 120.00 ج.م

إجمالي الفاتورة: 245.00 ج.م
تم الدفع: 100.00 ج.م
المتبقي: 145.00 ج.م
تاريخ التحصيل: 20/01/2024

ملاحظات: يرجى الدفع خلال أسبوع

نشكر تعاونكم.
```

## الميزات

### 1. التنسيق الاحترافي
- استخدام تنسيق العملة المصري مع فواصل الآلاف
- تنسيق التواريخ بالشكل العربي
- تخطيط واضح ومنظم للرسالة

### 2. المرونة
- دعم الحالات المختلفة (دفع كامل - متبقي - بدون تحصيل)
- معالجة البيانات المفقودة بشكل آمن
- رسائل خطأ واضحة في حالة حدوث مشاكل

### 3. سهولة الاستخدام
- دوال ثابتة (static) لا تحتاج لإنشاء كائنات
- واجهة بسيطة وواضحة
- توثيق شامل للدوال

### 4. الأمان
- معالجة الأخطاء بشكل شامل
- التحقق من صحة البيانات
- رسائل احتياطية في حالة الفشل

## التكامل مع واجهة المستخدم

تم إضافة زر "إنشاء رسالة الفاتورة" في شاشة تعديل الفاتورة (`add_edit_invoice_screen.dart`) الذي:
- يعرض الرسالة في نافذة منبثقة
- يوفر خيار نسخ الرسالة
- يعرض رسائل خطأ واضحة في حالة حدوث مشاكل

## الاختبارات

تم إنشاء مجموعة شاملة من الاختبارات تغطي:
- إنشاء الرسائل الأساسية
- معالجة البيانات المفقودة
- تنسيق العملة والتواريخ
- رسائل التذكير والتأكيد

## الاستخدام المستقبلي

يمكن استخدام هذه الخدمة في:
- إرسال رسائل SMS تلقائية
- إرسال رسائل WhatsApp
- إنشاء تقارير نصية
- إرسال إشعارات للعملاء
- إنشاء ملفات PDF نصية

## المتطلبات

- Flutter SDK
- حزمة `intl` للتنسيق
- قاعدة البيانات المحلية (SQLite)

## التحديثات المستقبلية

- إضافة دعم للغات أخرى
- إضافة قوالب رسائل قابلة للتخصيص
- دعم إرسال الرسائل تلقائياً
- إضافة خيارات تنسيق إضافية 