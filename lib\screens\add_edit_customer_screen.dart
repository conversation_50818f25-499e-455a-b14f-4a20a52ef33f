import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import '../database/database_helper.dart';
import '../models/customer_phone.dart';

class AddEditCustomerScreen extends StatefulWidget {
  final Map<String, dynamic>? customer;

  const AddEditCustomerScreen({super.key, this.customer});

  @override
  State<AddEditCustomerScreen> createState() => _AddEditCustomerScreenState();
}

class _AddEditCustomerScreenState extends State<AddEditCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedGovernorate;
  String? _selectedArea;
  List<String> _governorates = [];
  List<String> _areas = [];
  List<Map<String, dynamic>> _governoratesData = [];
  bool _isLoading = false;

  // قائمة أرقام الهواتف للعميل الجديد
  List<CustomerPhone> _customerPhones = [];

  @override
  void initState() {
    super.initState();
    _loadGovernorates();
    
    // التحقق من وجود معاملات محافظة محددة مسبقاً
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null && args['preSelectedGovernorate'] != null) {
        setState(() {
          _selectedGovernorate = args['preSelectedGovernorate'];
        });
        // تحميل المناطق للمحافظة المحددة
        if (_governorates.isNotEmpty) {
          _loadAreas(_selectedGovernorate!);
        }
      }
    });
    
    if (widget.customer != null) {
      _nameController.text = widget.customer!['name'] ?? '';
      _addressController.text = widget.customer!['address'] ?? '';
      _notesController.text = widget.customer!['notes'] ?? '';
      _selectedGovernorate = widget.customer!['governorate'];
      _selectedArea = widget.customer!['area'];
      _loadCustomerPhones(widget.customer!['id']);
    }
  }

  void _addPhone() {
    final phoneController = TextEditingController();
    final typeController = TextEditingController(text: 'الرئيسي');
    final notesController = TextEditingController();
    bool isPrimary = _customerPhones.isEmpty; // أول رقم يكون أساسي

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.phone, color: Color(0xFF4A90E2)),
              const SizedBox(width: 8),
              const Text('إضافة رقم هاتف'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف *',
                    hintText: 'مثال: 01110473536',
                    prefixIcon: Icon(Icons.phone, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4A90E2), width: 2),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  onChanged: (value) {
                    // تحسين تنسيق الرقم تلقائياً
                    if (value.length == 11 && value.startsWith('01')) {
                      // تنسيق رقم مصري
                      final formatted = '${value.substring(0, 3)}-${value.substring(3, 7)}-${value.substring(7)}';
                      phoneController.text = formatted;
                      phoneController.selection = TextSelection.fromPosition(
                        TextPosition(offset: formatted.length),
                      );
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: typeController,
                  decoration: const InputDecoration(
                    labelText: 'نوع الهاتف',
                    hintText: 'مثال: الرئيسي، العمل، المنزل',
                    prefixIcon: Icon(Icons.category, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4A90E2), width: 2),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    hintText: 'ملاحظات إضافية (اختياري)',
                    prefixIcon: Icon(Icons.note, color: Color(0xFF4A90E2)),
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4A90E2), width: 2),
                    ),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber[300]!),
                  ),
                  child: CheckboxListTile(
                    title: const Text(
                      'رقم الهاتف الأساسي',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: const Text(
                      'سيتم استخدام هذا الرقم كرقم رئيسي للعميل',
                      style: TextStyle(fontSize: 12),
                    ),
                    value: isPrimary,
                    onChanged: (value) {
                      setDialogState(() {
                        isPrimary = value ?? false;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final phoneNumber = phoneController.text.trim();
                if (phoneNumber.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال رقم الهاتف'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // التحقق من صحة تنسيق الرقم
                final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
                if (cleanNumber.length < 10 || cleanNumber.length > 11) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال رقم هاتف صحيح (10-11 رقم)'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // التحقق من عدم تكرار الرقم
                final phoneExists = _customerPhones.any(
                  (phone) => phone.phone == cleanNumber,
                );

                if (phoneExists) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('رقم الهاتف موجود بالفعل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final newPhone = CustomerPhone(
                  customerId: 0, // سيتم تحديثه عند حفظ العميل
                  phone: cleanNumber,
                  phoneType: typeController.text.trim(),
                  isPrimary: isPrimary,
                  notes: notesController.text.trim().isEmpty
                      ? null
                      : notesController.text.trim(),
                );

                setState(() {
                  if (isPrimary) {
                    // إلغاء تعيين جميع الأرقام كأساسية
                    _customerPhones = _customerPhones
                        .map((phone) => phone.copyWith(isPrimary: false))
                        .toList();
                  }
                  _customerPhones.add(newPhone);
                });

                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isPrimary 
                          ? 'تم إضافة رقم الهاتف الأساسي'
                          : 'تم إضافة رقم الهاتف',
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A90E2),
                foregroundColor: Colors.white,
              ),
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _removePhone(int index) {
    setState(() {
      final removedPhone = _customerPhones[index];
      _customerPhones.removeAt(index);

      // إذا كان الرقم المحذوف أساسي وكانت هناك أرقام أخرى، اجعل الأول أساسي
      if (removedPhone.isPrimary && _customerPhones.isNotEmpty) {
        _customerPhones[0] = _customerPhones[0].copyWith(isPrimary: true);
      }
    });
  }

  Future<void> _loadGovernorates() async {
    try {
      // تحميل البيانات من ملف JSON
      final String response = await rootBundle.loadString(
        'assets/data/governorates.json',
      );
      final List<dynamic> data = json.decode(response);

      setState(() {
        _governoratesData = List<Map<String, dynamic>>.from(data);
        _governorates = data
            .map((item) => item['governorate'] as String)
            .toList();
      });

      if (_selectedGovernorate != null) {
        _loadAreas(_selectedGovernorate!);
      }
    } catch (e) {
      print('خطأ في تحميل بيانات المحافظات: $e');
      // استخدام قائمة احتياطية في حالة فشل تحميل الملف
      _loadFallbackGovernorates();
    }
  }

  void _loadFallbackGovernorates() {
    // قائمة احتياطية للمحافظات في حالة فشل تحميل الملف
    final governorates = [
      'القاهرة',
      'الجيزة',
      'الإسكندرية',
      'الشرقية',
      'الغربية',
      'المنوفية',
      'القليوبية',
      'البحيرة',
      'كفر الشيخ',
      'دمياط',
      'الدقهلية',
      'المنيا',
      'أسيوط',
      'سوهاج',
      'قنا',
      'الأقصر',
      'أسوان',
      'بني سويف',
      'الفيوم',
      'مطروح',
      'شمال سيناء',
      'جنوب سيناء',
      'البحر الأحمر',
      'الوادي الجديد',
      'بورسعيد',
      'الإسماعيلية',
      'السويس',
    ];

    setState(() {
      _governorates = governorates;
    });

    if (_selectedGovernorate != null) {
      _loadFallbackAreas(_selectedGovernorate!);
    }
  }

  Future<void> _loadAreas(String governorate) async {
    try {
      // البحث عن المحافظة المختارة في البيانات المحملة
      final governorateData = _governoratesData.firstWhere(
        (item) => item['governorate'] == governorate,
        orElse: () => {'areas': []},
      );

      final areas = List<String>.from(governorateData['areas'] ?? []);

      setState(() {
        _areas = areas;
        _selectedArea = null; // إعادة تعيين المنطقة المختارة
      });
    } catch (e) {
      print('خطأ في تحميل المناطق: $e');
      _loadFallbackAreas(governorate);
    }
  }

  void _loadFallbackAreas(String governorate) {
    // قائمة احتياطية للمناطق في حالة فشل تحميل البيانات
    final areasMap = {
      'القاهرة': [
        'وسط البلد',
        'المعادي',
        'مدينة نصر',
        'مصر الجديدة',
        'الزمالك',
        'جاردن سيتي',
      ],
      'الجيزة': [
        'الدقي',
        'المهندسين',
        'الهرم',
        '6 أكتوبر',
        'الشيخ زايد',
        'العجوزة',
      ],
      'الإسكندرية': [
        'سموحة',
        'سيدي جابر',
        'الإبراهيمية',
        'ميامي',
        'العجمي',
        'باكوس',
      ],
      'الشرقية': ['الزقازيق', 'العاشر من رمضان', 'بلبيس', 'أبو كبير', 'فاقوس'],
      'الغربية': ['طنطا', 'المحلة الكبرى', 'كفر الزيات', 'سمنود', 'زفتى'],
      'المنوفية': ['شبين الكوم', 'تلا', 'بركة السبع', 'قويسنا', 'أشمون'],
      'القليوبية': [
        'بنها',
        'شبرا الخيمة',
        'القناطر الخيرية',
        'الخانكة',
        'كفر شكر',
      ],
      'البحيرة': ['دمنهور', 'كفر الدوار', 'رشيد', 'إدكو', 'أبو المطامير'],
      'كفر الشيخ': ['كفر الشيخ', 'دسوق', 'فوه', 'مطوبس', 'سيدي سالم'],
      'دمياط': ['دمياط', 'فارسكور', 'الزرقا', 'كفر البطيخ', 'رأس البر'],
      'الدقهلية': ['المنصورة', 'ميت غمر', 'أجا', 'السنبلاوين', 'منية النصر'],
      'المنيا': ['المنيا', 'مطاي', 'أبو قرقاص', 'ملوي', 'بني مزار'],
      'أسيوط': ['أسيوط', 'ديروط', 'أبنوب', 'أبو تيج', 'ساحل سليم'],
      'سوهاج': ['سوهاج', 'أخميم', 'البلينا', 'المراغة', 'طهطا'],
      'قنا': ['قنا', 'قوص', 'نقادة', 'دشنا', 'فطيرة'],
      'الأقصر': ['الأقصر', 'إسنا', 'الطود', 'بياضة العرب', 'الزينية'],
      'أسوان': ['أسوان', 'كوم أمبو', 'دراو', 'نصر النوبة', 'كلابشة'],
      'بني سويف': ['بني سويف', 'الواسطي', 'ناصر', 'إهناسيا', 'ببا'],
      'الفيوم': ['الفيوم', 'سنورس', 'طامية', 'إطسا', 'يوسف الصديق'],
      'مطروح': ['مرسى مطروح', 'الحمام', 'العلمين', 'النجيلة', 'سيوة'],
      'شمال سيناء': ['العريش', 'رفح', 'بئر العبد', 'نخل', 'الحسنة'],
      'جنوب سيناء': ['الطور', 'سانت كاترين', 'دهب', 'نويبع', 'شرم الشيخ'],
      'البحر الأحمر': ['الغردقة', 'رأس غارب', 'سفاجا', 'القصير', 'مرسى علم'],
      'الوادي الجديد': ['الخارجة', 'الداخلة', 'الفرافرة', 'باريس', 'بلاط'],
      'بورسعيد': ['بورسعيد', 'بورفؤاد', 'العرب', 'المناخ', 'الضواحي'],
      'الإسماعيلية': [
        'الإسماعيلية',
        'فايد',
        'القنطرة شرق',
        'القنطرة غرب',
        'التل الكبير',
      ],
      'السويس': ['السويس', 'الأربعين', 'فايد', 'الجناين', 'عتاقة'],
    };

    final areas = areasMap[governorate] ?? [];
    setState(() {
      _areas = areas;
      _selectedArea = null; // إعادة تعيين المنطقة المختارة
    });
  }

  Future<void> _loadCustomerPhones(int customerId) async {
    try {
      final phonesData = await DatabaseHelper().getCustomerPhones(customerId);
      setState(() {
        _customerPhones = phonesData
            .map((data) => CustomerPhone.fromMap(data))
            .toList();
      });
    } catch (e) {
      print('خطأ في تحميل أرقام هواتف العميل: $e');
    }
  }

  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) return;

    // تحقق إضافي من البيانات الإجبارية
    final name = _nameController.text.trim();
    final governorate = _selectedGovernorate;

    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // التحقق من وجود رقم هاتف واحد على الأقل
    if (_customerPhones.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رقم هاتف واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (governorate == null || governorate.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المحافظة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final dbHelper = DatabaseHelper();

      // --- تحسين التحقق من تكرار أرقام الهواتف ---
      // جمع جميع أرقام الهواتف للتحقق منها مرة واحدة
      final phoneNumbers = _customerPhones.map((phone) => phone.phone).toList();

      // التحقق من تكرار الأرقام في قاعدة البيانات مرة واحدة
      final existingPhones = await dbHelper.getExistingPhones(phoneNumbers);

      for (final phoneEntry in _customerPhones) {
        final existingCustomerWithPhone = existingPhones.firstWhere(
          (phone) => phone['phone'] == phoneEntry.phone,
          orElse: () => {},
        );

        if (existingCustomerWithPhone.isNotEmpty) {
          // إذا كان التعديل، اسمح إذا كان الهاتف ينتمي للعميل الحالي
          if (widget.customer != null &&
              existingCustomerWithPhone['customer_id'] ==
                  widget.customer!['id']) {
            // هذا الهاتف ينتمي للعميل الحالي، لذا فهو مقبول.
            continue;
          }
          // وإلا فهو ينتمي لعميل آخر أو مكرر لعميل جديد
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'رقم الهاتف ${phoneEntry.phone} موجود بالفعل لعميل آخر.',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }
      // --- نهاية التحقق المحسن ---

      final customerData = {
        'name': name,
        'address': _addressController.text.trim(),
        'notes': _notesController.text.trim(),
        'governorate': governorate,
        'area': _selectedArea,
      };

      int customerId;
      if (widget.customer == null) {
        // إضافة عميل جديد
        customerId = await dbHelper.insertCustomer(customerData);

        // إضافة أرقام الهواتف للعميل الجديد في عملية واحدة
        await dbHelper.insertCustomerPhonesBatch(customerId, _customerPhones);
      } else {
        // تحديث عميل موجود
        customerId = widget.customer!['id'];
        await dbHelper.updateCustomer(customerId, customerData);

        // تحديث الهواتف في عملية واحدة
        await dbHelper.updateCustomerPhonesBatch(customerId, _customerPhones);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.customer == null
                  ? 'تم إضافة العميل بنجاح'
                  : 'تم تحديث العميل بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          widget.customer == null ? 'إضافة عميل جديد' : 'تعديل العميل',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // اسم العميل
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'اسم العميل *',
                  prefixIcon: const Icon(
                    Icons.person,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم العميل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // أرقام الهواتف
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.phone,
                                color: Color(0xFF4A90E2),
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'أرقام الهواتف',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF4A90E2),
                                ),
                              ),
                            ],
                          ),
                          ElevatedButton.icon(
                            onPressed: _addPhone,
                            icon: const Icon(Icons.add),
                            label: const Text('إضافة رقم'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF4A90E2),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_customerPhones.isEmpty)
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: const Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.phone_disabled,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'لا توجد أرقام هواتف مضافة',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'اضغط على "إضافة رقم" لإضافة رقم هاتف للعميل',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        )
                      else
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _customerPhones.length,
                          itemBuilder: (context, index) {
                            final phone = _customerPhones[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              decoration: BoxDecoration(
                                color: phone.isPrimary
                                    ? Colors.amber[50]
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: phone.isPrimary
                                      ? Colors.amber[300]!
                                      : Colors.grey[300]!,
                                  width: 1,
                                ),
                              ),
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: phone.isPrimary
                                        ? Colors.amber
                                        : Colors.blue[50],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    phone.isPrimary ? Icons.star : Icons.phone,
                                    color: phone.isPrimary
                                        ? Colors.white
                                        : Colors.blue,
                                    size: 20,
                                  ),
                                ),
                                title: Text(
                                  phone.phone,
                                  style: TextStyle(
                                    fontWeight: phone.isPrimary
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    fontSize: 16,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'النوع: ${phone.phoneType}',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                    if (phone.notes != null &&
                                        phone.notes!.isNotEmpty)
                                      Text(
                                        'ملاحظات: ${phone.notes}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    if (phone.isPrimary)
                                      Container(
                                        margin: const EdgeInsets.only(top: 4),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.amber,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: const Text(
                                          'الرقم الأساسي',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                trailing: IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed: () => _removePhone(index),
                                  tooltip: 'حذف رقم الهاتف',
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // المحافظة
              DropdownButtonFormField<String>(
                value: _selectedGovernorate,
                decoration: InputDecoration(
                  labelText: 'المحافظة *',
                  prefixIcon: const Icon(
                    Icons.location_city,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                items: _governorates.map((governorate) {
                  return DropdownMenuItem(
                    value: governorate,
                    child: Text(governorate),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGovernorate = value;
                    _selectedArea = null;
                    _areas = [];
                  });
                  if (value != null) {
                    _loadAreas(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار المحافظة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // المنطقة
              DropdownButtonFormField<String>(
                value: _selectedArea,
                decoration: InputDecoration(
                  labelText: 'المنطقة',
                  prefixIcon: const Icon(
                    Icons.location_on,
                    color: Color(0xFF4A90E2),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                items: _areas.map((area) {
                  return DropdownMenuItem(value: area, child: Text(area));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // العنوان
              TextFormField(
                controller: _addressController,
                maxLines: 2,
                decoration: InputDecoration(
                  labelText: 'العنوان',
                  prefixIcon: const Icon(Icons.home, color: Color(0xFF4A90E2)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
              const SizedBox(height: 16),

              // الملاحظات
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'ملاحظات',
                  prefixIcon: const Icon(Icons.note, color: Color(0xFF4A90E2)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF4A90E2),
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveCustomer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 3,
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          widget.customer == null
                              ? 'إضافة العميل'
                              : 'حفظ التغييرات',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
