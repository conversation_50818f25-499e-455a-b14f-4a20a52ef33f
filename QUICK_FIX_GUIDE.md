# دليل الحلول السريعة - Atlas Medical Supplies

## 🚨 المشاكل الشائعة والحلول

### 1. خطأ أسماء ملفات الأيقونة
**الخطأ**: `'A' is not a valid file-based resource name character`

**الحل**:
```bash
# إعادة تسمية ملفات الأيقونة إلى أحرف صغيرة
# ATLAS.png → atlas.png
```

### 2. خطأ AndroidManifest.xml
**الخطأ**: `resource mipmap/ic_launcher not found`

**الحل**:
```xml
<!-- في android/app/src/main/AndroidManifest.xml -->
android:icon="@mipmap/atlas"  <!-- بدلاً من @mipmap/ic_launcher -->
```

### 3. مشكلة Overflow في شريط التنقل
**الخطأ**: `OVERFLOWED BY 39 PIXELS`

**الحل**: تم إصلاحه في `dashboard_screen.dart`

## 🛠️ أوامر التشغيل السريعة

### تشغيل التطبيق
```powershell
# الطريقة السريعة
.\run_app.ps1

# الطريقة اليدوية
flutter run
```

### تنظيف وإعادة بناء
```powershell
# تنظيف كامل
flutter clean
flutter pub get
flutter run
```

### حل مشاكل البناء
```powershell
# إذا واجهت مشاكل في البناء
flutter clean
flutter pub get
flutter doctor
flutter run
```

## 📱 بيانات تسجيل الدخول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## 🔧 إعدادات PowerShell
إذا واجهت مشكلة في تشغيل السكريبتات:
```powershell
# تفعيل تشغيل السكريبتات
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## ✅ التحقق من الإصلاح
1. ✅ أسماء ملفات الأيقونة بأحرف صغيرة
2. ✅ AndroidManifest.xml يشير إلى `@mipmap/atlas`
3. ✅ شريط التنقل السفلي بدون overflow
4. ✅ التطبيق يبني ويعمل بدون أخطاء

---
**آخر تحديث**: 30 يوليو 2025 