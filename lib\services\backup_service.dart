import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';

class BackupService {
  static const String _backupFolderName = 'AtlasBackups';
  static const String _backupFileName = 'atlas_backup';
  static const String _backupExtension = '.json';
  static const String _lastBackupKey = 'last_backup_time';
  static const String _backupIntervalKey = 'backup_interval_hours';
  static const String _autoBackupEnabledKey = 'auto_backup_enabled';

  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على مجلد النسخ الاحتياطي
  Future<Directory> get _backupDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/$_backupFolderName');

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    return backupDir;
  }

  /// التحقق من الأذونات المطلوبة
  Future<bool> _checkPermissions() async {
    if (Platform.isAndroid) {
      final storagePermission = await Permission.storage.status;
      if (storagePermission != PermissionStatus.granted) {
        final result = await Permission.storage.request();
        return result == PermissionStatus.granted;
      }
      return true;
    }
    return true;
  }

  /// إنشاء نسخة احتياطية كاملة
  Future<Map<String, dynamic>> createFullBackup() async {
    try {
      // التحقق من الأذونات
      if (!await _checkPermissions()) {
        throw Exception('لا توجد أذونات كافية لإنشاء النسخة الاحتياطية');
      }

      // جمع جميع البيانات من قاعدة البيانات
      final backupData = await _collectAllData();

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${_backupFileName}_$timestamp$_backupExtension';

      // الحصول على مجلد النسخ الاحتياطي
      final backupDir = await _backupDirectory;
      final backupFile = File('${backupDir.path}/$fileName');

      // تحويل البيانات إلى JSON وحفظها
      final jsonData = jsonEncode(backupData);
      await backupFile.writeAsString(jsonData, encoding: utf8);

      // حفظ معلومات النسخة الاحتياطية
      await _saveBackupInfo(fileName, backupData['metadata']);

      // تنظيف النسخ الاحتياطية القديمة
      await _cleanOldBackups();

      return {
        'success': true,
        'file_path': backupFile.path,
        'file_size': await backupFile.length(),
        'backup_count': backupData['metadata']['total_records'],
        'timestamp': timestamp,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// جمع جميع البيانات من قاعدة البيانات
  Future<Map<String, dynamic>> _collectAllData() async {
    final users = await _dbHelper.getUsers();
    final customers = await _dbHelper.getCustomers();
    final products = await _dbHelper.getProducts();
    final invoices = await _dbHelper.getAllInvoices();
    final collections = await _dbHelper.getAllCollections();
    final invoiceItems = await _dbHelper.getAllInvoiceItems();

    // إحصائيات النسخة الاحتياطية
    final totalRecords =
        users.length +
        customers.length +
        products.length +
        invoices.length +
        collections.length +
        invoiceItems.length;

    return {
      'metadata': {
        'backup_version': '2.0',
        'created_at': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
        'total_records': totalRecords,
        'tables': {
          'users': users.length,
          'customers': customers.length,
          'products': products.length,
          'invoices': invoices.length,
          'collections': collections.length,
          'invoice_items': invoiceItems.length,
        },
      },
      'data': {
        'users': users,
        'customers': customers,
        'products': products,
        'invoices': invoices,
        'collections': collections,
        'invoice_items': invoiceItems,
      },
    };
  }

  /// حفظ معلومات النسخة الاحتياطية
  Future<void> _saveBackupInfo(
    String fileName,
    Map<String, dynamic> metadata,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastBackupKey, DateTime.now().toIso8601String());

    // حفظ قائمة النسخ الاحتياطية
    final backupList = prefs.getStringList('backup_files') ?? [];
    backupList.add(fileName);

    // الاحتفاظ بآخر 10 نسخ احتياطية فقط
    if (backupList.length > 10) {
      backupList.removeAt(0);
    }

    await prefs.setStringList('backup_files', backupList);
  }

  /// تنظيف النسخ الاحتياطية القديمة
  Future<void> _cleanOldBackups() async {
    try {
      final backupDir = await _backupDirectory;
      final files = backupDir.listSync();

      // الاحتفاظ بآخر 5 نسخ احتياطية فقط
      if (files.length > 5) {
        // ترتيب الملفات حسب تاريخ الإنشاء
        final sortedFiles =
            files
                .whereType<File>()
                .where((file) => file.path.endsWith(_backupExtension))
                .toList()
              ..sort(
                (a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()),
              );

        // حذف الملفات القديمة
        for (int i = 0; i < sortedFiles.length - 5; i++) {
          await sortedFiles[i].delete();
        }
      }
    } catch (e) {
      print('خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }

  /// استعادة النسخة الاحتياطية
  Future<Map<String, dynamic>> restoreBackup(String filePath) async {
    try {
      final backupFile = File(filePath);
      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // قراءة البيانات من الملف
      final jsonData = await backupFile.readAsString(encoding: utf8);
      final backupData = jsonDecode(jsonData) as Map<String, dynamic>;

      // التحقق من صحة البيانات
      if (!_validateBackupData(backupData)) {
        throw Exception('بيانات النسخة الاحتياطية غير صحيحة');
      }

      // استعادة البيانات
      await _restoreData(backupData['data']);

      return {
        'success': true,
        'restored_records': backupData['metadata']['total_records'],
        'backup_date': backupData['metadata']['created_at'],
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// التحقق من صحة بيانات النسخة الاحتياطية
  bool _validateBackupData(Map<String, dynamic> backupData) {
    return backupData.containsKey('metadata') &&
        backupData.containsKey('data') &&
        backupData['data'] is Map<String, dynamic>;
  }

  /// استعادة البيانات إلى قاعدة البيانات
  Future<void> _restoreData(Map<String, dynamic> data) async {
    // حذف البيانات الموجودة
    await _dbHelper.clearAllData();

    // استعادة البيانات الجديدة
    if (data['users'] != null) {
      for (final user in data['users']) {
        await _dbHelper.insertUser(user);
      }
    }

    if (data['customers'] != null) {
      for (final customer in data['customers']) {
        await _dbHelper.insertCustomer(customer);
      }
    }

    if (data['products'] != null) {
      for (final product in data['products']) {
        await _dbHelper.insertProduct(product);
      }
    }

    if (data['invoices'] != null) {
      for (final invoice in data['invoices']) {
        await _dbHelper.insertInvoice(invoice);
      }
    }

    if (data['collections'] != null) {
      for (final collection in data['collections']) {
        await _dbHelper.insertCollection(collection);
      }
    }

    if (data['invoice_items'] != null) {
      for (final item in data['invoice_items']) {
        await _dbHelper.insertInvoiceItem(item);
      }
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  Future<List<Map<String, dynamic>>> getBackupFiles() async {
    try {
      final backupDir = await _backupDirectory;
      final files = backupDir.listSync();

      final backupFiles = <Map<String, dynamic>>[];

      for (final file in files) {
        if (file is File && file.path.endsWith(_backupExtension)) {
          final stat = await file.stat();
          final fileName = file.path.split('/').last;

          // محاولة قراءة metadata من الملف
          Map<String, dynamic>? metadata;
          try {
            final content = await file.readAsString(encoding: utf8);
            final data = jsonDecode(content) as Map<String, dynamic>;
            metadata = data['metadata'] as Map<String, dynamic>?;
          } catch (e) {
            // تجاهل الأخطاء في قراءة metadata
          }

          backupFiles.add({
            'file_name': fileName,
            'file_path': file.path,
            'file_size': stat.size,
            'created_at':
                metadata?['created_at'] ?? stat.modified.toIso8601String(),
            'total_records': metadata?['total_records'] ?? 0,
            'backup_version': metadata?['backup_version'] ?? '1.0',
          });
        }
      }

      // ترتيب الملفات حسب التاريخ (الأحدث أولاً)
      backupFiles.sort(
        (a, b) => DateTime.parse(
          b['created_at'],
        ).compareTo(DateTime.parse(a['created_at'])),
      );

      return backupFiles;
    } catch (e) {
      return [];
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();

        // إزالة من قائمة النسخ الاحتياطية
        final prefs = await SharedPreferences.getInstance();
        final backupList = prefs.getStringList('backup_files') ?? [];
        final fileName = filePath.split('/').last;
        backupList.remove(fileName);
        await prefs.setStringList('backup_files', backupList);

        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على معلومات النسخ الاحتياطي التلقائي
  Future<Map<String, dynamic>> getAutoBackupInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final lastBackup = prefs.getString(_lastBackupKey);
    final interval = prefs.getInt(_backupIntervalKey) ?? 24; // افتراضي 24 ساعة
    final enabled = prefs.getBool(_autoBackupEnabledKey) ?? false;

    return {
      'enabled': enabled,
      'interval_hours': interval,
      'last_backup': lastBackup,
      'next_backup': lastBackup != null
          ? DateTime.parse(lastBackup).add(Duration(hours: interval))
          : null,
    };
  }

  /// تفعيل/إلغاء النسخ الاحتياطي التلقائي
  Future<void> setAutoBackup(bool enabled, int intervalHours) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoBackupEnabledKey, enabled);
    await prefs.setInt(_backupIntervalKey, intervalHours);
  }

  /// التحقق من الحاجة للنسخ الاحتياطي التلقائي
  Future<bool> shouldAutoBackup() async {
    final info = await getAutoBackupInfo();

    if (!info['enabled']) return false;

    final lastBackup = info['last_backup'];
    if (lastBackup == null) return true;

    final lastBackupTime = DateTime.parse(lastBackup);
    final nextBackupTime = lastBackupTime.add(
      Duration(hours: info['interval_hours']),
    );

    return DateTime.now().isAfter(nextBackupTime);
  }

  /// الحصول على حجم مجلد النسخ الاحتياطية
  Future<int> getBackupFolderSize() async {
    try {
      final backupDir = await _backupDirectory;
      final files = backupDir.listSync();

      int totalSize = 0;
      for (final file in files) {
        if (file is File && file.path.endsWith(_backupExtension)) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// تصدير نسخة احتياطية إلى مجلد خارجي
  Future<Map<String, dynamic>> exportBackup(
    String sourceFilePath,
    String destinationPath,
  ) async {
    try {
      final sourceFile = File(sourceFilePath);
      if (!await sourceFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final destinationFile = File(destinationPath);
      await sourceFile.copy(destinationFile.path);

      return {
        'success': true,
        'exported_path': destinationPath,
        'file_size': await destinationFile.length(),
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// استيراد نسخة احتياطية من مجلد خارجي
  Future<Map<String, dynamic>> importBackup(String sourceFilePath) async {
    try {
      final sourceFile = File(sourceFilePath);
      if (!await sourceFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // التحقق من صحة الملف
      final content = await sourceFile.readAsString(encoding: utf8);
      final backupData = jsonDecode(content) as Map<String, dynamic>;

      if (!_validateBackupData(backupData)) {
        throw Exception('بيانات النسخة الاحتياطية غير صحيحة');
      }

      // نسخ الملف إلى مجلد النسخ الاحتياطية
      final backupDir = await _backupDirectory;
      final fileName = sourceFilePath.split('/').last;
      final destinationFile = File('${backupDir.path}/$fileName');

      await sourceFile.copy(destinationFile.path);

      // حفظ معلومات النسخة الاحتياطية
      await _saveBackupInfo(fileName, backupData['metadata']);

      return {
        'success': true,
        'imported_path': destinationFile.path,
        'file_size': await destinationFile.length(),
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
