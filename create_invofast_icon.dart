import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() async {
  await createInvoFastIcon();
}

Future<void> createInvoFastIcon() async {
  // Create a 1024x1024 image
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  final size = const Size(1024, 1024);

  // Background - Blue gradient
  final paint = Paint()
    ..shader = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color(0xFF4A90E2), // Blue
        const Color(0xFF357ABD), // Darker blue
      ],
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

  // Add a white circle in the center
  final circlePaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;

  canvas.drawCircle(
    Offset(size.width / 2, size.height / 2),
    size.width * 0.35,
    circlePaint,
  );

  // Add "IF" text in the center
  final textPainter = TextPainter(
    text: TextSpan(
      text: 'IF',
      style: TextStyle(
        color: const Color(0xFF4A90E2),
        fontSize: size.width * 0.25,
        fontWeight: FontWeight.bold,
        fontFamily: 'Arial',
      ),
    ),
    textDirection: TextDirection.ltr,
  );

  textPainter.layout();
  textPainter.paint(
    canvas,
    Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    ),
  );

  // Convert to image
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  final bytes = byteData!.buffer.asUint8List();

  // Save the image
  final file = File('assets/images/invofast_icon.png');
  await file.writeAsBytes(bytes);

  print('✅ تم إنشاء أيقونة InvoFast بنجاح!');
  print('📁 الموقع: ${file.absolute.path}');
}
