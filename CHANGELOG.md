# سجل التحديثات - Atlas Medical Supplies

## الإصدار 1.0.9 (Build 11)

### 📱 تحديث شامل لإلغاء تفعيل كود الدولة
- **تحديث `invoice_sharing_service.dart`**: تم تحديث دالة `formatPhoneNumber` لإلغاء تفعيل كود الدولة
- **تحديث الاختبارات**: تم تحديث جميع الاختبارات لتتوافق مع التغيير الجديد
- **توحيد السلوك**: جميع خدمات معالجة الأرقام تعمل بنفس الطريقة الآن
- **شفافية في المعالجة**: إضافة رسائل تصحيح واضحة لعملية تنظيف الأرقام
- **الحفاظ على الأرقام**: الأرقام تبقى كما هي مسجلة بدون أي تعديل تلقائي

### 🔧 تحسينات تقنية
- **توافق الخدمات**: `messaging_service.dart` و `invoice_sharing_service.dart` يعملان بنفس الطريقة
- **اختبارات محدثة**: جميع الاختبارات تعكس السلوك الجديد
- **توثيق شامل**: إضافة ملف `PHONE_NUMBER_FIX.md` لتوثيق التحديثات

## الإصدار 1.0.9 (Build 10)

### 📱 إلغاء تفعيل كود الدولة في أرقام الهواتف
- **إلغاء تفعيل كود الدولة**: تم إلغاء جميع التعديلات التلقائية على أرقام الهواتف
- **الاحتفاظ بالرقم كما هو**: الرقم يبقى كما هو مسجل في قاعدة البيانات بدون أي تعديل
- **إزالة الرموز فقط**: يتم إزالة الرموز والمسافات فقط من الرقم
- **إضافة معلومات الرقم**: دالة `getFormattedPhoneInfo` لعرض معلومات الرقم الأصلي والمنسق
- **إضافة زر معلومات الرقم**: زر في شاشة تفاصيل الفاتورة لعرض معلومات الرقم المنسق

### 🔄 إلغاء النسخ الاحتياطي التلقائي
- **إلغاء النسخ التلقائي**: تم إلغاء النسخ الاحتياطي التلقائي كل ثانية
- **نسخ يدوي**: النسخ الاحتياطي أصبح يدوياً عند الطلب
- **تحسين الأداء**: تقليل استهلاك الموارد وتحسين الأداء
- **إزالة التذكيرات**: لا توجد تذكيرات تلقائية للنسخ الاحتياطي

### تحسينات في واجهة المستخدم
- إضافة زر "معلومات الرقم" في شاشة تفاصيل الفاتورة
- إضافة نافذة منبثقة لعرض معلومات الرقم المنسق
- تحسين رسائل التصحيح في وحدة التحكم
- إضافة ملاحظة أن كود الدولة تم إلغاء تفعيله

### تحسينات في نظام الرسائل
- إضافة رسائل تصحيح مفصلة لعملية تنظيف الأرقام
- إضافة رقم افتراضي للاختبار عند عدم وجود رقم هاتف
- تحسين التعامل مع الأرقام الفارغة
- إلغاء جميع التعديلات التلقائية على الأرقام

### الإصلاحات
- إصلاح مشكلة أخذ الرقم الخطأ عند إرسال الرسائل النصية
- إصلاح مشكلة إضافة رمز البلد المصري (2) تلقائياً
- إصلاح مشكلة حذف الصفر من بداية الأرقام المحلية
- إصلاح مشكلة تعديل الأرقام الدولية

## الإصدار 1.0.8 (Build 9)

### 📱 تحسين نظام الرسائل النصية
- **إضافة دعم إرسال الرسائل عبر تطبيق الهاتف**: تم تحديث نظام الرسائل النصية ليعمل عبر تطبيق الهاتف الافتراضي
- **إزالة الحاجة لأذونات SMS**: لا يحتاج التطبيق لأذونات خاصة لإرسال الرسائل
- **إضافة زر اختبار الرسائل**: زر مخصص لاختبار إرسال الرسائل النصية
- **تحسين تنسيق الرسائل**: إزالة الرموز emoji لتحسين التوافق مع جميع الأجهزة
- **إضافة دالة إرسال رسائل مخصصة**: `sendCustomSMS` لإرسال رسائل مخصصة

### تحسينات في واجهة المستخدم
- إضافة زر "اختبار" في شاشة تفاصيل الفاتورة
- تحسين رسائل النجاح والفشل
- تحسين تنسيق الرسائل النصية

### تحسينات في قاعدة البيانات
- إضافة عمود `notes` لجدول الفواتير
- تحديث إصدار قاعدة البيانات إلى 8
- إضافة منطق الترحيل التلقائي

## الإصدار 1.0.7 (Build 8)

### 🚨 إصلاحات حرجة
- **تحسين شامل لتصحيح قاعدة البيانات**: إضافة تفاصيل أكثر في رسائل التصحيح
- **إضافة أدوات إصلاح قاعدة البيانات**: دالة إعادة إنشاء جدول invoice_items
- **إضافة زر "إصلاح قاعدة البيانات"**: في شاشة الإعدادات للمستخدمين
- **تحسين رسائل الخطأ**: تفاصيل أكثر عن البيانات والخطأ
- **إضافة مراقبة هيكل الجدول**: طباعة هيكل الجدول للتحقق من المشاكل

### تحسينات في قاعدة البيانات
- إضافة طباعة تفصيلية لأنواع البيانات المرسلة
- إضافة التحقق من وجود الجدول في قاعدة البيانات
- إضافة إعادة إنشاء تلقائي للجدول في حالة الخطأ
- تحسين معالجة الأخطاء مع تفاصيل أكثر

## الإصدار 1.0.6 (Build 7)

### 🚨 إصلاحات حرجة
- **إصلاح مشكلة إضافة المنتجات للفواتير**: تم حل المشكلة التي تمنع إضافة منتجات للفواتير
- **إزالة عمود غير موجود**: تم إزالة `product_id` من بيانات عناصر الفاتورة
- **إضافة عمود notes**: تم إضافة عمود `notes` الاختياري لعناصر الفاتورة
- **تحسين معالجة الأخطاء**: إضافة تحقق مفصل من البيانات قبل الإدراج

### تحسينات في قاعدة البيانات
- تحسين رسائل التصحيح في عمليات إدراج عناصر الفواتير
- إضافة طباعة هيكل الجدول للتحقق من المشاكل
- تحسين التحقق من صحة البيانات

## الإصدار 1.0.5 (Build 6)

### 🚨 إصلاحات حرجة
- **إصلاح مشكلة قاعدة البيانات**: تم حل المشكلة التي تمنع حفظ المنتجات والفواتير
- **تحديث مخطط قاعدة البيانات**: جعل عمود `unit_price` اختياري في جدول المنتجات
- **إضافة معالجة أخطاء مفصلة**: رسائل تصحيح واضحة لعمليات قاعدة البيانات
- **ترحيل قاعدة البيانات**: تحديث الإصدار إلى 7 مع منطق الترحيل التلقائي

### تحسينات في قاعدة البيانات
- تحسين رسائل الخطأ في عمليات قاعدة البيانات
- إضافة تسجيل مفصل للعمليات للمساعدة في التصحيح
- تحسين التوافق مع البيانات الموجودة

## الإصدار 1.0.4 (Build 5)

### 🐛 إصلاح خطأ إضافة المنتجات في الفواتير
- **إصلاح مشكلة السعر**: تم إصلاح مشكلة إضافة المنتجات التي لا تحتوي على سعر في الفواتير
- **حقل سعر يدوي**: إضافة حقل سعر يدوي للمنتجات التي لا تحتوي على سعر محدد
- **تحسين التحقق**: تحسين التحقق من السعر والكمية عند إضافة المنتجات
- **تحسين العرض**: تحسين عرض المنتجات في القائمة المنسدلة لتوضيح حالة السعر

### تحسينات في واجهة المستخدم
- إضافة حقل سعر اختياري في نموذج إضافة المنتجات للفواتير
- تحسين رسائل التحقق من الحقول
- تحسين عرض المنتجات في القائمة المنسدلة

## الإصدار 1.0.3 (Build 4)

### تبسيط نموذج إضافة المنتجات
- **حذف حقل الفئة**: تم إزالة حقل الفئة من نموذج إضافة/تعديل المنتجات
- **حذف حقل السعر**: تم إزالة حقل السعر من نموذج إضافة/تعديل المنتجات
- **تبسيط التحقق**: تم تبسيط التحقق من الحقول المطلوبة (اسم المنتج فقط)
- **تحسين العرض**: تم تبسيط عرض المنتجات في القائمة (الاسم والوصف فقط)
- **تحسين البحث**: تم تحديث البحث ليشمل الاسم والوصف فقط

### تحسينات في واجهة المستخدم
- تبسيط نموذج إضافة المنتجات
- تحسين عرض قائمة المنتجات
- تحسين رسائل التحقق من الحقول

## الإصدار 1.0.2 (Build 3)

### تحسينات في صفحة تفاصيل العميل
- **إخفاء تبويب التحصيل**: إذا كان العميل ليس له فواتير، لا يتم عرض تبويب التحصيل
- **صلاحيات المدير**: إضافة إمكانية تعديل وحذف التحصيلات للمدير فقط
- **قائمة منبثقة للتحصيلات**: إضافة قائمة منبثقة مع خيارات التعديل والحذف للمدير
- **تحسين واجهة المستخدم**: تحسين عرض التحصيلات مع إضافة أيقونات التعديل والحذف

### تحسينات في قاعدة البيانات
- **إضافة دوال التحصيل**: إضافة `getCollection`، `updateCollection`، و `deleteCollection` إلى DatabaseHelper
- **تحسين إدارة البيانات**: تحسين التعامل مع بيانات التحصيل

## الإصدار 1.0.1 (Build 2) - 2024-12-19

### 🐛 إصلاحات الأخطاء
- **إصلاح خطأ إضافة المنتجات في الفواتير**: تم إصلاح مشكلة تحويل النوع عند إضافة منتجات إلى الفواتير
  - تحويل `quantity` من `int` إلى `double` لتوافق قاعدة البيانات
  - إصلاح حساب `total_price` في عناصر الفاتورة
  - تحسين التعامل مع أنواع البيانات في `_addProduct` و `_saveInvoice`
- إصلاح مشاكل في تحويل الأنواع بين `int` و `double`
- تحسين التعامل مع البيانات في قاعدة البيانات

### 🔧 التحسينات
- تحسين أداء إضافة المنتجات للفواتير
- تحسين دقة الحسابات في الفواتير

---

## الإصدار 1.0.0 (Build 1) - 2024-12-19

### ✨ الميزات الجديدة
- **نظام إدارة المنتجات**: إضافة شاشة كاملة لإدارة المنتجات مع CRUD operations
- **قائمة منسدلة للمنتجات**: في شاشة إضافة/تعديل الفواتير بدلاً من الكتابة اليدوية
- **نظام تتبع الإصدارات**: تتبع تلقائي للتحديثات مع سجل كامل
- **شاشة معلومات الإصدار**: عرض تفاصيل الإصدار وسجل التحديثات
- **تحسين شاشة تفاصيل العميل**: إضافة تبويبات للفواتير والتحصيلات
- **إمكانية إضافة تحصيل من تفاصيل العميل**: مع تحديث مبالغ الفواتير تلقائياً

### 🔧 التحسينات
- **تحسين واجهة المستخدم**: تصميم أكثر وضوحاً وسهولة في الاستخدام
- **تحسين الأداء**: تحسين استعلامات قاعدة البيانات
- **تحسين التنقل**: إضافة قائمة المنتجات في الشريط السفلي والقائمة الجانبية
- **تحسين التحقق من البيانات**: validation محسن لحقول الإدخال

### 🐛 إصلاحات الأخطاء
- إصلاح خطأ في اختيار العميل في شاشة الفواتير
- إصلاح خطأ في إضافة عناصر الفاتورة
- إصلاح مشاكل التنقل بين الشاشات
- إصلاح مشاكل عرض البيانات في القوائم

### 📊 قاعدة البيانات
- إضافة جدول `products` لإدارة المنتجات
- إضافة جدول `invoice_items` لتفاصيل عناصر الفواتير
- تحديث إحصائيات لوحة التحكم لتشمل المنتجات
- تحسين هيكل قاعدة البيانات

### 🔄 النسخ الاحتياطي
- تحديث نظام النسخ الاحتياطي ليشمل الجداول الجديدة
- تحسين عملية الاستعادة من النسخ الاحتياطية
- إضافة تتبع أفضل لحالة النسخ الاحتياطي

### 🎨 التصميم
- تحسين تصميم القوائم المنسدلة
- إضافة أيقونات وألوان متناسقة
- تحسين عرض الرسائل والتنبيهات
- تحسين تصميم البطاقات والعناصر

---

## الإصدارات السابقة

### الإصدار 0.9.0 (الإصدار التجريبي)
- نظام إدارة العملاء الأساسي
- نظام إدارة الفواتير الأساسي
- نظام النسخ الاحتياطي المحلي
- واجهة مستخدم أساسية

---

## ملاحظات التطوير

### التقنيات المستخدمة
- **Flutter**: إطار العمل الرئيسي
- **SQLite**: قاعدة البيانات المحلية
- **Google Drive API**: النسخ الاحتياطي السحابي
- **SharedPreferences**: تخزين الإعدادات

### المتطلبات
- Android 5.0+ (API level 21)
- iOS 11.0+
- اتصال بالإنترنت للنسخ الاحتياطي السحابي

### التحديثات القادمة
- نظام إدارة المخزون
- تقارير متقدمة
- نظام التنبيهات
- دعم متعدد اللغات
- نظام الصلاحيات المتقدم 