# سكريبت تنظيف وإعادة بناء مشروع Atlas Medical Supplies
Write-Host "🧹 بدء تنظيف وإعادة بناء المشروع..." -ForegroundColor Green

# تنظيف المشروع
Write-Host "🗑️ تنظيف المشروع..." -ForegroundColor Yellow
flutter clean

# حذف مجلد build
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "✅ تم حذف مجلد build" -ForegroundColor Green
}

# حذف مجلد .dart_tool
if (Test-Path ".dart_tool") {
    Remove-Item -Recurse -Force ".dart_tool"
    Write-Host "✅ تم حذف مجلد .dart_tool" -ForegroundColor Green
}

# تحميل التبعيات
Write-Host "📦 تحميل التبعيات..." -ForegroundColor Yellow
flutter pub get

# التحقق من صحة المشروع
Write-Host "🔍 التحقق من صحة المشروع..." -ForegroundColor Yellow
flutter doctor

# تحليل الكود
Write-Host "📊 تحليل الكود..." -ForegroundColor Yellow
flutter analyze

Write-Host "✅ تم تنظيف وإعادة بناء المشروع بنجاح!" -ForegroundColor Green
Write-Host "🚀 يمكنك الآن تشغيل التطبيق باستخدام: flutter run" -ForegroundColor Cyan 