import 'package:flutter/material.dart';
import '../database/database_helper.dart';

import '../services/auth_service.dart';
import '../services/messaging_service.dart';
import 'customer_details_screen.dart';
import 'customers_by_governorate_screen.dart';
import '../widgets/atlas_logo.dart';
import '../widgets/permission_wrapper.dart';
import '../widgets/customer_phones_widget.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<String> _governorates = [];
  Map<String, int> _customerCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGovernorates();
  }

  Future<void> _loadGovernorates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final governorates = await _dbHelper.getGovernorates();
      final counts = await _dbHelper.getCustomerCountByGovernorate();

      setState(() {
        _governorates = governorates;
        _customerCounts = counts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المحافظات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCustomersForGovernorate(String governorate) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CustomersByGovernorateScreen(governorate: governorate),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('العملاء حسب المحافظات'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGovernorates,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(
            context,
            '/add-customer',
          ).then((_) => _loadGovernorates());
        },
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
            )
          : _governorates.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.location_city, size: 80, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد محافظات',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'قم بإضافة عملاء مع تحديد المحافظة',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadGovernorates,
              color: const Color(0xFF4A90E2),
              child: ListView.builder(
                padding: const EdgeInsets.only(
                  left: 16,
                  right: 16,
                  top: 16,
                  bottom:
                      80, // إضافة مساحة في الأسفل لتجنب تداخل FloatingActionButton
                ),
                itemCount: _governorates.length,
                itemBuilder: (context, index) {
                  final governorate = _governorates[index];
                  final customerCount = _customerCounts[governorate] ?? 0;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: CircleAvatar(
                        backgroundColor: const Color(0xFF4A90E2),
                        child: Text(
                          governorate.substring(0, 1),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        governorate,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Text(
                        '$customerCount عميل',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios,
                        color: Color(0xFF4A90E2),
                      ),
                      onTap: () => _showCustomersForGovernorate(governorate),
                    ),
                  );
                },
              ),
            ),
      // تم إزالة FloatingActionButton حسب طلب المستخدم
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     Navigator.pushNamed(
      //       context,
      //       '/add-customer',
      //     ).then((_) => _loadGovernorates());
      //   },
      //   backgroundColor: const Color(0xFF4A90E2),
      //   foregroundColor: Colors.white,
      //   child: const Icon(Icons.add),
      // ),
    );
  }
}
