import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'services/auth_service.dart';
import 'utils/version_manager.dart';
import 'screens/splash_screen.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/add_edit_customer_screen.dart';
import 'screens/add_edit_invoice_screen.dart';
import 'screens/invoice_details_screen.dart';
import 'screens/customer_details_screen.dart';
import 'screens/products_screen.dart';
import 'screens/version_info_screen.dart';

import 'screens/customer_invoices_screen.dart';
import 'screens/customers_by_governorate_screen.dart';
import 'screens/storage_management_screen.dart';
import 'screens/all_invoices_screen.dart';
import 'services/storage_service.dart';
import 'widgets/atlas_logo.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تشغيل التطبيق فوراً مع شاشة التحميل
  runApp(const AtlasMedicalApp());

  // تشغيل العمليات في الخلفية
  _initializeBackgroundServices();
}

// تشغيل الخدمات في الخلفية
Future<void> _initializeBackgroundServices() async {
  // إنشاء مستخدم افتراضي إذا لم يكن موجود
  try {
    await AuthService.createDefaultUser();
    print('✅ تم التحقق من المستخدم الافتراضي بنجاح');
  } catch (e) {
    print('❌ خطأ في التحقق من المستخدم الافتراضي: $e');
  }

  // تهيئة نظام الإصدارات
  try {
    await VersionManager.saveCurrentVersion();
    print('✅ تم تهيئة نظام الإصدارات بنجاح');
  } catch (e) {
    print('❌ خطأ في تهيئة نظام الإصدارات: $e');
  }

  // طلب أذونات التخزين عند بدء التطبيق
  try {
    final hasPermissions = await StorageService.hasStoragePermissions();
    if (!hasPermissions) {
      print('📱 طلب أذونات التخزين عند بدء التطبيق...');
      await StorageService.requestStoragePermissions();
    } else {
      print('✅ أذونات التخزين متوفرة بالفعل');
    }
  } catch (e) {
    print('❌ خطأ في طلب أذونات التخزين: $e');
  }
}

class AtlasMedicalApp extends StatelessWidget {
  const AtlasMedicalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'InvoFast',
      debugShowCheckedModeBanner: false,

      // دعم اللغة العربية و RTL
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'EG'), // العربية - مصر
      ],
      locale: const Locale('ar', 'EG'),

      // التصميم والألوان الزرقاء الهادئة
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF4A90E2),
        scaffoldBackgroundColor: Colors.white,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF4A90E2),
          primary: const Color(0xFF4A90E2),
          secondary: const Color(0xFF7BB3F0),
          background: Colors.white,
        ),
        useMaterial3: true,

        // خط عربي واضح
        fontFamily: 'Arial',
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontFamily: 'Arial'),
          displayMedium: TextStyle(fontFamily: 'Arial'),
          displaySmall: TextStyle(fontFamily: 'Arial'),
          headlineLarge: TextStyle(fontFamily: 'Arial'),
          headlineMedium: TextStyle(fontFamily: 'Arial'),
          headlineSmall: TextStyle(fontFamily: 'Arial'),
          titleLarge: TextStyle(fontFamily: 'Arial'),
          titleMedium: TextStyle(fontFamily: 'Arial'),
          titleSmall: TextStyle(fontFamily: 'Arial'),
          bodyLarge: TextStyle(fontFamily: 'Arial'),
          bodyMedium: TextStyle(fontFamily: 'Arial'),
          bodySmall: TextStyle(fontFamily: 'Arial'),
          labelLarge: TextStyle(fontFamily: 'Arial'),
          labelMedium: TextStyle(fontFamily: 'Arial'),
          labelSmall: TextStyle(fontFamily: 'Arial'),
        ),

        // تصميم AppBar
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF4A90E2),
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),

        // تصميم الأزرار
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4A90E2),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 3,
          ),
        ),

        // تصميم البطاقات
        cardTheme: CardThemeData(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          color: Colors.white,
        ),

        // تصميم حقول الإدخال مع DropdownButtonFormField
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF4A90E2)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF4A90E2), width: 2),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF4A90E2)),
          ),
          filled: true,
          fillColor: Colors.white,
          labelStyle: const TextStyle(color: Color(0xFF4A90E2)),
        ),
      ),

      home: const SplashScreen(),
      routes: {
        '/add-customer': (context) => const AddEditCustomerScreen(),
        '/edit-customer': (context) {
          final customer =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return AddEditCustomerScreen(customer: customer);
        },
        '/add-invoice': (context) => const AddEditInvoiceScreen(),
        '/edit-invoice': (context) {
          final invoice =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return AddEditInvoiceScreen(invoice: invoice);
        },
        '/invoice-details': (context) {
          final invoice =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return InvoiceDetailsScreen(invoice: invoice);
        },
        '/customer-details': (context) {
          final customer =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return CustomerDetailsScreen(customer: customer);
        },
        '/products': (context) => const ProductsScreen(),
        '/version-info': (context) => const VersionInfoScreen(),

        '/customer-invoices': (context) {
          final customer =
              ModalRoute.of(context)!.settings.arguments
                  as Map<String, dynamic>;
          return CustomerInvoicesScreen(customer: customer);
        },
        '/customers-by-governorate': (context) {
          final governorate =
              ModalRoute.of(context)!.settings.arguments as String;
          return CustomersByGovernorateScreen(governorate: governorate);
        },
        '/all-invoices': (context) => const AllInvoicesScreen(),
        '/storage-management': (context) => const StorageManagementScreen(),
      },
    );
  }
}
