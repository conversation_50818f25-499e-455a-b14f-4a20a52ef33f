import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:invofast/main.dart';
import 'package:invofast/screens/login_screen.dart';
import 'package:invofast/screens/dashboard_screen.dart';
import 'package:invofast/screens/customers_screen.dart';
import 'package:invofast/screens/products_screen.dart';
import 'package:invofast/screens/invoices_screen.dart';
import 'package:invofast/screens/collections_screen.dart';
import 'package:invofast/database/database_helper.dart';
import 'package:invofast/services/auth_service.dart';
import 'package:invofast/services/backup_service.dart';
import 'package:invofast/services/data_export_service.dart';
import 'package:invofast/services/notification_service.dart';

void main() {
  group('InvoFast - اختبار شامل', () {
    late DatabaseHelper dbHelper;

    setUpAll(() async {
      // تهيئة قاعدة البيانات للاختبار
      dbHelper = DatabaseHelper();
      await dbHelper.initializeDatabase();
      
      // تهيئة الإشعارات
      await NotificationService.initialize();
    });

    tearDownAll(() async {
      // تنظيف قاعدة البيانات بعد الاختبار
      await dbHelper.clearAllData();
    });

    group('اختبارات قاعدة البيانات', () {
      test('إنشاء مستخدم جديد', () async {
        final userId = await dbHelper.insertUser({
          'name': 'مستخدم اختبار',
          'phone': '0123456789',
          'password': '123456',
          'role': 'admin',
          'is_active': 1,
        });

        expect(userId, isNotNull);
        expect(userId, isA<int>());
      });

      test('إنشاء عميل جديد', () async {
        final customerId = await dbHelper.insertCustomer({
          'name': 'عميل اختبار',
          'primary_phone': '0987654321',
          'address': 'عنوان اختبار',
          'governorate': 'القاهرة',
          'area': 'وسط البلد',
        });

        expect(customerId, isNotNull);
        expect(customerId, isA<int>());
      });

      test('إنشاء منتج جديد', () async {
        final productId = await dbHelper.insertProduct({
          'name': 'منتج اختبار',
          'price': 100.0,
          'description': 'وصف المنتج',
        });

        expect(productId, isNotNull);
        expect(productId, isA<int>());
      });

      test('إنشاء فاتورة جديدة', () async {
        // إنشاء عميل أولاً
        final customerId = await dbHelper.insertCustomer({
          'name': 'عميل فاتورة',
          'primary_phone': '0111111111',
          'address': 'عنوان العميل',
          'governorate': 'الجيزة',
          'area': 'الدقي',
        });

        // إنشاء منتج أولاً
        final productId = await dbHelper.insertProduct({
          'name': 'منتج فاتورة',
          'price': 50.0,
          'description': 'وصف المنتج',
        });

        final invoiceId = await dbHelper.insertInvoice({
          'customer_id': customerId,
          'invoice_number': 'INV-001',
          'date': DateTime.now().toString().substring(0, 10),
          'total_amount': 100.0,
          'paid_amount': 50.0,
          'remaining_amount': 50.0,
          'notes': 'ملاحظات الفاتورة',
          'created_by': 1,
        });

        expect(invoiceId, isNotNull);
        expect(invoiceId, isA<int>());
      });

      test('إنشاء تحصيل جديد', () async {
        // إنشاء عميل أولاً
        final customerId = await dbHelper.insertCustomer({
          'name': 'عميل تحصيل',
          'primary_phone': '0222222222',
          'address': 'عنوان العميل',
          'governorate': 'الإسكندرية',
          'area': 'سموحة',
        });

        final collectionId = await dbHelper.insertCollection({
          'customer_id': customerId,
          'amount': 75.0,
          'date': DateTime.now().toString().substring(0, 10),
          'payment_method': 'نقداً',
          'collector_id': 1,
          'notes': 'ملاحظات التحصيل',
        });

        expect(collectionId, isNotNull);
        expect(collectionId, isA<int>());
      });

      test('جلب إحصائيات لوحة التحكم', () async {
        final stats = await dbHelper.getDashboardStats();
        
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('totalCustomers'), isTrue);
        expect(stats.containsKey('totalProducts'), isTrue);
        expect(stats.containsKey('totalInvoices'), isTrue);
        expect(stats.containsKey('totalCollections'), isTrue);
      });
    });

    group('اختبارات خدمة المصادقة', () {
      test('تسجيل الدخول', () async {
        // إنشاء مستخدم أولاً
        await dbHelper.insertUser({
          'name': 'مستخدم تسجيل دخول',
          'phone': '0333333333',
          'password': '123456',
          'role': 'user',
          'is_active': 1,
        });

        final isLoggedIn = await AuthService.login('0333333333', '123456');
        expect(isLoggedIn, isTrue);
      });

      test('تسجيل الخروج', () async {
        await AuthService.logout();
        final currentUser = await AuthService.getCurrentUser();
        expect(currentUser, isNull);
      });
    });

    group('اختبارات خدمة النسخ الاحتياطي', () {
      test('إنشاء نسخة احتياطية', () async {
        final backupPath = await BackupService().createFullBackup();
        expect(backupPath, isNotNull);
        expect(backupPath, isA<String>());
        expect(backupPath.isNotEmpty, isTrue);
      });

      test('جلب ملفات النسخ الاحتياطي', () async {
        final backupFiles = await BackupService().getBackupFiles();
        expect(backupFiles, isA<List<String>>());
      });
    });

    group('اختبارات خدمة تصدير البيانات', () {
      test('تصدير البيانات إلى Excel', () async {
        final exportPath = await DataExportService.exportAllDataToExcel();
        expect(exportPath, isNotNull);
        expect(exportPath, isA<String>());
        expect(exportPath.isNotEmpty, isTrue);
      });
    });

    group('اختبارات واجهة المستخدم', () {
      testWidgets('اختبار شاشة تسجيل الدخول', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('تسجيل الدخول'), findsOneWidget);
        expect(find.byType(TextFormField), findsAtLeast(2));
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('اختبار لوحة التحكم', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: DashboardScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('ATLAS'), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(BottomNavigationBar), findsOneWidget);
      });

      testWidgets('اختبار شاشة العملاء', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: CustomersScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('العملاء'), findsOneWidget);
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });

      testWidgets('اختبار شاشة المنتجات', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: ProductsScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('المنتجات'), findsOneWidget);
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });

      testWidgets('اختبار شاشة الفواتير', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: InvoicesScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('الفواتير'), findsOneWidget);
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });

      testWidgets('اختبار شاشة التحصيلات', (WidgetTester tester) async {
        await tester.pumpWidget(const MaterialApp(home: CollectionsScreen()));

        // التحقق من وجود عناصر الواجهة
        expect(find.text('التحصيلات'), findsOneWidget);
        expect(find.byType(FloatingActionButton), findsOneWidget);
      });
    });

    group('اختبارات الأداء', () {
      test('اختبار سرعة تحميل البيانات', () async {
        final stopwatch = Stopwatch()..start();
        
        await dbHelper.getDashboardStats();
        await dbHelper.getUsersStats();
        await dbHelper.getCustomers();
        await dbHelper.getProducts();
        await dbHelper.getAllInvoices();
        await dbHelper.getAllCollections();
        
        stopwatch.stop();
        
        // يجب أن يكون وقت التحميل أقل من 5 ثواني
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      test('اختبار سرعة إنشاء نسخة احتياطية', () async {
        final stopwatch = Stopwatch()..start();
        
        await BackupService().createFullBackup();
        
        stopwatch.stop();
        
        // يجب أن يكون وقت النسخ الاحتياطي أقل من 10 ثواني
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });

      test('اختبار سرعة تصدير البيانات', () async {
        final stopwatch = Stopwatch()..start();
        
        await DataExportService.exportAllDataToExcel();
        
        stopwatch.stop();
        
        // يجب أن يكون وقت التصدير أقل من 15 ثانية
        expect(stopwatch.elapsedMilliseconds, lessThan(15000));
      });
    });

    group('اختبارات الأمان', () {
      test('اختبار تشفير كلمات المرور', () async {
        const password = 'كلمة مرور قوية';
        final hashedPassword = await AuthService.hashPassword(password);
        
        expect(hashedPassword, isNot(equals(password)));
        expect(hashedPassword.length, greaterThan(password.length));
      });

      test('اختبار التحقق من كلمة المرور', () async {
        const password = 'كلمة مرور أخرى';
        final hashedPassword = await AuthService.hashPassword(password);
        final isValid = await AuthService.verifyPassword(password, hashedPassword);
        
        expect(isValid, isTrue);
      });
    });

    group('اختبارات التكامل', () {
      test('اختبار دورة حياة كاملة للفاتورة', () async {
        // 1. إنشاء عميل
        final customerId = await dbHelper.insertCustomer({
          'name': 'عميل تكامل',
          'primary_phone': '0444444444',
          'address': 'عنوان التكامل',
          'governorate': 'المنوفية',
          'area': 'شبين الكوم',
        });

        // 2. إنشاء منتج
        final productId = await dbHelper.insertProduct({
          'name': 'منتج تكامل',
          'price': 200.0,
          'description': 'وصف التكامل',
        });

        // 3. إنشاء فاتورة
        final invoiceId = await dbHelper.insertInvoice({
          'customer_id': customerId,
          'invoice_number': 'INV-INT-001',
          'date': DateTime.now().toString().substring(0, 10),
          'total_amount': 400.0,
          'paid_amount': 200.0,
          'remaining_amount': 200.0,
          'notes': 'ملاحظات التكامل',
          'created_by': 1,
        });

        // 4. إنشاء تحصيل
        final collectionId = await dbHelper.insertCollection({
          'customer_id': customerId,
          'amount': 150.0,
          'date': DateTime.now().toString().substring(0, 10),
          'payment_method': 'فودافون كاش',
          'collector_id': 1,
          'notes': 'تحصيل التكامل',
        });

        // 5. التحقق من البيانات
        final customer = await dbHelper.getCustomerById(customerId);
        final product = await dbHelper.getProductById(productId);
        final invoice = await dbHelper.getInvoiceById(invoiceId);
        final collection = await dbHelper.getCollectionById(collectionId);

        expect(customer, isNotNull);
        expect(product, isNotNull);
        expect(invoice, isNotNull);
        expect(collection, isNotNull);

        expect(customer['name'], equals('عميل تكامل'));
        expect(product['name'], equals('منتج تكامل'));
        expect(invoice['invoice_number'], equals('INV-INT-001'));
        expect(collection['payment_method'], equals('فودافون كاش'));
      });
    });

    group('اختبارات الأخطاء', () {
      test('اختبار معالجة الأخطاء في قاعدة البيانات', () async {
        try {
          // محاولة إدخال بيانات غير صحيحة
          await dbHelper.insertCustomer({});
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });

      test('اختبار معالجة الأخطاء في المصادقة', () async {
        final isLoggedIn = await AuthService.login('رقم غير موجود', 'كلمة مرور خاطئة');
        expect(isLoggedIn, isFalse);
      });
    });

    group('اختبارات التوافق', () {
      test('اختبار توافق البيانات', () async {
        // إنشاء بيانات متنوعة
        await dbHelper.insertCustomer({
          'name': 'عميل عربي',
          'primary_phone': '0555555555',
          'address': 'عنوان باللغة العربية',
          'governorate': 'طنطا',
          'area': 'وسط المدينة',
        });

        await dbHelper.insertProduct({
          'name': 'منتج باللغة العربية',
          'price': 150.0,
          'description': 'وصف باللغة العربية مع رموز: @#$%',
        });

        // التحقق من حفظ واسترجاع البيانات العربية
        final customers = await dbHelper.getCustomers();
        final products = await dbHelper.getProducts();

        final arabicCustomer = customers.firstWhere(
          (c) => c['name'] == 'عميل عربي',
          orElse: () => {},
        );

        final arabicProduct = products.firstWhere(
          (p) => p['name'] == 'منتج باللغة العربية',
          orElse: () => {},
        );

        expect(arabicCustomer.isNotEmpty, isTrue);
        expect(arabicProduct.isNotEmpty, isTrue);
        expect(arabicCustomer['name'], equals('عميل عربي'));
        expect(arabicProduct['name'], equals('منتج باللغة العربية'));
      });
    });
  });
} 