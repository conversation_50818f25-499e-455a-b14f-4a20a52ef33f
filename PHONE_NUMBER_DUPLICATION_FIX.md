# إصلاح مشكلة تكرار أرقام الهواتف

## المشكلة المبلغ عنها
عند إضافة رقم هاتف في العملاء، يظهر رسالة "الرقم موجود في قاعدة البيانات" حتى لو كان الرقم جديداً.

## سبب المشكلة
1. **تضارب في قاعدة البيانات**: كان هناك عمود `phone` في جدول `customers` وجدول منفصل `customer_phones` للهواتف المتعددة.
2. **دالة التحقق المعيبة**: دالة `getCustomerByPhone` كانت تبحث في كلا الجدولين مما يسبب تضارباً.
3. **منطق التحقق القديم**: كان يعتمد على حقل هاتف واحد بدلاً من قائمة الهواتف المتعددة.

## الحلول المطبقة

### 1. تحديث قاعدة البيانات (database_helper.dart)

#### أ. زيادة إصدار قاعدة البيانات
```dart
// من الإصدار 11 إلى 12
version: 12,
```

#### ب. حذف عمود `phone` من جدول `customers`
```dart
// في دالة _onCreate
CREATE TABLE customers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  // phone TEXT, // تم حذفه
  address TEXT,
  notes TEXT,
  governorate TEXT,
  area TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
)
```

#### ج. إضافة خطوة التحديث في `_onUpgrade`
```dart
if (oldVersion < 12) {
  // حذف عمود 'phone' من جدول 'customers'
  try {
    // إعادة تسمية الجدول القديم
    await db.execute('ALTER TABLE customers RENAME TO customers_old');

    // إنشاء الجدول الجديد بدون عمود 'phone'
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        notes TEXT,
        governorate TEXT,
        area TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // نسخ البيانات من الجدول القديم
    await db.execute('''
      INSERT INTO customers (id, name, address, notes, governorate, area, created_at)
      SELECT id, name, address, notes, governorate, area, created_at
      FROM customers_old
    ''');

    // حذف الجدول القديم
    await db.execute('DROP TABLE customers_old');
    print('✅ تم حذف عمود الهاتف من جدول العملاء بنجاح');
  } catch (e) {
    print('⚠️ خطأ في حذف عمود الهاتف من جدول العملاء: $e');
  }
}
```

#### د. تحديث دالة `getCustomerByPhone`
```dart
// من البحث في كلا الجدولين إلى البحث في جدول الهواتف فقط
Future<Map<String, dynamic>?> getCustomerByPhone(String phone) async {
  final db = await database;
  final result = await db.rawQuery(
    '''
    SELECT c.*, cp.phone as primary_phone
    FROM customers c
    INNER JOIN customer_phones cp ON c.id = cp.customer_id
    WHERE cp.phone = ?
    LIMIT 1
  ''',
    [phone],
  );
  return result.isNotEmpty ? result.first : null;
}
```

### 2. تحديث واجهة إضافة/تعديل العملاء (add_edit_customer_screen.dart)

#### أ. حذف `_phoneController`
```dart
// تم حذف
final _phoneController = TextEditingController();
```

#### ب. إضافة دالة تحميل الهواتف الموجودة
```dart
Future<void> _loadCustomerPhones(int customerId) async {
  try {
    final phonesData = await DatabaseHelper().getCustomerPhones(customerId);
    setState(() {
      _customerPhones = phonesData.map((data) => CustomerPhone.fromMap(data)).toList();
    });
  } catch (e) {
    print('خطأ في تحميل أرقام هواتف العميل: $e');
  }
}
```

#### ج. تحديث منطق التحقق من الهواتف
```dart
// التحقق الجديد من تكرار أرقام الهواتف
for (final phoneEntry in _customerPhones) {
  final existingCustomerWithPhone = await dbHelper.getCustomerByPhone(phoneEntry.phone);
  if (existingCustomerWithPhone != null) {
    // إذا كان التعديل، اسمح إذا كان الهاتف ينتمي للعميل الحالي
    if (widget.customer != null && existingCustomerWithPhone['id'] == widget.customer!['id']) {
      // هذا الهاتف ينتمي للعميل الحالي، لذا فهو مقبول.
      continue;
    }
    // وإلا فهو ينتمي لعميل آخر أو مكرر لعميل جديد
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('رقم الهاتف ${phoneEntry.phone} موجود بالفعل لعميل آخر.'),
          backgroundColor: Colors.red,
        ),
      );
    }
    setState(() {
      _isLoading = false;
    });
    return;
  }
}
```

#### د. تحديث منطق حفظ العميل
```dart
// إزالة حقل phone من customerData
final customerData = {
  'name': name,
  'address': _addressController.text.trim(),
  'notes': _notesController.text.trim(),
  'governorate': governorate,
  'area': _selectedArea,
};

// عند التحديث، حذف جميع الهواتف القديمة وإعادة إدراج الجديدة
if (widget.customer != null) {
  customerId = widget.customer!['id'];
  await dbHelper.updateCustomer(customerId, customerData);

  // حذف جميع الهواتف الموجودة لهذا العميل
  final existingPhones = await dbHelper.getCustomerPhones(customerId);
  for (final existingPhone in existingPhones) {
    await dbHelper.deleteCustomerPhone(existingPhone['id']);
  }

  // إدراج جميع الهواتف من قائمة _customerPhones
  for (final phone in _customerPhones) {
    final phoneData = {
      'customer_id': customerId,
      'phone': phone.phone,
      'phone_type': phone.phoneType,
      'is_primary': phone.isPrimary ? 1 : 0,
      'notes': phone.notes,
    };
    await dbHelper.insertCustomerPhone(phoneData);
  }
}
```

## النتائج المتوقعة

1. **حل مشكلة التكرار**: لن تظهر رسالة "الرقم موجود في قاعدة البيانات" إلا إذا كان الرقم مستخدماً فعلاً من عميل آخر.
2. **دعم الهواتف المتعددة**: يمكن إضافة أكثر من رقم هاتف لكل عميل.
3. **تحديث سلس**: عند تعديل عميل، يمكن تحديث هواتفه بسهولة.
4. **تناسق البيانات**: جدول `customer_phones` هو المصدر الوحيد لأرقام الهواتف.

## كيفية الاختبار

1. **إضافة عميل جديد**:
   - أضف عميل جديد مع رقم هاتف
   - تأكد من عدم ظهور رسالة التكرار

2. **إضافة عميل آخر بنفس الرقم**:
   - أضف عميل آخر بنفس رقم الهاتف
   - يجب أن تظهر رسالة التكرار

3. **تعديل عميل موجود**:
   - عدل عميل موجود وأضف أرقام هواتف جديدة
   - تأكد من حفظ التغييرات بنجاح

4. **إضافة هواتف متعددة**:
   - أضف أكثر من رقم هاتف لعميل واحد
   - تأكد من عمل جميع الأرقام

## ملاحظات مهمة

- **نسخة احتياطية**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات قبل التطبيق.
- **تحديث التطبيق**: سيتم تحديث قاعدة البيانات تلقائياً عند تشغيل التطبيق.
- **البيانات الموجودة**: سيتم نقل أرقام الهواتف الموجودة إلى الجدول الجديد تلقائياً. 