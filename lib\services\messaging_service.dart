import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';

class MessagingService {
  // إرسال تفاصيل الفاتورة عبر WhatsApp
  static Future<bool> sendInvoiceViaWhatsApp({
    required String customerPhone,
    required String customerName,
    required String invoiceNumber,
    required double totalAmount,
    required double paidAmount,
    required double remainingAmount,
    required String invoiceDate,
    required List<Map<String, dynamic>> invoiceItems,
    int? invoiceId,
  }) async {
    try {
      // تنسيق رسالة الفاتورة
      final message = await _formatInvoiceMessage(
        customerName: customerName,
        invoiceNumber: invoiceNumber,
        totalAmount: totalAmount,
        paidAmount: paidAmount,
        remainingAmount: remainingAmount,
        invoiceDate: invoiceDate,
        invoiceItems: invoiceItems,
        invoiceId: invoiceId,
      );

      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(customerPhone);

      // إنشاء رابط WhatsApp
      final whatsappUrl =
          'https://wa.me/$cleanPhone?text=${Uri.encodeComponent(message)}';

      // فتح WhatsApp
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(
          Uri.parse(whatsappUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح WhatsApp');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال الفاتورة عبر WhatsApp: $e');
      return false;
    }
  }

  // إرسال تفاصيل الفاتورة عبر الرسائل النصية
  static Future<bool> sendInvoiceViaSMS({
    required String customerPhone,
    required String customerName,
    required String invoiceNumber,
    required double totalAmount,
    required double paidAmount,
    required double remainingAmount,
    required String invoiceDate,
    required List<Map<String, dynamic>> invoiceItems,
    int? invoiceId,
  }) async {
    try {
      // تنسيق رسالة الفاتورة
      final message = await _formatInvoiceMessage(
        customerName: customerName,
        invoiceNumber: invoiceNumber,
        totalAmount: totalAmount,
        paidAmount: paidAmount,
        remainingAmount: remainingAmount,
        invoiceDate: invoiceDate,
        invoiceItems: invoiceItems,
        invoiceId: invoiceId,
      );

      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(customerPhone);

      // إنشاء رابط SMS
      final smsUrl = 'sms:$cleanPhone?body=${Uri.encodeComponent(message)}';

      // فتح تطبيق الرسائل
      if (await canLaunchUrl(Uri.parse(smsUrl))) {
        await launchUrl(
          Uri.parse(smsUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح تطبيق الرسائل');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال الفاتورة عبر الرسائل النصية: $e');
      return false;
    }
  }

  // إرسال رسالة مخصصة عبر WhatsApp
  static Future<bool> sendCustomMessage({
    required String phone,
    required String message,
    String title = 'رسالة من أطلس',
  }) async {
    try {
      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(phone);

      // إضافة العنوان للرسالة
      final fullMessage = '$title\n\n$message';

      // إنشاء رابط WhatsApp
      final whatsappUrl =
          'https://wa.me/$cleanPhone?text=${Uri.encodeComponent(fullMessage)}';

      // فتح WhatsApp
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(
          Uri.parse(whatsappUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح WhatsApp');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال الرسالة المخصصة عبر WhatsApp: $e');
      return false;
    }
  }

  // إرسال رسالة مخصصة عبر الرسائل النصية
  static Future<bool> sendCustomSMS({
    required String phone,
    required String message,
  }) async {
    try {
      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(phone);

      // إنشاء رابط SMS
      final smsUrl = 'sms:$cleanPhone?body=${Uri.encodeComponent(message)}';

      // فتح تطبيق الرسائل
      if (await canLaunchUrl(Uri.parse(smsUrl))) {
        await launchUrl(
          Uri.parse(smsUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح تطبيق الرسائل');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال الرسالة المخصصة عبر الرسائل النصية: $e');
      return false;
    }
  }

  // إرسال تنبيه تحصيل عبر WhatsApp
  static Future<bool> sendCollectionAlertViaWhatsApp({
    required String customerPhone,
    required String customerName,
    required double amount,
    required String collectionDate,
    required String collectorName,
    String? notes,
  }) async {
    try {
      // تنسيق رسالة التحصيل
      final message = _formatCollectionMessage(
        customerName: customerName,
        amount: amount,
        collectionDate: collectionDate,
        collectorName: collectorName,
        notes: notes,
      );

      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(customerPhone);

      // إنشاء رابط WhatsApp
      final whatsappUrl =
          'https://wa.me/$cleanPhone?text=${Uri.encodeComponent(message)}';

      // فتح WhatsApp
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(
          Uri.parse(whatsappUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح WhatsApp');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال تنبيه التحصيل عبر WhatsApp: $e');
      return false;
    }
  }

  // إرسال تنبيه تحصيل عبر الرسائل النصية
  static Future<bool> sendCollectionAlertViaSMS({
    required String customerPhone,
    required String customerName,
    required double amount,
    required String collectionDate,
    required String collectorName,
    String? notes,
  }) async {
    try {
      // تنسيق رسالة التحصيل
      final message = _formatCollectionMessage(
        customerName: customerName,
        amount: amount,
        collectionDate: collectionDate,
        collectorName: collectorName,
        notes: notes,
      );

      // إزالة الرموز من رقم الهاتف
      final cleanPhone = _cleanPhoneNumber(customerPhone);

      // إنشاء رابط SMS
      final smsUrl = 'sms:$cleanPhone?body=${Uri.encodeComponent(message)}';

      // فتح تطبيق الرسائل
      if (await canLaunchUrl(Uri.parse(smsUrl))) {
        await launchUrl(
          Uri.parse(smsUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        print('❌ لا يمكن فتح تطبيق الرسائل');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في إرسال تنبيه التحصيل عبر الرسائل النصية: $e');
      return false;
    }
  }

  // تنسيق رسالة الفاتورة
  static Future<String> _formatInvoiceMessage({
    required String customerName,
    required String invoiceNumber,
    required double totalAmount,
    required double paidAmount,
    required double remainingAmount,
    required String invoiceDate,
    required List<Map<String, dynamic>> invoiceItems,
    int? invoiceId,
  }) async {
    final formatter = NumberFormat('#,##0.00', 'ar_EG');

    String message =
        '''
أطلس للمستلزمات الطبية
تفاصيل الفاتورة

العميل: $customerName
رقم الفاتورة: $invoiceNumber
تاريخ الفاتورة: $invoiceDate

المنتجات:
''';

    // إضافة المنتجات
    for (var item in invoiceItems) {
      final itemName = item['name'] ?? 'منتج غير محدد';
      final quantity = item['quantity'] ?? 1;
      final price = item['price'] ?? 0.0;
      final total = quantity * price;

      message +=
          '''
- $itemName
  الكمية: $quantity × ${formatter.format(price)} = ${formatter.format(total)} ج.م
''';
    }

    message +=
        '''

الإجماليات:
المبلغ الإجمالي: ${formatter.format(totalAmount)} ج.م
المبلغ المدفوع: ${formatter.format(paidAmount)} ج.م
المبلغ المتبقي: ${formatter.format(remainingAmount)} ج.م
''';

    // إضافة تفاصيل التحصيلات إذا كان معرف الفاتورة متوفر
    if (invoiceId != null) {
      try {
        final dbHelper = DatabaseHelper();
        final collections = await dbHelper.getCollectionsByInvoice(invoiceId);

        if (collections.isNotEmpty) {
          message += '''

تفاصيل التحصيلات:
''';

          for (var collection in collections) {
            final collectionDate = collection['date'] ?? 'غير محدد';
            final collectionAmount = collection['amount'] ?? 0.0;
            final collectorName = collection['collector_name'] ?? 'غير محدد';
            final paymentMethod = collection['payment_method'] ?? 'نقداً';
            final notes = collection['notes'];

            message +=
                '''
📅 ${collectionDate}
💰 المبلغ: ${formatter.format(collectionAmount)} ج.م
👤 المحصل: $collectorName
💳 طريقة الدفع: $paymentMethod
''';

            if (notes != null && notes.toString().isNotEmpty) {
              message += '''📝 ملاحظات: $notes
''';
            }
            message += '''
''';
          }
        }
      } catch (e) {
        print('❌ خطأ في جلب تفاصيل التحصيلات: $e');
        // لا نضيف أي شيء للرسالة في حالة الخطأ
      }
    }

    message += '''

شكراً لتعاملكم معنا
للاستفسار: 01125312343
''';

    return message;
  }

  // تنسيق رسالة التحصيل
  static String _formatCollectionMessage({
    required String customerName,
    required double amount,
    required String collectionDate,
    required String collectorName,
    String? notes,
  }) {
    final formatter = NumberFormat('#,##0.00', 'ar_EG');

    String message =
        '''
أطلس للمستلزمات الطبية
تنبيه تحصيل

العميل: $customerName
المبلغ المحصل: ${formatter.format(amount)} ج.م
تاريخ التحصيل: $collectionDate
المحصل: $collectorName
''';

    if (notes != null && notes.isNotEmpty) {
      message +=
          '''
ملاحظات: $notes
''';
    }

    message += '''

شكراً لتعاملكم معنا
للاستفسار: 01125312343
''';

    return message;
  }

  // تنظيف رقم الهاتف - إلغاء تفعيل كود الدولة
  static String _cleanPhoneNumber(String phone) {
    // إزالة جميع الرموز والمسافات فقط (بدون تعديل الرقم)
    String clean = phone.replaceAll(RegExp(r'[^\d]'), '');

    print('🔍 تنظيف رقم الهاتف: "$phone" -> "$clean"');

    // إذا كان الرقم فارغاً، إرجاع رقم افتراضي
    if (clean.isEmpty) {
      print('⚠️ رقم الهاتف فارغ، استخدام رقم افتراضي');
      return '201125312343'; // رقم افتراضي للاختبار
    }

    // إلغاء تفعيل كود الدولة - إرجاع الرقم كما هو بعد إزالة الرموز فقط
    print('✅ الرقم النهائي (بدون تعديل): "$clean"');
    return clean;
  }

  // التحقق من وجود WhatsApp
  static Future<bool> isWhatsAppInstalled() async {
    try {
      final whatsappUrl = 'whatsapp://send?phone=1234567890';
      return await canLaunchUrl(Uri.parse(whatsappUrl));
    } catch (e) {
      return false;
    }
  }

  // التحقق من إمكانية إرسال الرسائل النصية
  static Future<bool> canSendSMS() async {
    try {
      final status = await Permission.sms.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      return false;
    }
  }

  // طلب أذونات الإرسال
  static Future<Map<String, bool>> requestPermissions() async {
    try {
      final smsStatus = await Permission.sms.request();
      final phoneStatus = await Permission.phone.request();

      return {
        'sms': smsStatus == PermissionStatus.granted,
        'phone': phoneStatus == PermissionStatus.granted,
      };
    } catch (e) {
      print('❌ خطأ في طلب الأذونات: $e');
      return {'sms': false, 'phone': false};
    }
  }

  // اختبار إرسال رسالة نصية
  static Future<bool> testSMS(String phoneNumber) async {
    try {
      final testMessage =
          '''
أطلس للمستلزمات الطبية
رسالة اختبار

هذه رسالة اختبار للتأكد من عمل نظام الرسائل النصية.
التاريخ: ${DateTime.now().toString().substring(0, 10)}

شكراً لتعاملكم معنا
للاستفسار: 01125312343
''';

      return await sendCustomSMS(phone: phoneNumber, message: testMessage);
    } catch (e) {
      print('❌ خطأ في اختبار الرسالة النصية: $e');
      return false;
    }
  }

  // الحصول على معلومات الرقم المنسق
  static String getFormattedPhoneInfo(String phoneNumber) {
    final original = phoneNumber;
    final cleaned = _cleanPhoneNumber(phoneNumber);

    return '''
معلومات الرقم:
الرقم الأصلي: $original
الرقم المنسق: $cleaned
ملاحظة: تم إلغاء تفعيل كود الدولة - الرقم كما هو مسجل
''';
  }
}
