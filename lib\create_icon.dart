import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

void main() async {
  // Create a simple icon
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // Set background
  final paint = Paint()..color = const Color(0xFF4A90E2);
  canvas.drawRect(Rect.fromLTWH(0, 0, 1024, 1024), paint);
  
  // Draw a simple "A" letter
  final textPainter = TextPainter(
    text: TextSpan(
      text: 'A',
      style: TextStyle(
        color: Colors.white,
        fontSize: 400,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();
  textPainter.paint(canvas, Offset(
    (1024 - textPainter.width) / 2,
    (1024 - textPainter.height) / 2,
  ));
  
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  final bytes = byteData!.buffer.asUint8List();
  
  // Save the icon
  final file = File('assets/images/atlas_icon_simple.png');
  await file.writeAsBytes(bytes);
  
  print('Icon created successfully!');
} 