import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import 'permissions_service.dart';

class AuthService {
  static const String _keyUserId = 'user_id';
  static const String _keyUserName = 'user_name';
  static const String _keyUserPhone = 'user_phone';
  static const String _keyUserRole = 'user_role';
  static const String _keyIsLoggedIn = 'is_logged_in';

  static final DatabaseHelper _dbHelper = DatabaseHelper();

  // تسجيل الدخول
  static Future<Map<String, dynamic>?> login(
    String phone,
    String password,
  ) async {
    try {
      // تسجيل الدخول من قاعدة البيانات المحلية
      final user = await _dbHelper.getUserByPhone(phone);

      if (user == null) {
        return {'success': false, 'message': 'رقم الهاتف غير موجود'};
      }

      if (user['password'] != password) {
        return {'success': false, 'message': 'كلمة المرور غير صحيحة'};
      }

      // حفظ بيانات المستخدم
      await _saveUserData(user);

      return {
        'success': true,
        'message': 'تم تسجيل الدخول بنجاح',
        'user': user,
      };
    } catch (e) {
      return {'success': false, 'message': 'حدث خطأ أثناء تسجيل الدخول'};
    }
  }

  // مزامنة بيانات المستخدم مع قاعدة البيانات المحلية (محفوظة للمستقبل)
  static Future<void> _syncUserToLocal(Map<String, dynamic> userData) async {
    try {
      // التحقق من وجود المستخدم محلياً
      final localUser = await _dbHelper.getUserByPhone(userData['phone']);
      
      if (localUser == null) {
        // إضافة المستخدم محلياً إذا لم يكن موجوداً
        await _dbHelper.insertUser({
          'name': userData['name'],
          'phone': userData['phone'],
          'password': userData['password'] ?? '123456', // كلمة مرور افتراضية
          'role': userData['role'] ?? 'مستخدم',
          'permissions': userData['permissions'] ?? '',
          'is_active': userData['is_active'] ?? 1,
        });
      }
    } catch (e) {
      print('Error syncing user to local: $e');
    }
  }

  // إنشاء مستخدم افتراضي إذا لم يكن موجود
  static Future<void> createDefaultUser() async {
    try {
      // التحقق من وجود مستخدمين في قاعدة البيانات المحلية
      final users = await _dbHelper.getUsers();
      
      if (users.isEmpty) {
        // إنشاء مستخدم افتراضي
        final defaultUser = {
          'name': 'مدير النظام',
          'phone': 'admin',
          'password': '123456',
          'role': 'مدير',
          'permissions': PermissionsService.getDefaultPermissionsByRole('مدير'),
          'is_active': 1,
        };
        
        await _dbHelper.insertUser(defaultUser);
        print('✅ تم إنشاء المستخدم الافتراضي بنجاح');
      }
    } catch (e) {
      print('❌ خطأ في إنشاء المستخدم الافتراضي: $e');
    }
  }

  // مزامنة جميع المستخدمين من Firebase إلى قاعدة البيانات المحلية (محفوظة للمستقبل)
  static Future<void> syncUsersFromFirebase() async {
    try {
      print('🔄 مزامنة المستخدمين من Firebase...');
      print('ℹ️ Firebase سيتم تفعيله قريباً');
      
      // TODO: سيتم تفعيل Firebase قريباً
      // final firebaseUsers = await FirebaseService.getUsers();
      
      print('✅ تمت مزامنة المستخدمين بنجاح');
    } catch (e) {
      print('❌ خطأ في مزامنة المستخدمين: $e');
    }
  }

  // حفظ بيانات المستخدم في SharedPreferences
  static Future<void> _saveUserData(Map<String, dynamic> user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyUserId, user['id']);
    await prefs.setString(_keyUserName, user['name']);
    await prefs.setString(_keyUserPhone, user['phone']);
    await prefs.setString(_keyUserRole, user['role']);
    await prefs.setBool(_keyIsLoggedIn, true);
  }

  // التحقق من حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  // الحصول على بيانات المستخدم الحالي
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;

    if (!isLoggedIn) return null;

    return {
      'id': prefs.getInt(_keyUserId),
      'name': prefs.getString(_keyUserName),
      'phone': prefs.getString(_keyUserPhone),
      'role': prefs.getString(_keyUserRole),
    };
  }

  // الحصول على معرف المستخدم الحالي
  static Future<int?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyUserId);
  }

  // الحصول على اسم المستخدم الحالي
  static Future<String?> getCurrentUserName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserName);
  }

  // الحصول على دور المستخدم الحالي
  static Future<String?> getCurrentUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyUserRole);
  }

  // التحقق من صلاحيات المدير
  static Future<bool> isAdmin() async {
    final role = await getCurrentUserRole();
    return role == 'مدير';
  }

  // التحقق من صلاحية معينة للمستخدم الحالي
  static Future<bool> hasPermission(String permission) async {
    final userId = await getCurrentUserId();
    if (userId == null) return false;

    // المدير له جميع الصلاحيات
    final isAdminUser = await isAdmin();
    if (isAdminUser) return true;

    return await PermissionsService.hasPermission(userId, permission);
  }

  // التحقق من وجود أي من الصلاحيات المطلوبة
  static Future<bool> hasAnyPermission(List<String> permissions) async {
    final userId = await getCurrentUserId();
    if (userId == null) return false;

    // المدير له جميع الصلاحيات
    final isAdminUser = await isAdmin();
    if (isAdminUser) return true;

    return await PermissionsService.hasAnyPermission(userId, permissions);
  }

  // التحقق من وجود جميع الصلاحيات المطلوبة
  static Future<bool> hasAllPermissions(List<String> permissions) async {
    final userId = await getCurrentUserId();
    if (userId == null) return false;

    // المدير له جميع الصلاحيات
    final isAdminUser = await isAdmin();
    if (isAdminUser) return true;

    return await PermissionsService.hasAllPermissions(userId, permissions);
  }

  // جلب صلاحيات المستخدم الحالي
  static Future<List<String>> getCurrentUserPermissions() async {
    final userId = await getCurrentUserId();
    if (userId == null) return [];

    return await PermissionsService.getUserPermissions(userId);
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyUserId);
    await prefs.remove(_keyUserName);
    await prefs.remove(_keyUserPhone);
    await prefs.remove(_keyUserRole);
    await prefs.setBool(_keyIsLoggedIn, false);
  }

  // إعادة تهيئة قاعدة البيانات
  static Future<void> resetDatabase() async {
    await _dbHelper.resetDatabase();
  }

  // تغيير كلمة المرور
  static Future<Map<String, dynamic>> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        return {'success': false, 'message': 'المستخدم غير مسجل الدخول'};
      }

      final user = await _dbHelper.getUserByEmail(currentUser['email']);
      if (user == null || user['password'] != currentPassword) {
        return {'success': false, 'message': 'كلمة المرور الحالية غير صحيحة'};
      }

      await _dbHelper.updateUser(currentUser['id'], {'password': newPassword});

      return {'success': true, 'message': 'تم تغيير كلمة المرور بنجاح'};
    } catch (e) {
      return {'success': false, 'message': 'حدث خطأ أثناء تغيير كلمة المرور'};
    }
  }
}
