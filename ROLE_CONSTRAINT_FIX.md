# 🔧 إصلاح قيود الدور في قاعدة البيانات

## 📋 المشكلة

كانت هناك مشكلة في قاعدة البيانات عند محاولة إضافة مستخدم بدور "بائع" (Seller). الخطأ كان:

```
DatabaseException(CHECK :constraint failed: users (code 275 SQLITE_CONSTRAINT_CHECK))
```

## 🔍 سبب المشكلة

كان جدول المستخدمين يحتوي على قيد CHECK يسمح فقط بالأدوار التالية:
- 'مدير' (Manager)
- 'مستخدم' (User)
- 'مبيعات' (Sales)
- 'محاسب' (Accountant)

لكن التطبيق كان يحاول إضافة مستخدم بدور "بائع" (Seller) الذي لم يكن مدرجاً في القيود.

## ✅ الحل المطبق

### 1. تحديث قاعدة البيانات
- تم زيادة إصدار قاعدة البيانات من 3 إلى 4
- تم تحديث قيد CHECK ليشمل "بائع":

```sql
role TEXT NOT NULL CHECK (role IN ('مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع'))
```

### 2. إضافة migration script
تم إضافة migration script في `_onUpgrade` لإعادة إنشاء جدول المستخدمين مع القيود الجديدة:

```dart
if (oldVersion < 4) {
  // إعادة إنشاء جدول المستخدمين مع القيود الجديدة
  await db.execute('''
    CREATE TABLE users_new (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع')),
      permissions TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
  ''');
  
  // نسخ البيانات من الجدول القديم
  await db.execute('''
    INSERT INTO users_new (id, name, phone, password, role, permissions, is_active, created_at)
    SELECT id, name, phone, password, role, permissions, is_active, created_at
    FROM users
  ''');
  
  // حذف الجدول القديم وإعادة تسمية الجدول الجديد
  await db.execute('DROP TABLE users');
  await db.execute('ALTER TABLE users_new RENAME TO users');
}
```

### 3. تحديث واجهة المستخدم
تم تحديث قوائم الأدوار في:
- `users_screen.dart`
- `advanced_users_screen.dart`

```dart
final List<String> _roles = ['مدير', 'مستخدم', 'مبيعات', 'محاسب', 'بائع'];
```

### 4. تحديث خدمة الصلاحيات
تم إضافة الصلاحيات الافتراضية لدور "بائع" في `permissions_service.dart`:

```dart
case 'seller':
case 'بائع':
  return [
    'customers_view',
    'customers_add',
    'invoices_view',
    'invoices_add',
    'invoices_send',
    'collections_view',
    'collections_add',
    'dashboard_view',
  ];
```

## 🎯 النتيجة

الآن يمكن إضافة مستخدمين بدور "بائع" بدون أي أخطاء في قاعدة البيانات.

## 📝 الأدوار المتاحة الآن

1. **مدير** - جميع الصلاحيات
2. **مستخدم** - صلاحيات أساسية
3. **مبيعات** - صلاحيات المبيعات
4. **محاسب** - صلاحيات المحاسبة
5. **بائع** - صلاحيات البيع والتحصيل

## 🔄 كيفية تطبيق التحديث

1. قم بتشغيل التطبيق
2. سيتم تحديث قاعدة البيانات تلقائياً عند فتح التطبيق
3. يمكن الآن إضافة مستخدمين بدور "بائع"

## ⚠️ ملاحظات مهمة

- التحديث يحافظ على جميع البيانات الموجودة
- لا توجد حاجة لحذف قاعدة البيانات أو إعادة تثبيت التطبيق
- التحديث يتم تلقائياً عند تشغيل التطبيق 