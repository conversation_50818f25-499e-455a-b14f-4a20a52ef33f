# إنشاء أيقونات التشغيل - أطلس للمستلزمات الطبية
# PowerShell Script

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   إنشاء أيقونات التشغيل" -ForegroundColor Yellow
Write-Host "   أطلس للمستلزمات الطبية" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# الحصول على المسار الحالي
$currentPath = Get-Location
Write-Host "المجلد الحالي: $currentPath" -ForegroundColor Green
Write-Host ""

Write-Host "جاري إنشاء أيقونات التشغيل..." -ForegroundColor Blue
Write-Host ""

# إنشاء اختصار التشغيل التفاعلي
$shortcut1 = New-Object -ComObject WScript.Shell
$link1 = $shortcut1.CreateShortcut("$currentPath\🚀 تشغيل التطبيق.lnk")
$link1.TargetPath = "$currentPath\run_app.bat"
$link1.WorkingDirectory = $currentPath
$link1.Description = "تشغيل تطبيق أطلس للمستلزمات الطبية"

# تحديد الأيقونة
$iconPath = "$currentPath\android\app\src\main\res\mipmap-hdpi\ic_launcher.png"
if (Test-Path $iconPath) {
    $link1.IconLocation = $iconPath
} else {
    $link1.IconLocation = "$env:SystemRoot\System32\shell32.dll,21"
}
$link1.Save()

# إنشاء اختصار التشغيل السريع
$shortcut2 = New-Object -ComObject WScript.Shell
$link2 = $shortcut2.CreateShortcut("$currentPath\⚡ تشغيل سريع.lnk")
$link2.TargetPath = "$currentPath\START.bat"
$link2.WorkingDirectory = $currentPath
$link2.Description = "تشغيل سريع للتطبيق على Chrome"

if (Test-Path $iconPath) {
    $link2.IconLocation = $iconPath
} else {
    $link2.IconLocation = "$env:SystemRoot\System32\shell32.dll,21"
}
$link2.Save()

Write-Host "✅ تم إنشاء أيقونات التشغيل بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "📁 الأيقونات المنشأة:" -ForegroundColor Yellow
Write-Host "   • 🚀 تشغيل التطبيق.lnk" -ForegroundColor White
Write-Host "   • ⚡ تشغيل سريع.lnk" -ForegroundColor White
Write-Host ""
Write-Host "💡 يمكنك الآن النقر على أي من الأيقونات لتشغيل التطبيق" -ForegroundColor Cyan
Write-Host ""

# عرض معلومات إضافية
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   معلومات إضافية:" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 تشغيل التطبيق.lnk" -ForegroundColor White
Write-Host "   - تشغيل تفاعلي مع خيارات متعددة" -ForegroundColor Gray
Write-Host "   - واجهة عربية جميلة" -ForegroundColor Gray
Write-Host "   - خيارات: ويب، Windows، Android" -ForegroundColor Gray
Write-Host ""
Write-Host "⚡ تشغيل سريع.lnk" -ForegroundColor White
Write-Host "   - تشغيل مباشر على Chrome" -ForegroundColor Gray
Write-Host "   - الأسرع والأسهل" -ForegroundColor Gray
Write-Host "   - مناسب للاستخدام اليومي" -ForegroundColor Gray
Write-Host ""

Read-Host "اضغط Enter للخروج" 