import 'package:flutter/material.dart';
import '../services/auth_service.dart';

class PermissionWrapper extends StatelessWidget {
  final Widget child;
  final String permission;
  final List<String> permissions;
  final Widget? fallbackWidget;
  final bool requireAllPermissions;

  const PermissionWrapper({
    super.key,
    required this.child,
    this.permission = '',
    this.permissions = const [],
    this.fallbackWidget,
    this.requireAllPermissions = false,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _checkPermission(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(color: Color(0xFF4A90E2)),
          );
        }

        final hasPermission = snapshot.data ?? false;

        if (hasPermission) {
          return child;
        }

        return fallbackWidget ?? _buildDefaultFallback();
      },
    );
  }

  Future<bool> _checkPermission() async {
    if (permission.isNotEmpty) {
      return await AuthService.hasPermission(permission);
    }

    if (permissions.isNotEmpty) {
      if (requireAllPermissions) {
        return await AuthService.hasAllPermissions(permissions);
      } else {
        return await AuthService.hasAnyPermission(permissions);
      }
    }

    return true; // إذا لم يتم تحديد صلاحيات، السماح بالوصول
  }

  Widget _buildDefaultFallback() {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      appBar: AppBar(
        title: const Text('غير مصرح'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'ليس لديك صلاحية للوصول لهذه الصفحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Widget مبسط للتحقق من صلاحية واحدة
class SinglePermissionWrapper extends StatelessWidget {
  final Widget child;
  final String permission;
  final Widget? fallbackWidget;

  const SinglePermissionWrapper({
    super.key,
    required this.child,
    required this.permission,
    this.fallbackWidget,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionWrapper(
      child: child,
      permission: permission,
      fallbackWidget: fallbackWidget,
    );
  }
}

// Widget للتحقق من وجود أي من الصلاحيات
class AnyPermissionWrapper extends StatelessWidget {
  final Widget child;
  final List<String> permissions;
  final Widget? fallbackWidget;

  const AnyPermissionWrapper({
    super.key,
    required this.child,
    required this.permissions,
    this.fallbackWidget,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionWrapper(
      child: child,
      permissions: permissions,
      fallbackWidget: fallbackWidget,
      requireAllPermissions: false,
    );
  }
}

// Widget للتحقق من وجود جميع الصلاحيات
class AllPermissionsWrapper extends StatelessWidget {
  final Widget child;
  final List<String> permissions;
  final Widget? fallbackWidget;

  const AllPermissionsWrapper({
    super.key,
    required this.child,
    required this.permissions,
    this.fallbackWidget,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionWrapper(
      child: child,
      permissions: permissions,
      fallbackWidget: fallbackWidget,
      requireAllPermissions: true,
    );
  }
}
