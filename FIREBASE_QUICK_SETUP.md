# 🔥 إعداد Firebase السريع

## 📋 الخطوات المطلوبة:

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. أدخل اسم المشروع: `atlas-medical-supplies`
4. اتبع الخطوات حتى يتم إنشاء المشروع

### 2. إضافة تطبيق Android
1. في Firebase Console، انقر على أيقونة Android
2. أدخل Package name: `com.atlas.medical.atlas_medical_supplies`
3. انقر "تسجيل التطبيق"
4. قم بتحميل ملف `google-services.json`
5. استبدل الملف الموجود في `android/app/google-services.json`

### 3. تفعيل Authentication
1. في Firebase Console، اذهب إلى "Authentication"
2. انقر على "Get started"
3. في "Sign-in method"، فعّل "Email/Password"
4. انقر "Save"

### 4. إنشاء Firestore Database
1. في Firebase Console، اذهب إلى "Firestore Database"
2. انقر على "Create database"
3. اختر "Start in test mode" (للاختبار)
4. اختر موقع قاعدة البيانات (مثل us-central1)

### 5. تحديث firebase_options.dart
استبدل القيم في `lib/firebase_options.dart` بالقيم الحقيقية من مشروعك:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'YOUR-ACTUAL-API-KEY',
  appId: 'YOUR-ACTUAL-APP-ID',
  messagingSenderId: 'YOUR-ACTUAL-SENDER-ID',
  projectId: 'atlas-medical-supplies',
  storageBucket: 'atlas-medical-supplies.appspot.com',
);
```

### 6. إنشاء المستخدم الأول
1. شغل التطبيق
2. استخدم بيانات تسجيل الدخول:
   - رقم الهاتف: `01125312343`
   - كلمة المرور: `123456`
3. سيتم إنشاء المستخدم الأول في Firebase

## ✅ بعد الإعداد:

- ✅ التطبيق سيعمل مع Firebase
- ✅ البيانات ستُحفظ في السحابة
- ✅ يمكن الوصول للبيانات من أي جهاز
- ✅ النسخ الاحتياطي التلقائي

## 🚨 ملاحظات مهمة:

1. **لا تشارك API keys** مع أي شخص
2. **غيّر قواعد الأمان** في Firestore قبل الإنتاج
3. **فعّل Authentication** للمستخدمين الجدد
4. **راقب الاستخدام** في Firebase Console

## 📞 للمساعدة:

إذا واجهت أي مشاكل، راجع:
- [Firebase Documentation](https://firebase.google.com/docs)
- [FlutterFire Documentation](https://firebase.flutter.dev/) 