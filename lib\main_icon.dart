import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:io';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await createAppIcon();
}

Future<void> createAppIcon() async {
  print('🎨 إنشاء أيقونة التطبيق...');
  
  // إنشاء مجلد الأصول إذا لم يكن موجوداً
  final assetsDir = Directory('assets/images');
  if (!await assetsDir.exists()) {
    await assetsDir.create(recursive: true);
    print('✅ تم إنشاء مجلد assets/images');
  }
  
  // إنشاء الأيقونة
  final iconData = await generateIcon();
  
  // حفظ الأيقونة
  final file = File('assets/images/atlas_icon_simple.png');
  await file.writeAsBytes(iconData);
  
  print('✅ تم إنشاء الأيقونة: ${file.path}');
  print('🎉 يمكنك الآن تشغيل: flutter pub run flutter_launcher_icons');
}

Future<Uint8List> generateIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = Size(1024, 1024);
  
  // رسم خلفية دائرية
  final paint = Paint()
    ..shader = RadialGradient(
      colors: [
        const Color(0xFF40E0D0),
        const Color(0xFF20B2AA),
        const Color(0xFF008B8B),
      ],
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
  
  canvas.drawCircle(
    Offset(size.width / 2, size.height / 2),
    size.width / 2,
    paint,
  );
  
  // رسم صليب طبي
  final crossPaint = Paint()
    ..color = Colors.white
    ..strokeWidth = 20
    ..strokeCap = StrokeCap.round;
  
  final center = Offset(size.width / 2, size.height / 2);
  const crossSize = 200.0;
  
  // خط عمودي
  canvas.drawLine(
    Offset(center.dx, center.dy - crossSize),
    Offset(center.dx, center.dy + crossSize),
    crossPaint,
  );
  
  // خط أفقي
  canvas.drawLine(
    Offset(center.dx - crossSize, center.dy),
    Offset(center.dx + crossSize, center.dy),
    crossPaint,
  );
  
  // إضافة نص ATLAS
  final textPainter = TextPainter(
    text: const TextSpan(
      text: 'ATLAS',
      style: TextStyle(
        color: Colors.white,
        fontSize: 120,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  
  textPainter.layout();
  textPainter.paint(
    canvas,
    Offset(
      center.dx - textPainter.width / 2,
      center.dy + crossSize + 50,
    ),
  );
  
  final picture = recorder.endRecording();
  final image = await picture.toImage(1024, 1024);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  return byteData!.buffer.asUint8List();
} 